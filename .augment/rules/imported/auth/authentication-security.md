---
type: "agent_requested"
---

# Authentication & Security

## Objective
Implement secure, robust authentication flows with proper error handling, validation, and user experience patterns.

## Context
- Firebase Authentication with JWT tokens
- Role-based access control (RBAC)
- Route guards and permission systems
- Zod validation for user inputs
- Secure session management

## Rules

### Authentication Flow Patterns
- **Login Implementation**: Secure login with proper error handling
  ```typescript
  // ✅ Good
  interface LoginCredentials {
    email: string;
    password: string;
  }
  
  const loginSchema = z.object({
    email: z.string().email('Invalid email address'),
    password: z.string().min(8, 'Password must be at least 8 characters'),
  });
  
  export const useLogin = () => {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    
    const login = async (credentials: LoginCredentials) => {
      try {
        setIsLoading(true);
        setError(null);
        
        // Validate input
        const validatedData = loginSchema.parse(credentials);
        
        // Firebase authentication
        const userCredential = await signInWithEmailAndPassword(
          auth,
          validatedData.email,
          validatedData.password
        );
        
        // Get custom claims for RBAC
        const idToken = await userCredential.user.getIdToken();
        const decodedToken = await auth.currentUser?.getIdTokenResult();
        
        return {
          user: userCredential.user,
          role: decodedToken?.claims.role as string,
          permissions: decodedToken?.claims.permissions as string[],
        };
      } catch (error) {
        const errorMessage = getAuthErrorMessage(error);
        setError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };
    
    return { login, isLoading, error };
  };
  ```

- **Registration Flow**: Secure user registration with validation
  ```typescript
  // ✅ Good
  interface RegisterData {
    email: string;
    password: string;
    confirmPassword: string;
    firstName: string;
    lastName: string;
  }
  
  const registerSchema = z.object({
    email: z.string().email('Invalid email address'),
    password: z.string()
      .min(8, 'Password must be at least 8 characters')
      .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one uppercase letter, one lowercase letter, and one number'),
    confirmPassword: z.string(),
    firstName: z.string().min(1, 'First name is required'),
    lastName: z.string().min(1, 'Last name is required'),
  }).refine(data => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });
  
  export const register = async (data: RegisterData) => {
    const validatedData = registerSchema.parse(data);
    
    const userCredential = await createUserWithEmailAndPassword(
      auth,
      validatedData.email,
      validatedData.password
    );
    
    // Update profile
    await updateProfile(userCredential.user, {
      displayName: `${validatedData.firstName} ${validatedData.lastName}`,
    });
    
    // Send verification email
    await sendEmailVerification(userCredential.user);
    
    return userCredential;
  };
  ```

### Auth Context Implementation
- **Context Provider**: Centralized authentication state management
  ```typescript
  // ✅ Good
  interface AuthContextType {
    user: User | null;
    loading: boolean;
    isAuthenticated: boolean;
    role: string | null;
    permissions: string[];
    login: (credentials: LoginCredentials) => Promise<void>;
    logout: () => Promise<void>;
    updateProfile: (data: Partial<UserProfile>) => Promise<void>;
  }
  
  const AuthContext = createContext<AuthContextType | undefined>(undefined);
  
  export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
    const [user, setUser] = useState<User | null>(null);
    const [loading, setLoading] = useState(true);
    const [role, setRole] = useState<string | null>(null);
    const [permissions, setPermissions] = useState<string[]>([]);
    
    useEffect(() => {
      const unsubscribe = auth.onAuthStateChanged(async (firebaseUser) => {
        if (firebaseUser) {
          try {
            // Get fresh token with custom claims
            const idTokenResult = await firebaseUser.getIdTokenResult(true);
            
            setUser(firebaseUser);
            setRole(idTokenResult.claims.role as string || 'user');
            setPermissions(idTokenResult.claims.permissions as string[] || []);
          } catch (error) {
            console.error('Error fetching user claims:', error);
            setUser(null);
            setRole(null);
            setPermissions([]);
          }
        } else {
          setUser(null);
          setRole(null);
          setPermissions([]);
        }
        setLoading(false);
      });
      
      return unsubscribe;
    }, []);
    
    const logout = async () => {
      try {
        await signOut(auth);
        // Clear any persisted data
        localStorage.removeItem('userPreferences');
      } catch (error) {
        console.error('Logout error:', error);
        throw error;
      }
    };
    
    const value = {
      user,
      loading,
      isAuthenticated: !!user,
      role,
      permissions,
      login: async (credentials: LoginCredentials) => {
        // Implementation handled by useLogin hook
      },
      logout,
      updateProfile: async (data: Partial<UserProfile>) => {
        // Profile update implementation
      },
    };
    
    return (
      <AuthContext.Provider value={value}>
        {children}
      </AuthContext.Provider>
    );
  };
  ```

### Route Guards
- **Authentication Guards**: Protect routes from unauthorized access
  ```typescript
  // ✅ Good
  interface AuthGuardProps {
    children: React.ReactNode;
    fallback?: React.ReactNode;
    requireEmailVerification?: boolean;
  }
  
  export const AuthGuard = ({ 
    children, 
    fallback,
    requireEmailVerification = false 
  }: AuthGuardProps) => {
    const { user, loading, isAuthenticated } = useAuthContext();
    const navigate = useNavigate();
    const location = useLocation();
    
    useEffect(() => {
      if (!loading && !isAuthenticated) {
        // Store intended destination for redirect after login
        navigate('/auth/login', {
          state: { from: location.pathname },
          replace: true,
        });
      }
    }, [loading, isAuthenticated, navigate, location]);
    
    if (loading) {
      return <LoadingScreen />;
    }
    
    if (!isAuthenticated) {
      return fallback || <Navigate to="/auth/login" replace />;
    }
    
    if (requireEmailVerification && !user?.emailVerified) {
      return <EmailVerificationRequired />;
    }
    
    return <>{children}</>;
  };
  
  // Permission-based guard
  interface PermissionGuardProps {
    children: React.ReactNode;
    requiredPermission: string;
    fallback?: React.ReactNode;
  }
  
  export const PermissionGuard = ({ 
    children, 
    requiredPermission, 
    fallback 
  }: PermissionGuardProps) => {
    const { permissions, role } = useAuthContext();
    
    const hasPermission = permissions.includes(requiredPermission) || role === 'admin';
    
    if (!hasPermission) {
      return fallback || <UnauthorizedAccess />;
    }
    
    return <>{children}</>;
  };
  ```

- **Guest Guards**: Redirect authenticated users from public pages
  ```typescript
  // ✅ Good
  export const GuestGuard = ({ children }: { children: React.ReactNode }) => {
    const { isAuthenticated, loading } = useAuthContext();
    const navigate = useNavigate();
    const location = useLocation();
    
    useEffect(() => {
      if (!loading && isAuthenticated) {
        // Redirect to intended destination or dashboard
        const from = (location.state as any)?.from || '/dashboard';
        navigate(from, { replace: true });
      }
    }, [loading, isAuthenticated, navigate, location]);
    
    if (loading) {
      return <LoadingScreen />;
    }
    
    if (isAuthenticated) {
      return null; // Will redirect via useEffect
    }
    
    return <>{children}</>;
  };
  ```

### Password Security
- **Password Validation**: Strong password requirements
  ```typescript
  // ✅ Good
  export const passwordSchema = z.string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password must be less than 128 characters')
    .regex(/^(?=.*[a-z])/, 'Password must contain at least one lowercase letter')
    .regex(/^(?=.*[A-Z])/, 'Password must contain at least one uppercase letter')
    .regex(/^(?=.*\d)/, 'Password must contain at least one number')
    .regex(/^(?=.*[@$!%*?&])/, 'Password must contain at least one special character');
  
  export const validatePasswordStrength = (password: string) => {
    const criteria = [
      { test: password.length >= 8, label: 'At least 8 characters' },
      { test: /[a-z]/.test(password), label: 'One lowercase letter' },
      { test: /[A-Z]/.test(password), label: 'One uppercase letter' },
      { test: /\d/.test(password), label: 'One number' },
      { test: /[@$!%*?&]/.test(password), label: 'One special character' },
    ];
    
    const score = criteria.filter(criterion => criterion.test).length;
    
    return {
      score,
      maxScore: criteria.length,
      strength: score < 3 ? 'weak' : score < 5 ? 'medium' : 'strong',
      criteria,
    };
  };
  ```

- **Password Reset Flow**: Secure password reset implementation
  ```typescript
  // ✅ Good
  export const usePasswordReset = () => {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [emailSent, setEmailSent] = useState(false);
    
    const sendResetEmail = async (email: string) => {
      try {
        setIsLoading(true);
        setError(null);
        
        const emailSchema = z.string().email();
        const validEmail = emailSchema.parse(email);
        
        await sendPasswordResetEmail(auth, validEmail, {
          url: `${window.location.origin}/auth/login`,
          handleCodeInApp: false,
        });
        
        setEmailSent(true);
      } catch (error) {
        setError(getAuthErrorMessage(error));
        throw error;
      } finally {
        setIsLoading(false);
      }
    };
    
    return { sendResetEmail, isLoading, error, emailSent };
  };
  ```

### Session Management
- **Token Refresh**: Automatic token refresh handling
  ```typescript
  // ✅ Good
  export const useTokenRefresh = () => {
    useEffect(() => {
      const refreshToken = async () => {
        const user = auth.currentUser;
        if (user) {
          try {
            // Force token refresh every 55 minutes (tokens expire in 1 hour)
            await user.getIdToken(true);
          } catch (error) {
            console.error('Token refresh failed:', error);
            // Force logout on refresh failure
            await signOut(auth);
          }
        }
      };
      
      // Set up periodic token refresh
      const interval = setInterval(refreshToken, 55 * 60 * 1000); // 55 minutes
      
      return () => clearInterval(interval);
    }, []);
  };
  ```

### Error Handling
- **Auth Error Messages**: User-friendly error messages
  ```typescript
  // ✅ Good
  export const getAuthErrorMessage = (error: any): string => {
    if (error?.code) {
      switch (error.code) {
        case 'auth/user-not-found':
          return 'No account found with this email address.';
        case 'auth/wrong-password':
          return 'Incorrect password. Please try again.';
        case 'auth/invalid-email':
          return 'Please enter a valid email address.';
        case 'auth/user-disabled':
          return 'This account has been disabled. Please contact support.';
        case 'auth/email-already-in-use':
          return 'An account with this email already exists.';
        case 'auth/weak-password':
          return 'Password is too weak. Please choose a stronger password.';
        case 'auth/network-request-failed':
          return 'Network error. Please check your connection and try again.';
        case 'auth/too-many-requests':
          return 'Too many failed attempts. Please try again later.';
        default:
          return 'An unexpected error occurred. Please try again.';
      }
    }
    
    if (error instanceof z.ZodError) {
      return error.errors[0]?.message || 'Invalid input data.';
    }
    
    return error?.message || 'An unexpected error occurred.';
  };
  ```

### Security Best Practices
- **Input Validation**: Always validate user inputs
  ```typescript
  // ✅ Good
  const loginFormSchema = z.object({
    email: z.string()
      .min(1, 'Email is required')
      .email('Invalid email format')
      .max(254, 'Email is too long'),
    password: z.string()
      .min(1, 'Password is required')
      .max(128, 'Password is too long'),
  });
  
  // Sanitize inputs
  export const sanitizeInput = (input: string): string => {
    return input.trim().replace(/[<>]/g, '');
  };
  ```

- **Rate Limiting**: Implement client-side rate limiting
  ```typescript
  // ✅ Good
  export const useRateLimit = (maxAttempts: number, windowMs: number) => {
    const [attempts, setAttempts] = useState<number[]>([]);
    
    const canAttempt = useCallback(() => {
      const now = Date.now();
      const recentAttempts = attempts.filter(time => now - time < windowMs);
      return recentAttempts.length < maxAttempts;
    }, [attempts, maxAttempts, windowMs]);
    
    const recordAttempt = useCallback(() => {
      const now = Date.now();
      setAttempts(prev => [...prev.filter(time => now - time < windowMs), now]);
    }, [windowMs]);
    
    return { canAttempt, recordAttempt };
  };
  ```

### Multi-Factor Authentication
- **MFA Setup**: Support for additional security layers
  ```typescript
  // ✅ Good
  export const useMFA = () => {
    const setupMFA = async () => {
      const user = auth.currentUser;
      if (!user) throw new Error('User not authenticated');
      
      const multiFactorSession = await multiFactor(user).getSession();
      const phoneAuthCredential = PhoneAuthProvider.credential(
        verificationId,
        verificationCode
      );
      
      const multiFactorAssertion = PhoneMultiFactorGenerator
        .assertion(phoneAuthCredential);
      
      await multiFactor(user).enroll(multiFactorAssertion, session);
    };
    
    return { setupMFA };
  };
  ```

## Security Checklist
- **Always validate inputs** with Zod or similar validation library
- **Use HTTPS** for all authentication requests
- **Implement proper error handling** without exposing sensitive information
- **Store sensitive data securely** (use Firebase Security Rules)
- **Implement session timeouts** and automatic logout
- **Use role-based permissions** for feature access
- **Monitor authentication events** for suspicious activity
- **Implement proper logout** with token cleanup

## Exceptions
- **Development environment**: Relaxed validation for testing purposes
- **Legacy authentication**: Maintain existing patterns during migration
- **Third-party SSO**: Follow provider-specific authentication flows
