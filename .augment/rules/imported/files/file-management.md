---
type: "agent_requested"
---

# File Management Best Practices

## Objective
Implement secure, efficient file upload, processing, and management with proper validation, progress tracking, and user experience.

## Context
- React Dropzone for file upload interface
- File validation and type checking
- Transcoding status monitoring for media files
- Permission-based file access control
- File thumbnails and preview generation

## Rules

### File Upload Components
- **Dropzone Implementation**: Secure file upload with validation
  ```typescript
  // ✅ Good
  interface FileUploadProps {
    onFilesAccepted: (files: File[]) => void;
    onFilesRejected?: (rejections: FileRejection[]) => void;
    maxSize?: number;
    maxFiles?: number;
    acceptedTypes?: string[];
    disabled?: boolean;
  }
  
  export const FileUpload = ({
    onFilesAccepted,
    onFilesRejected,
    maxSize = 10 * 1024 * 1024, // 10MB
    maxFiles = 10,
    acceptedTypes = ['image/*', 'video/*', 'audio/*', '.pdf', '.doc', '.docx'],
    disabled = false,
  }: FileUploadProps) => {
    const {
      getRootProps,
      getInputProps,
      isDragActive,
      isDragReject,
      fileRejections,
    } = useDropzone({
      onDrop: onFilesAccepted,
      onDropRejected: onFilesRejected,
      maxSize,
      maxFiles,
      accept: acceptedTypes.reduce((acc, type) => {
        acc[type] = [];
        return acc;
      }, {} as Record<string, string[]>),
      disabled,
      multiple: maxFiles > 1,
    });
    
    return (
      <Box
        {...getRootProps()}
        sx={{
          border: 2,
          borderStyle: 'dashed',
          borderColor: isDragActive 
            ? 'primary.main' 
            : isDragReject 
            ? 'error.main' 
            : 'grey.300',
          borderRadius: 2,
          p: 4,
          textAlign: 'center',
          cursor: disabled ? 'not-allowed' : 'pointer',
          backgroundColor: isDragActive ? 'primary.light' : 'background.paper',
          transition: 'all 0.2s ease',
        }}
      >
        <input {...getInputProps()} />
        
        <CloudUploadIcon 
          sx={{ 
            fontSize: 48, 
            color: isDragReject ? 'error.main' : 'primary.main',
            mb: 2 
          }} 
        />
        
        <Typography variant="h6" gutterBottom>
          {isDragActive 
            ? 'Drop files here...' 
            : 'Drag & drop files here, or click to select'}
        </Typography>
        
        <Typography variant="body2" color="text.secondary">
          Supported formats: {acceptedTypes.join(', ')}
        </Typography>
        
        <Typography variant="body2" color="text.secondary">
          Max file size: {formatBytes(maxSize)} | Max files: {maxFiles}
        </Typography>
        
        {fileRejections.length > 0 && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {fileRejections.map(({ file, errors }) => (
              <div key={file.name}>
                {file.name}: {errors.map(e => e.message).join(', ')}
              </div>
            ))}
          </Alert>
        )}
      </Box>
    );
  };
  ```

### File Validation
- **Client-Side Validation**: Comprehensive file validation
  ```typescript
  // ✅ Good
  interface FileValidationResult {
    isValid: boolean;
    errors: string[];
  }
  
  export const validateFile = (file: File): FileValidationResult => {
    const errors: string[] = [];
    
    // File size validation
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSize) {
      errors.push(`File size must be less than ${formatBytes(maxSize)}`);
    }
    
    // File type validation
    const allowedTypes = [
      'image/jpeg', 'image/png', 'image/gif', 'image/webp',
      'video/mp4', 'video/mov', 'video/avi', 'video/webm',
      'audio/mp3', 'audio/wav', 'audio/ogg',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    
    if (!allowedTypes.includes(file.type)) {
      errors.push(`File type "${file.type}" is not allowed`);
    }
    
    // File name validation
    const maxNameLength = 255;
    if (file.name.length > maxNameLength) {
      errors.push(`File name must be less than ${maxNameLength} characters`);
    }
    
    // Check for potentially dangerous file extensions
    const dangerousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif'];
    const extension = file.name.toLowerCase().split('.').pop();
    if (extension && dangerousExtensions.includes(`.${extension}`)) {
      errors.push('File type not allowed for security reasons');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
    };
  };
  
  export const validateFileList = (files: File[]): FileValidationResult => {
    const allErrors: string[] = [];
    
    files.forEach((file, index) => {
      const result = validateFile(file);
      if (!result.isValid) {
        allErrors.push(`File ${index + 1} (${file.name}): ${result.errors.join(', ')}`);
      }
    });
    
    return {
      isValid: allErrors.length === 0,
      errors: allErrors,
    };
  };
  ```

### File Upload Progress
- **Upload Progress Tracking**: Monitor upload progress with status updates
  ```typescript
  // ✅ Good
  interface UploadProgress {
    fileId: string;
    fileName: string;
    progress: number; // 0-100
    status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
    error?: string;
    url?: string;
  }
  
  export const useFileUpload = () => {
    const [uploads, setUploads] = useState<Record<string, UploadProgress>>({});
    
    const uploadFile = async (file: File): Promise<string> => {
      const fileId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      // Initialize upload progress
      setUploads(prev => ({
        ...prev,
        [fileId]: {
          fileId,
          fileName: file.name,
          progress: 0,
          status: 'pending',
        },
      }));
      
      try {
        // Validate file
        const validation = validateFile(file);
        if (!validation.isValid) {
          throw new Error(validation.errors.join(', '));
        }
        
        // Update status to uploading
        setUploads(prev => ({
          ...prev,
          [fileId]: { ...prev[fileId], status: 'uploading' },
        }));
        
        // Create form data
        const formData = new FormData();
        formData.append('file', file);
        formData.append('metadata', JSON.stringify({
          originalName: file.name,
          size: file.size,
          type: file.type,
        }));
        
        // Upload with progress tracking
        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
          headers: {
            'Authorization': `Bearer ${await getAuthToken()}`,
          },
          // Track upload progress
          onUploadProgress: (progressEvent) => {
            const progress = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            );
            
            setUploads(prev => ({
              ...prev,
              [fileId]: { ...prev[fileId], progress },
            }));
          },
        });
        
        if (!response.ok) {
          throw new Error(`Upload failed: ${response.statusText}`);
        }
        
        const result = await response.json();
        
        // Update to processing status
        setUploads(prev => ({
          ...prev,
          [fileId]: {
            ...prev[fileId],
            status: 'processing',
            progress: 100,
          },
        }));
        
        // Start polling for transcoding status if needed
        if (result.requiresProcessing) {
          pollTranscodingStatus(fileId, result.processingId);
        } else {
          // Mark as completed
          setUploads(prev => ({
            ...prev,
            [fileId]: {
              ...prev[fileId],
              status: 'completed',
              url: result.url,
            },
          }));
        }
        
        return fileId;
        
      } catch (error) {
        setUploads(prev => ({
          ...prev,
          [fileId]: {
            ...prev[fileId],
            status: 'error',
            error: error instanceof Error ? error.message : 'Upload failed',
          },
        }));
        
        throw error;
      }
    };
    
    const removeUpload = (fileId: string) => {
      setUploads(prev => {
        const { [fileId]: removed, ...rest } = prev;
        return rest;
      });
    };
    
    return {
      uploads: Object.values(uploads),
      uploadFile,
      removeUpload,
    };
  };
  ```

### Transcoding Status Monitoring
- **Status Polling**: Monitor file processing status
  ```typescript
  // ✅ Good
  interface TranscodingStatus {
    id: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    progress: number;
    error?: string;
    outputs?: Array<{
      format: string;
      url: string;
      size: number;
    }>;
  }
  
  export const useTranscodingStatusPolling = (processingId: string) => {
    const [status, setStatus] = useState<TranscodingStatus | null>(null);
    const [isPolling, setIsPolling] = useState(false);
    
    const pollStatus = useCallback(async () => {
      try {
        const response = await fetch(`/api/transcoding/${processingId}/status`, {
          headers: {
            'Authorization': `Bearer ${await getAuthToken()}`,
          },
        });
        
        if (!response.ok) {
          throw new Error('Failed to fetch transcoding status');
        }
        
        const statusData: TranscodingStatus = await response.json();
        setStatus(statusData);
        
        // Stop polling if completed or failed
        if (statusData.status === 'completed' || statusData.status === 'failed') {
          setIsPolling(false);
        }
        
      } catch (error) {
        console.error('Error polling transcoding status:', error);
        setIsPolling(false);
      }
    }, [processingId]);
    
    useEffect(() => {
      if (!isPolling) return;
      
      const interval = setInterval(pollStatus, 2000); // Poll every 2 seconds
      
      return () => clearInterval(interval);
    }, [isPolling, pollStatus]);
    
    const startPolling = useCallback(() => {
      setIsPolling(true);
      pollStatus(); // Initial poll
    }, [pollStatus]);
    
    const stopPolling = useCallback(() => {
      setIsPolling(false);
    }, []);
    
    return {
      status,
      isPolling,
      startPolling,
      stopPolling,
    };
  };
  ```

### File Thumbnails
- **Thumbnail Generation**: Create and display file thumbnails
  ```typescript
  // ✅ Good
  interface FileThumbnailProps {
    file: {
      type: string;
      name: string;
      url?: string;
      thumbnailUrl?: string;
    };
    size?: 'small' | 'medium' | 'large';
    onClick?: () => void;
  }
  
  export const FileThumbnail = ({ file, size = 'medium', onClick }: FileThumbnailProps) => {
    const [imageError, setImageError] = useState(false);
    
    const sizeMap = {
      small: 40,
      medium: 80,
      large: 120,
    };
    
    const thumbnailSize = sizeMap[size];
    
    const renderThumbnail = () => {
      if (file.type.startsWith('image/') && file.thumbnailUrl && !imageError) {
        return (
          <img
            src={file.thumbnailUrl}
            alt={file.name}
            style={{
              width: thumbnailSize,
              height: thumbnailSize,
              objectFit: 'cover',
              borderRadius: 4,
            }}
            onError={() => setImageError(true)}
          />
        );
      }
      
      // Fallback to file type icon
      const getFileIcon = (type: string) => {
        if (type.startsWith('image/')) return <ImageIcon />;
        if (type.startsWith('video/')) return <VideoFileIcon />;
        if (type.startsWith('audio/')) return <AudioFileIcon />;
        if (type === 'application/pdf') return <PictureAsPdfIcon />;
        if (type.includes('word') || type.includes('document')) return <DescriptionIcon />;
        return <InsertDriveFileIcon />;
      };
      
      return (
        <Box
          sx={{
            width: thumbnailSize,
            height: thumbnailSize,
            backgroundColor: 'grey.100',
            borderRadius: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'grey.600',
          }}
        >
          {getFileIcon(file.type)}
        </Box>
      );
    };
    
    return (
      <Box
        onClick={onClick}
        sx={{
          cursor: onClick ? 'pointer' : 'default',
          '&:hover': onClick ? {
            opacity: 0.8,
            transform: 'scale(1.05)',
          } : {},
          transition: 'all 0.2s ease',
        }}
      >
        {renderThumbnail()}
      </Box>
    );
  };
  ```

### File Permissions
- **Permission-Based Access**: Control file access with user permissions
  ```typescript
  // ✅ Good
  interface FilePermissions {
    canView: boolean;
    canEdit: boolean;
    canDelete: boolean;
    canShare: boolean;
  }
  
  export const useFilePermissions = (fileId: string, ownerId: string) => {
    const { user, permissions, role } = useAuthContext();
    
    const filePermissions = useMemo((): FilePermissions => {
      if (!user) {
        return {
          canView: false,
          canEdit: false,
          canDelete: false,
          canShare: false,
        };
      }
      
      // File owner has all permissions
      const isOwner = user.uid === ownerId;
      if (isOwner) {
        return {
          canView: true,
          canEdit: true,
          canDelete: true,
          canShare: true,
        };
      }
      
      // Admin has all permissions
      if (role === 'admin') {
        return {
          canView: true,
          canEdit: true,
          canDelete: true,
          canShare: true,
        };
      }
      
      // Check specific permissions
      const canView = permissions.includes('files:view') || permissions.includes('files:*');
      const canEdit = permissions.includes('files:edit') || permissions.includes('files:*');
      const canDelete = permissions.includes('files:delete') || permissions.includes('files:*');
      const canShare = permissions.includes('files:share') || permissions.includes('files:*');
      
      return {
        canView,
        canEdit,
        canDelete,
        canShare,
      };
    }, [user, permissions, role, ownerId]);
    
    return filePermissions;
  };
  
  // Action buttons with permission checks
  interface FileActionButtonsProps {
    fileId: string;
    ownerId: string;
    onEdit?: () => void;
    onDelete?: () => void;
    onShare?: () => void;
    onDownload?: () => void;
  }
  
  export const FileActionButtons = ({
    fileId,
    ownerId,
    onEdit,
    onDelete,
    onShare,
    onDownload,
  }: FileActionButtonsProps) => {
    const permissions = useFilePermissions(fileId, ownerId);
    
    return (
      <Stack direction="row" spacing={1}>
        {permissions.canView && onDownload && (
          <IconButton onClick={onDownload} size="small">
            <DownloadIcon />
          </IconButton>
        )}
        
        {permissions.canEdit && onEdit && (
          <IconButton onClick={onEdit} size="small">
            <EditIcon />
          </IconButton>
        )}
        
        {permissions.canShare && onShare && (
          <IconButton onClick={onShare} size="small">
            <ShareIcon />
          </IconButton>
        )}
        
        {permissions.canDelete && onDelete && (
          <IconButton 
            onClick={onDelete} 
            size="small"
            color="error"
          >
            <DeleteIcon />
          </IconButton>
        )}
      </Stack>
    );
  };
  ```

### File List Management
- **File List Component**: Display and manage file collections
  ```typescript
  // ✅ Good
  interface FileListProps {
    files: FileItem[];
    loading?: boolean;
    onFileSelect?: (file: FileItem) => void;
    onFileDelete?: (fileId: string) => void;
    onFileShare?: (file: FileItem) => void;
    viewMode?: 'list' | 'grid';
  }
  
  export const FileList = ({
    files,
    loading = false,
    onFileSelect,
    onFileDelete,
    onFileShare,
    viewMode = 'list',
  }: FileListProps) => {
    const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set());
    
    const handleFileClick = (file: FileItem) => {
      if (onFileSelect) {
        onFileSelect(file);
      }
    };
    
    const handleSelectFile = (fileId: string, selected: boolean) => {
      setSelectedFiles(prev => {
        const newSelection = new Set(prev);
        if (selected) {
          newSelection.add(fileId);
        } else {
          newSelection.delete(fileId);
        }
        return newSelection;
      });
    };
    
    if (loading) {
      return (
        <Box display="flex" justifyContent="center" p={4}>
          <CircularProgress />
        </Box>
      );
    }
    
    if (files.length === 0) {
      return (
        <EmptyContent
          title="No files found"
          description="Upload files to get started"
          imgUrl="/assets/illustrations/empty-files.svg"
        />
      );
    }
    
    if (viewMode === 'grid') {
      return (
        <Grid container spacing={2}>
          {files.map((file) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={file.id}>
              <FileCard
                file={file}
                selected={selectedFiles.has(file.id)}
                onClick={() => handleFileClick(file)}
                onSelect={(selected) => handleSelectFile(file.id, selected)}
                onDelete={onFileDelete}
                onShare={onFileShare}
              />
            </Grid>
          ))}
        </Grid>
      );
    }
    
    return (
      <List>
        {files.map((file) => (
          <FileListItem
            key={file.id}
            file={file}
            selected={selectedFiles.has(file.id)}
            onClick={() => handleFileClick(file)}
            onSelect={(selected) => handleSelectFile(file.id, selected)}
            onDelete={onFileDelete}
            onShare={onFileShare}
          />
        ))}
      </List>
    );
  };
  ```

### Utility Functions
- **File Helper Functions**: Common file operations
  ```typescript
  // ✅ Good
  export const formatBytes = (bytes: number, decimals = 2): string => {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  };
  
  export const getFileExtension = (filename: string): string => {
    return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
  };
  
  export const getMimeTypeFromExtension = (extension: string): string => {
    const mimeTypes: Record<string, string> = {
      'pdf': 'application/pdf',
      'doc': 'application/msword',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'mp4': 'video/mp4',
      'mov': 'video/quicktime',
      'mp3': 'audio/mpeg',
      'wav': 'audio/wav',
    };
    
    return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
  };
  
  export const generateFilePreview = async (file: File): Promise<string | null> => {
    if (!file.type.startsWith('image/')) {
      return null;
    }
    
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = () => resolve(null);
      reader.readAsDataURL(file);
    });
  };
  ```

## File Security Guidelines
- **Always validate file types** on both client and server
- **Limit file sizes** to prevent abuse
- **Scan uploaded files** for malware when possible
- **Use secure file storage** with proper access controls
- **Implement proper permissions** for file operations
- **Monitor file operations** for suspicious activity
- **Cleanup temporary files** and failed uploads

## Exceptions
- **Large file uploads**: Alternative upload strategies for files > 100MB
- **Legacy file formats**: Support for older file types in specific contexts
- **Specialized processing**: Custom handling for domain-specific file types
