---
type: "always_apply"
---

# React + TypeScript Best Practices

## Objective

Ensure type-safe, performant, and maintainable React components with proper TypeScript usage.

## Context

- React 18 with TypeScript in strict mode
- Functional components with hooks
- forwardRef pattern for reusable components
- Material-UI integration
- Custom hook patterns

## Rules

### Component Definition

- **Functional Components**: Use arrow functions for components

  ```typescript
  // ✅ Good
  export const UserProfile = ({ user, onEdit }: UserProfileProps) => {
    return <div>...</div>;
  };

  // ❌ Avoid
  export function UserProfile(props: UserProfileProps) {
    return <div>...</div>;
  }
  ```

- **Component Props**: Always define explicit interfaces

  ```typescript
  // ✅ Good
  interface UserProfileProps {
    user: User;
    onEdit?: (user: User) => void;
    disabled?: boolean;
  }

  // ❌ Avoid
  type UserProfileProps = {
    user: any;
    onEdit: Function;
  };
  ```

- **ForwardRef Pattern**: Use for reusable components that need ref access
  ```typescript
  // ✅ Good
  export const CustomButton = forwardRef<HTMLButtonElement, CustomButtonProps>(
    (props, ref) => {
      const { children, variant = 'contained', ...other } = props;
      return <Button ref={ref} variant={variant} {...other}>{children}</Button>;
    }
  );
  ```

### TypeScript Patterns

- **Type Imports**: Use explicit type imports

  ```typescript
  // ✅ Good
  import type { User } from 'src/types/user';
  import type { ComponentProps } from 'react';

  // ❌ Avoid
  import { User } from 'src/types/user';
  ```

- **Generic Components**: Use proper generic constraints

  ```typescript
  // ✅ Good
  interface DataTableProps<T extends Record<string, any>> {
    data: T[];
    columns: Array<keyof T>;
    onRowClick?: (item: T) => void;
  }

  export const DataTable = <T extends Record<string, any>>({
    data,
    columns,
    onRowClick,
  }: DataTableProps<T>) => {
    // ...
  };
  ```

- **Utility Types**: Leverage TypeScript utility types

  ```typescript
  // ✅ Good
  type PartialUser = Partial<User>;
  type UserWithoutId = Omit<User, 'id'>;
  type UserEmail = Pick<User, 'email'>;

  // ❌ Avoid defining custom interfaces for simple cases
  interface PartialUser {
    id?: string;
    name?: string;
    email?: string;
  }
  ```

### Custom Hooks

- **Hook Naming**: Always start with `use` prefix

  ```typescript
  // ✅ Good
  export function useAuthContext() {
    const context = useContext(AuthContext);
    if (!context) {
      throw new Error('useAuthContext must be used within AuthProvider');
    }
    return context;
  }

  // ❌ Avoid
  export function getAuthContext() { ... }
  ```

- **Hook Return Types**: Explicit return type definitions

  ```typescript
  // ✅ Good
  interface UseApiResult<T> {
    data: T | null;
    loading: boolean;
    error: string | null;
    refetch: () => Promise<void>;
  }

  export function useApi<T>(url: string): UseApiResult<T> {
    // ...
  }
  ```

- **Hook Dependencies**: Always specify dependencies explicitly

  ```typescript
  // ✅ Good
  useEffect(() => {
    fetchUserData(userId);
  }, [userId, fetchUserData]);

  // ❌ Avoid
  useEffect(() => {
    fetchUserData(userId);
  }, []); // Missing dependencies
  ```

### State Management

- **useState Typing**: Explicit state types when not inferred

  ```typescript
  // ✅ Good
  const [user, setUser] = useState<User | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // ❌ Avoid
  const [user, setUser] = useState(null);
  const [errors, setErrors] = useState({});
  ```

- **Reducer Pattern**: For complex state logic

  ```typescript
  // ✅ Good
  interface FormState {
    values: Record<string, any>;
    errors: Record<string, string>;
    touched: Record<string, boolean>;
    isSubmitting: boolean;
  }

  type FormAction =
    | { type: 'SET_VALUE'; field: string; value: any }
    | { type: 'SET_ERROR'; field: string; error: string }
    | { type: 'SET_SUBMITTING'; isSubmitting: boolean };

  const formReducer = (state: FormState, action: FormAction): FormState => {
    switch (action.type) {
      case 'SET_VALUE':
        return { ...state, values: { ...state.values, [action.field]: action.value } };
      // ...
    }
  };
  ```

### Event Handling

- **Event Types**: Use proper React event types

  ```typescript
  // ✅ Good
  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    // ...
  };

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setValue(event.target.value);
  };

  // ❌ Avoid
  const handleSubmit = (event: any) => { ... };
  ```

### Error Boundaries

- **Error Boundary Components**: Always include error boundaries for sections

  ```typescript
  // ✅ Good
  interface ErrorBoundaryState {
    hasError: boolean;
    error?: Error;
  }

  export class ErrorBoundary extends Component<
    PropsWithChildren,
    ErrorBoundaryState
  > {
    constructor(props: PropsWithChildren) {
      super(props);
      this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: Error): ErrorBoundaryState {
      return { hasError: true, error };
    }

    componentDidCatch(error: Error, errorInfo: ErrorInfo) {
      console.error('Error caught by boundary:', error, errorInfo);
    }

    render() {
      if (this.state.hasError) {
        return <ErrorFallback error={this.state.error} />;
      }

      return this.props.children;
    }
  }
  ```

### Performance Optimization

- **React.memo**: Use for expensive components

  ```typescript
  // ✅ Good
  export const ExpensiveComponent = React.memo<ExpensiveComponentProps>(
    ({ data, onAction }) => {
      // Expensive rendering logic
      return <div>...</div>;
    },
    (prevProps, nextProps) => {
      return prevProps.data.id === nextProps.data.id;
    }
  );
  ```

- **useCallback/useMemo**: Use for expensive computations

  ```typescript
  // ✅ Good
  const expensiveValue = useMemo(() => {
    return data.reduce((acc, item) => acc + item.value, 0);
  }, [data]);

  const handleClick = useCallback(
    (id: string) => {
      onItemClick(id);
    },
    [onItemClick]
  );
  ```

### Component Composition

- **Render Props**: Use for flexible component APIs

  ```typescript
  // ✅ Good
  interface DataProviderProps<T> {
    url: string;
    children: (data: { data: T | null; loading: boolean; error: string | null }) => React.ReactNode;
  }

  export const DataProvider = <T>({ url, children }: DataProviderProps<T>) => {
    const { data, loading, error } = useApi<T>(url);
    return children({ data, loading, error });
  };
  ```

## Exceptions

- **Legacy components**: Maintain consistency within existing class components
- **Third-party component wrappers**: Follow library-specific patterns when wrapping external components
- **Performance critical paths**: Alternative patterns allowed for optimization
