---
type: "always_apply"
---

# Aida Project - Master Development Rule

## Project Context & Overview

**Aida** is a React/TypeScript application for AI-powered document processing, file management, and collaborative workspaces with real-time features.

### Technology Stack
- **Frontend**: React 18 + TypeScript (strict mode) + Vite + SWC
- **UI Framework**: Material-UI v6 (@mui/material, @mui/joy, @mui/lab)
- **State Management**: Redux Toolkit + RTK Query + GraphQL + React Context
- **Authentication**: Firebase Auth + JWT tokens + Role-based access control
- **File Handling**: React Dropzone + File processing + Transcoding status monitoring
- **Forms**: React Hook Form + Zod validation + Material-UI integration
- **Testing**: React Testing Library + Jest + Sentry monitoring
- **Routing**: React Router v7 + Route guards + Permission-based access
- **Build & Deploy**: Vite + Firebase hosting + Sentry integration

### Core Development Philosophy
1. **Type Safety First**: Strict TypeScript, explicit types, no `any`
2. **Component Composition**: Reusable, focused, well-typed components
3. **Domain-Driven Structure**: Feature-based organization with clear boundaries
4. **Security-First**: Comprehensive auth, validation, and error handling
5. **Performance Conscious**: Memoization, lazy loading, optimized bundles
6. **Testing-Driven**: Meaningful tests for business logic and user interactions

## Rule Application Logic

Based on your current task, the following rules should be applied:

### Always Apply (Core Foundation)
- **Naming Conventions** (`core/naming-conventions.mdc`)
- **Project Structure** (`core/project-structure.mdc`)

### Frontend Development Tasks
**When working with React components (.tsx files):**
- Apply `frontend/react-typescript.mdc`
- Apply `frontend/material-ui.mdc` if using MUI components

**When working with state management (store/, hooks/, context/):**
- Apply `frontend/state-management.mdc`
- Apply `frontend/react-typescript.mdc` for custom hooks

### Domain-Specific Tasks
**Authentication-related work (auth/, login, permissions, guards):**
- Apply `auth/authentication-security.mdc`
- Apply `frontend/react-typescript.mdc` for auth components

**File management tasks (upload, processing, thumbnails, resources):**
- Apply `files/file-management.mdc`
- Apply `frontend/react-typescript.mdc` for file components

**Testing tasks (.test.ts, .test.tsx, .spec.ts files):**
- Apply `quality/testing-standards.mdc`
- Apply relevant domain rule based on component being tested

### Development Context Clues

**File Path Indicators:**
- `src/auth/**/*` → Authentication domain rules
- `src/sections/resources/**/*` → File management rules  
- `src/store/**/*` → State management rules
- `src/components/**/*` → Core component + MUI rules
- `src/pages/**/*` → All frontend rules
- `**/*.test.*` → Testing standards

**Task Type Indicators:**
- Creating/modifying login/signup → Authentication rules
- File upload/download features → File management rules
- API integration/data fetching → State management rules
- UI component creation → React + Material-UI rules
- Form development → React + validation rules
- Testing implementation → Testing standards

**Keywords in Prompts:**
- "auth", "login", "permission", "security" → Authentication rules
- "upload", "file", "document", "processing" → File management rules
- "store", "redux", "api", "query", "state" → State management rules
- "component", "UI", "material", "styling" → Frontend component rules
- "test", "mock", "coverage", "jest" → Testing rules

## Quick Reference Standards

### File Naming (Always Follow)
- **Files**: kebab-case (`user-profile.tsx`, `auth-context.ts`)
- **Components**: PascalCase (`UserProfile`, `AuthGuard`)
- **Hooks**: `use-` prefix + kebab-case (`use-auth-context.ts`)

### Import Patterns (Always Follow)
```typescript
// Type imports first
import type { User } from 'src/types/user';

// External libraries
import { useState, useCallback } from 'react';
import Button from '@mui/material/Button';

// Internal imports (absolute paths)
import { useAuthContext } from 'src/auth/hooks';
import { UserProfile } from 'src/components/user-profile';
```

### Component Structure (Always Follow)
```typescript
// Props interface
interface ComponentProps {
  // Explicit typing
}

// Component with forwardRef if needed
export const Component = forwardRef<HTMLElement, ComponentProps>(
  ({ prop1, prop2, ...other }, ref) => {
    // Hooks at top
    // Event handlers
    // Render logic
    return <div ref={ref} {...other}>...</div>;
  }
);
```

### Error Handling (Always Include)
```typescript
// Always handle loading, error, and success states
const { data, isLoading, error } = useApiQuery();

if (isLoading) return <LoadingSpinner />;
if (error) return <ErrorMessage error={error} />;
return <SuccessContent data={data} />;
```

## Development Priorities

### High Priority (Never Compromise)
1. **Security**: Authentication, authorization, input validation
2. **Type Safety**: Proper TypeScript usage, no `any` types
3. **User Experience**: Loading states, error handling, accessibility
4. **Code Organization**: Consistent file structure and naming

### Medium Priority (Follow When Possible)
1. **Performance**: Memoization, lazy loading, bundle optimization
2. **Testing**: Component tests, hook tests, integration tests
3. **Documentation**: Clear prop interfaces, function signatures
4. **Code Quality**: ESLint compliance, consistent formatting

### Context-Specific Guidance

**For New Features:**
1. Identify domain (auth, files, general UI)
2. Apply appropriate domain rules
3. Follow core naming and structure conventions
4. Implement proper error handling and loading states
5. Add TypeScript types and interfaces
6. Include basic tests for business logic

**For Bug Fixes:**
1. Maintain existing patterns within the module
2. Apply security rules if touching auth/validation
3. Follow error handling patterns
4. Add tests to prevent regression

**For Refactoring:**
1. Gradually apply current standards
2. Maintain API compatibility
3. Update related tests
4. Document breaking changes

## Rule Conflicts Resolution

When rules conflict, follow this priority:
1. **Security requirements** (auth, validation, permissions)
2. **Core conventions** (naming, project structure)  
3. **TypeScript patterns** (type safety, explicit typing)
4. **Domain-specific patterns** (auth, files, state management)
5. **UI/UX patterns** (Material-UI, component composition)
6. **Code quality** (testing, performance, documentation)

---

**This master rule ensures consistent development practices while providing intelligent routing to specialized rules based on context and task requirements.**
