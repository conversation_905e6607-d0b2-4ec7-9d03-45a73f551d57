{"permissions": {"allow": ["Bash(npm run lint)", "Bash(npm run lint:*)", "Bash(npm run build:*)", "Bash(npm run:*)", "WebFetch(domain:www.loom.com)", "Bash(rm:*)", "Bash(npx tsc:*)", "Bash(npx eslint:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(grep:*)", "<PERSON><PERSON>(sed:*)", "mcp__sentry__find_organizations", "mcp__sentry__find_projects", "mcp__sentry__search_issues", "mcp__sentry__get_issue_details", "WebFetch(domain:redux-toolkit.js.org)"], "deny": []}}