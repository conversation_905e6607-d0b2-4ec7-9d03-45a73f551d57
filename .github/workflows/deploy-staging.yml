name: Deploy to Staging

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        type: choice
        options:
          - staging
        default: 'staging'

env:
  NODE_VERSION: '22'
  FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}

jobs:
  deploy:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    environment: staging
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'yarn'

      - name: Enable Corepack
        run: corepack enable

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Build application
        run: yarn run build:staging
        env:
          NODE_ENV: staging
          VITE_APP_ENV: staging
          VITE_GIT_SHA: ${{ github.sha }}

      - name: Install and Deploy to Firebase
        run: |
          npm install -g firebase-tools@latest
          firebase use aida---stg
          firebase deploy --token "${{ env.FIREBASE_TOKEN }}" --non-interactive
        env:
          FIREBASE_TOKEN: ${{ env.FIREBASE_TOKEN }} 