# 🤖 Multi-Model Integration Implementation Plan

## 📋 Overview

This document outlines the implementation plan for integrating the frontend AIDA application with the new multi-provider backend service, enabling support for Google Gemini, OpenAI GPT, and Anthropic Claude models.

**🎯 HYBRID APPROACH**: This implementation combines the best of both worlds - maintaining 100% backward compatibility while enabling unlimited scalability:

- **🔒 Flag OFF (Legacy)**: Uses existing 2 hardcoded Gemini models (zero changes to current UX)
- **🚀 Flag ON (Multi-Agent)**: 100% API-driven with all providers via `GET /agent/models`

**🛡️ Key Benefits**:

- ✅ **Zero Risk Migration**: Existing users experience no changes when flag is disabled
- 🔄 **Future-Proof Scaling**: API-driven approach supports unlimited models when enabled
- 📊 **Rich Metadata**: Complete model ecosystem from single backend API

## 🎯 Requirements

- ✅ **Feature Flag**: New "multi-agents" flag to toggle multi-model functionality
- ✅ **UI Update**: Replace ChatPanelHeader switch with dropdown for model selection
- ✅ **Backend Integration**: Full compatibility with multi-provider service
- ✅ **Backward Compatibility**: Seamless fallback when feature flag is disabled

---

## 📊 Current State Analysis

### 🔍 Frontend Chat System

- **Current Models**: Only 2 Gemini models (`gemini-2.5-flash`, `gemini-2.5-pro`)
- **Model Types**: Defined in `src/lib/firebase.ts` and `src/sections/chat/types.ts`
- **UI Component**: `ChatPanelHeader` uses simple switch toggle
- **Feature Flags**: Managed via PostHog with `AppFeatures` enum

### 🔍 Backend Service Capability

- **Providers**: Google, OpenAI, Anthropic (12+ models supported)
- **API Compatibility**: Zero impact guarantee - same endpoints, same format
- **Model Support**: All models route through universal interface
- **🆕 Model Discovery API**: `GET /agent/models` returns all available models with metadata

---

## 🔌 **API-First Architecture**

### **Backend Endpoint**: `GET /agent/models`

The implementation is built entirely around this single API endpoint:

```typescript
// API Response Structure (from backend)
{
  "success": true,
  "message": "Available models retrieved",
  "data": {
    "models": {
      "openai": [
        {
          "id": "gpt-4o",
          "name": "GPT-4o",
          "provider": "openai",
          "tier": "premium",
          "description": "Most capable GPT-4 model",
          "features": {
            "maxTokens": 128000,
            "supportsImages": true,
            "supportsStreaming": true
          }
        }
        // ... more OpenAI models
      ],
      "anthropic": [
        {
          "id": "claude-3-5-sonnet-20241022",
          "name": "Claude 3.5 Sonnet",
          "provider": "anthropic",
          "tier": "premium",
          "description": "Most intelligent Claude model",
          "features": {
            "maxTokens": 200000,
            "supportsImages": true,
            "supportsStreaming": true
          }
        }
        // ... more Anthropic models
      ],
      "google": [
        // ... all Google models
      ]
    },
    "all": [...], // Flat array of all models
    "total": 12
  }
}
```

### **🎯 Frontend Strategy**: **Conditional Architecture**

**🔒 Legacy Mode (Flag OFF)**:

- ✅ Uses existing 2 hardcoded Gemini models (`gemini-2.5-flash`, `gemini-2.5-pro`)
- ✅ No API calls needed - preserves current performance
- ✅ Zero changes to existing user experience
- ✅ Switch toggle UI remains exactly the same

**🚀 Multi-Agent Mode (Flag ON)**:

- ✅ Frontend fetches all models from API dynamically
- ✅ UI adapts to support unlimited models and providers
- ✅ Rich dropdown with provider icons, tiers, descriptions
- ✅ New models appear automatically without code updates

---

## 🚀 Implementation Plan

### **Phase 1: Type System Updates** ⭐

#### 1.1 Update Model Type Definitions

**File**: `src/sections/chat/types.ts`

```typescript
// Current (lines 89-90)
export type GeminiModel = 'gemini-2.5-flash' | 'gemini-2.5-pro';

// New - Hybrid Model Support
export type AIModel = string; // Dynamic model IDs when multi-agents enabled

// 🎯 HYBRID APPROACH:
// • Flag OFF → Use existing 2 hardcoded Gemini models (backward compatibility)
// • Flag ON → 100% API-driven with all providers

// Legacy models (preserved for backward compatibility)
export const LEGACY_MODELS = {
  GEMINI_2_5_FLASH: 'gemini-2.5-flash',
  GEMINI_2_5_PRO: 'gemini-2.5-pro',
} as const;

export type LegacyModel = (typeof LEGACY_MODELS)[keyof typeof LEGACY_MODELS];
```

#### 1.2 Update All Type References

**Files to Update**:

- `src/sections/chat/types.ts` - Replace `GeminiModel` with `AIModel`
- `src/sections/chat/hooks/use-chat.ts` - Update model parameter types
- `src/sections/projects/components/chat-panel/index.tsx` - Update state types
- `src/sections/projects/components/chat-panel/chat-panel-header.tsx` - Update props

### **Phase 2: Feature Flag Implementation** ⭐

#### 2.1 Add Multi-Agents Feature Flag

**File**: `src/types/features.ts`

```typescript
export enum AppFeatures {
  INTERNAL = 'internal',
  PROJECT_COLLABORATION = 'project-collaboration',
  LIVE_TRANSCRIBE = 'live-transcribe',
  MICROSOFT_LOGIN = 'microsoft-login',
  CHAT_PREFERENCES = 'chat-preferences',
  NEW_PREVIEW_EDITOR_FONT_SIZE_CONTROL = 'new-preview-editor-font-size-control',
  SUPPORT_CHAT_IMAGES = 'support-chat-images',
  MULTI_AGENTS = 'multi-agents', // ← NEW FLAG
}
```

### **Phase 3: Dynamic Model Loading & Metadata** ⭐

#### 3.1 Create Models API Hook

**New File**: `src/hooks/use-available-models.ts`

```typescript
import { useState, useEffect } from 'react';

export interface ModelInfo {
  id: string;
  name: string;
  provider: 'google' | 'openai' | 'anthropic';
  tier: 'economy' | 'balanced' | 'premium';
  description: string;
  features: {
    maxTokens: number;
    supportsImages: boolean;
    supportsStreaming: boolean;
  };
}

interface ModelsResponse {
  success: boolean;
  data: {
    models: {
      openai: ModelInfo[];
      anthropic: ModelInfo[];
      google: ModelInfo[];
    };
    all: ModelInfo[];
    total: number;
  };
}

export const useAvailableModels = () => {
  const [models, setModels] = useState<ModelInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchModels = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch('/api/agent/models', {
          headers: {
            Authorization: `Bearer ${await getAuthToken()}`,
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch available models');
        }

        const result: ModelsResponse = await response.json();
        setModels(result.data.all);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch models');
        console.error('Error fetching models:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchModels();
  }, []);

  const getModelsByProvider = (provider: string) =>
    models.filter((model) => model.provider === provider);

  const getModelById = (id: string) => models.find((model) => model.id === id);

  return {
    models,
    loading,
    error,
    getModelsByProvider,
    getModelById,
    refetch: () => fetchModels(),
  };
};
```

### **Phase 4: UI Component Updates** ⭐

#### 4.1 Enhanced ChatPanelHeader Component

**File**: `src/sections/projects/components/chat-panel/chat-panel-header.tsx`

**Current Interface**:

```typescript
interface ChatPanelHeaderProps {
  selectedModel: GeminiModelType; // ← Limited to 2 models
  onModelChange: (model: GeminiModelType) => void;
  onNewChat?: () => void;
  disabled?: boolean;
  hasMessages?: boolean;
}
```

**New Interface**:

```typescript
interface ChatPanelHeaderProps {
  selectedModel: AIModel; // ← Supports all models
  onModelChange: (model: AIModel) => void;
  onNewChat?: () => void;
  disabled?: boolean;
  hasMessages?: boolean;
}
```

**New Component Structure**:

```typescript
const ChatPanelHeader: React.FC<ChatPanelHeaderProps> = ({
  selectedModel,
  onModelChange,
  onNewChat,
  disabled = false,
  hasMessages = false,
}) => {
  const { isFlagEnabled } = useFeatureFlags();
  const isMultiAgentsEnabled = isFlagEnabled(AppFeatures.MULTI_AGENTS);

  // 📌 LEGACY MODE: Use existing switch component (zero changes to current UX)
  if (!isMultiAgentsEnabled) {
    return <LegacyModelSwitch {...props} />;
  }

  // 🚀 MULTI-AGENT MODE: New dropdown for all providers
  return (
    <Stack direction="row" alignItems="center" justifyContent="space-between">
      <Stack direction="row" alignItems="center" gap={2}>
        <Typography variant="subtitle2">Chat</Typography>

        <ModelSelector
          selectedModel={selectedModel}
          onModelChange={onModelChange}
          disabled={disabled}
        />
      </Stack>

      {/* New chat button */}
      {onNewChat && hasMessages && (
        <IconButton onClick={onNewChat} disabled={disabled} size="small">
          <Iconify icon="material-symbols:add-comment-rounded" />
        </IconButton>
      )}
    </Stack>
  );
};
```

#### 4.2 New ModelSelector Component

**New File**: `src/sections/projects/components/chat-panel/model-selector.tsx`

```typescript
interface ModelSelectorProps {
  selectedModel: AIModel;
  onModelChange: (model: AIModel) => void;
  disabled?: boolean;
}

export const ModelSelector: React.FC<ModelSelectorProps> = ({
  selectedModel,
  onModelChange,
  disabled = false,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const { models, loading, error, getModelById } = useAvailableModels();

  const currentModelInfo = getModelById(selectedModel);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleModelSelect = (modelId: string) => {
    onModelChange(modelId as AIModel);
    handleClose();
  };

  // Loading state
  if (loading) {
    return (
      <Button
        variant="outlined"
        size="small"
        disabled
        sx={{ minWidth: 140 }}
      >
        <CircularProgress size={16} sx={{ mr: 1 }} />
        Loading...
      </Button>
    );
  }

  // Error state
  if (error) {
    return (
      <Button
        variant="outlined"
        size="small"
        disabled
        color="error"
        sx={{ minWidth: 140 }}
      >
        <ErrorIcon sx={{ mr: 1 }} fontSize="small" />
        Error
      </Button>
    );
  }

  return (
    <>
      <Button
        variant="outlined"
        size="small"
        onClick={handleClick}
        disabled={disabled || !currentModelInfo}
        endIcon={<KeyboardArrowDownIcon />}
        sx={{ minWidth: 140 }}
      >
        <Stack direction="row" alignItems="center" spacing={1}>
          <ProviderIcon provider={currentModelInfo?.provider || 'google'} />
          <Typography variant="caption">
            {currentModelInfo?.name || selectedModel}
          </Typography>
        </Stack>
      </Button>

      <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={handleClose}>
        {models.map((model) => (
          <MenuItem
            key={model.id}
            onClick={() => handleModelSelect(model.id)}
            selected={model.id === selectedModel}
          >
            <ListItemIcon>
              <ProviderIcon provider={model.provider} />
            </ListItemIcon>
            <ListItemText
              primary={model.name}
              secondary={model.description}
            />
            <Chip
              label={model.tier}
              size="small"
              color={model.tier === 'premium' ? 'primary' : 'default'}
            />
          </MenuItem>
        ))}
      </Menu>
    </>
  );
};
```

### **Phase 5: Backend Integration Updates** ⭐

#### 5.1 Update Chat Hook

**File**: `src/sections/chat/hooks/use-chat.ts`

**Current sendMessage API call** (around line 351):

```typescript
const requestBody: any = {
  model: messageModel, // ← Currently only Gemini models
  message,
  resources: messageResources,
  preferences: {
    instructions: preferences.userInstructions,
    ragSearchEnabled: preferences.enableSearchQuery,
  },
};
```

**New sendMessage** - No changes needed! Backend already supports all models:

```typescript
const requestBody: any = {
  model: messageModel, // ← Now supports all AIModel types
  message,
  resources: messageResources,
  preferences: {
    instructions: preferences.userInstructions,
    ragSearchEnabled: preferences.enableSearchQuery,
  },
};
// Backend automatically routes to correct provider!
```

#### 5.2 Update Default Model Selection

**Files**:

- `src/sections/projects/components/chat-panel/index.tsx`
- `src/sections/chat/components/chat-dialog.tsx`

**Current**:

```typescript
const [selectedModel, setSelectedModel] = useState<GeminiModelType>('gemini-2.5-flash');
```

**New**:

```typescript
const { isFlagEnabled } = useFeatureFlags();
const isMultiAgentsEnabled = isFlagEnabled(AppFeatures.MULTI_AGENTS);

// 🎯 HYBRID MODEL SELECTION STRATEGY
if (!isMultiAgentsEnabled) {
  // 📌 LEGACY MODE: Use existing 2 hardcoded Gemini models (no API needed!)
  const [selectedModel, setSelectedModel] = useState<LegacyModel>(LEGACY_MODELS.GEMINI_2_5_FLASH);

  // No API calls needed - use existing switch logic exactly as today
  return { selectedModel, setSelectedModel };
}

// 🚀 MULTI-AGENT MODE: 100% API-driven
const { models, loading } = useAvailableModels();

const getDefaultModel = (): AIModel => {
  if (models.length === 0) return ''; // Wait for API response

  // Prefer balanced models (GPT-4o-mini or similar)
  const balancedModel = models.find((m) => m.tier === 'balanced' || m.id.includes('4o-mini'));
  if (balancedModel) return balancedModel.id;

  // Fallback to first available model
  return models[0].id;
};

const [selectedModel, setSelectedModel] = useState<AIModel>('');

// Initialize model selection when API data loads
useEffect(() => {
  if (!loading && models.length > 0 && !selectedModel) {
    setSelectedModel(getDefaultModel());
  }
}, [models, loading]);
```

### **Phase 6: Backward Compatibility & Migration** ⭐

#### 6.1 Legacy Support Component

**New File**: `src/sections/projects/components/chat-panel/legacy-model-switch.tsx`

```typescript
// 📌 PRESERVES EXISTING BEHAVIOR - No API calls, no changes to current UX
interface LegacyModelSwitchProps {
  selectedModel: LegacyModel;
  onModelChange: (model: LegacyModel) => void;
  disabled?: boolean;
}

export const LegacyModelSwitch: React.FC<LegacyModelSwitchProps> = ({
  selectedModel,
  onModelChange,
  disabled = false,
}) => {
  // Use hardcoded values exactly as current implementation
  const isGeminiPro = selectedModel === LEGACY_MODELS.GEMINI_2_5_PRO;

  const handleSwitchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newModel: LegacyModel = event.target.checked
      ? LEGACY_MODELS.GEMINI_2_5_PRO
      : LEGACY_MODELS.GEMINI_2_5_FLASH;
    onModelChange(newModel);
  };

  return (
    <Tooltip title="Advanced mode" arrow placement="top">
      <FormControlLabel
        label=""
        control={
          <Switch
            checked={isGeminiPro}
            onChange={handleSwitchChange}
            size="small"
            disabled={disabled}
          />
        }
        sx={{ m: 0 }}
      />
    </Tooltip>
  );
};
```

---

## 🧪 Testing Strategy

### **Unit Tests** ✅

- **ModelSelector Component**: Dropdown rendering, model selection
- **ChatPanelHeader**: Feature flag toggle behavior
- **Model Registry**: Correct model metadata

### **Integration Tests** ✅

- **Chat Flow**: Send messages with different models
- **Feature Flag**: Toggle behavior between modes
- **Backward Compatibility**: Legacy mode functionality

### **E2E Tests** ✅

- **Model Selection**: End-to-end model switching
- **Multi-Provider Chat**: Successful communication with all providers
- **Feature Flag Rollout**: Gradual enablement testing

---

## 🔄 Migration Strategy

### **Phase A: Development (Feature Flag OFF)**

1. ✅ Implement all new components and types
2. ✅ Maintain 100% backward compatibility
3. ✅ Add comprehensive testing
4. ✅ Deploy with feature flag disabled

### **Phase B: Beta Testing (Feature Flag ON for Internal)**

1. ✅ Enable flag for `AppFeatures.INTERNAL` users
2. ✅ Test multi-model functionality
3. ✅ Monitor performance and user feedback
4. ✅ Fix any issues discovered

### **Phase C: Gradual Rollout (Feature Flag ON for Selected Users)**

1. ✅ Enable for specific user cohorts
2. ✅ Monitor usage patterns and error rates
3. ✅ Collect user feedback on model preferences
4. ✅ Expand rollout based on metrics

### **Phase D: Full Rollout (Feature Flag ON for All)**

1. ✅ Enable for all users
2. ✅ Remove legacy code after stabilization
3. ✅ Update documentation and training materials

---

## 📈 Success Metrics

### **Technical Metrics** 📊

- ✅ **Zero Breaking Changes**: All existing functionality preserved
- ✅ **Response Time**: < 200ms latency for model switching
- ✅ **Error Rate**: < 0.1% for multi-model requests
- ✅ **Feature Flag Coverage**: 100% rollback capability

### **User Experience Metrics** 📊

- ✅ **Model Adoption**: Usage distribution across providers
- ✅ **User Satisfaction**: Feedback on model selection UX
- ✅ **Feature Discovery**: % of users trying new models
- ✅ **Performance Perception**: User-reported response quality

---

## 🚨 Risk Mitigation

### **Risk 1: API Availability Issues** ⚠️

- **Mitigation**: Robust error handling in `useAvailableModels` hook
- **Fallback**: Legacy model selection when API fails
- **Detection**: API response monitoring and user-friendly error states

### **Risk 2: User Confusion with New UI** ⚠️

- **Mitigation**: Progressive disclosure, tooltips, help text
- **Fallback**: Revert to simple switch
- **Detection**: User behavior analytics

### **Risk 3: Model Response Quality Variations** ⚠️

- **Mitigation**: Model performance benchmarking
- **Fallback**: Smart default model recommendations
- **Detection**: User feedback collection

---

## 📋 Implementation Checklist

### **Phase 1: Hybrid Foundation**

- [ ] Update `AIModel` type definition (dynamic string for multi-agent mode)
- [ ] Create `LEGACY_MODELS` constants for backward compatibility
- [ ] Create `useAvailableModels` hook for API integration (flag ON only)
- [ ] Update all type references across codebase
- [ ] Add `MULTI_AGENTS` feature flag

### **Phase 2: UI Components**

- [ ] Create `ModelSelector` dropdown component
- [ ] Update `ChatPanelHeader` with conditional rendering
- [ ] Create `LegacyModelSwitch` for backward compatibility
- [ ] Add provider icons and model metadata display

### **Phase 3: Integration**

- [ ] Integrate `useAvailableModels` hook in components
- [ ] Update chat hooks to handle all model types
- [ ] Implement smart default model selection
- [ ] Add feature flag checks throughout
- [ ] Update conversation state management

### **Phase 4: Hybrid Mode Testing**

- [ ] **Legacy Mode Tests**: Verify existing switch behavior unchanged (flag OFF)
- [ ] **Multi-Agent Mode Tests**: API integration and dynamic model selection (flag ON)
- [ ] Unit tests for `useAvailableModels` hook (API responses, loading, errors)
- [ ] Component tests for `ModelSelector` with mocked API data
- [ ] Component tests for `LegacyModelSwitch` with hardcoded models
- [ ] E2E tests for feature flag toggle between modes
- [ ] API failure graceful degradation testing
- [ ] Backward compatibility validation

### **Phase 5: Deployment**

- [ ] Deploy with feature flag disabled
- [ ] Enable for internal testing
- [ ] Monitor metrics and feedback
- [ ] Gradual rollout to all users

---

## 📚 Documentation Updates

- [ ] Update API documentation with new model support
- [ ] Create user guide for model selection
- [ ] Update developer documentation
- [ ] Create troubleshooting guide

---

This implementation plan ensures **zero downtime**, **complete backward compatibility**, and **seamless integration** with the new multi-provider backend service while providing users with a rich model selection experience.

## 🔑 Key Implementation Highlights

### 🎯 **Hybrid Architecture - Best of Both Worlds!**

**🔒 Legacy Mode (Flag OFF)**:

- **🛡️ Zero Risk**: Preserves existing 2 hardcoded Gemini models exactly
- **⚡ No API Overhead**: Zero additional API calls or loading states
- **👥 User Continuity**: Current users see no changes whatsoever
- **🔄 Existing Performance**: Maintains current fast, instant model switching

**🚀 Multi-Agent Mode (Flag ON)**:

- **📡 Dynamic Discovery**: All models fetched from `GET /agent/models` API
- **🔄 Self-Updating**: Backend adds new models → Frontend gets them instantly
- **📊 Rich Metadata**: Provider, tier, description, features from single source
- **⚡ Smart Defaults**: Intelligent model selection based on API characteristics
- **🛡️ Unlimited Scale**: Never need frontend updates for new models

### 🎯 **Benefits of This Approach**

- **Maintainability**: Backend team controls model availability centrally
- **Scalability**: Supports unlimited models without frontend changes
- **Consistency**: Single source of truth for all model information
- **Flexibility**: Model metadata can evolve without breaking frontend
- **Developer Experience**: No more model list synchronization between services

## 🏆 **Why This Hybrid Approach is Superior**

### **Our Hybrid Implementation**:

**🔒 Legacy Mode (Flag OFF)**:

```typescript
✅ // Preserved existing behavior - zero changes for current users
const [selectedModel, setSelectedModel] = useState('gemini-2.5-flash');
// Existing users see exact same experience as today
```

**🚀 Multi-Agent Mode (Flag ON)**:

```typescript
✅ // Dynamic API-driven - unlimited scalability for new users
const { models } = useAvailableModels(); // Live data from backend
// New models appear automatically without frontend updates!
```

### **Traditional All-Hardcoded Approach (Avoided)**:

```typescript
❌ // Frontend hardcodes ALL models for both modes
const MODELS = {
  'gpt-4': { name: 'GPT-4', provider: 'openai' },
  'claude-3': { name: 'Claude 3', provider: 'anthropic' },
  'gemini-2.5-flash': { name: 'Gemini Flash', provider: 'google' }
}
// Problem: Must update frontend for every new model across all modes!
```

### **Real-World Impact**:

- 🔄 **Backend team adds GPT-5** → Frontend users see it immediately
- 📊 **Model tiers change** → UI updates without code deployment
- ⚡ **New provider added** → Instant availability across all frontends
- 🛡️ **Model deprecated** → Automatic removal from UI options

## 🏆 **Perfect Migration Strategy**

This hybrid implementation represents the **gold standard** for feature migrations:

- **🛡️ Risk-Free**: Existing users are completely unaffected
- **🚀 Innovation-Ready**: New capabilities available via feature flag
- **📈 Gradual Rollout**: Can enable multi-agent mode incrementally
- **🔄 Reversible**: Can instantly fall back to legacy mode if needed
- **⚡ Performance-Conscious**: No API overhead for users who don't need it

This approach establishes a **modern, scalable pattern** that other frontend teams can follow for similar backward-compatible feature integrations.
