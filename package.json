{"name": "aida", "version": "1.0.0", "private": true, "scripts": {"dev": "vite dev --cors", "start": "vite preview", "build:production": "tsc && NODE_OPTIONS='--max_old_space_size=8192' vite build --mode production", "build:development": "tsc && NODE_OPTIONS='--max_old_space_size=8192' vite build --mode development", "build:staging": "tsc && NODE_OPTIONS='--max_old_space_size=8192' vite build --mode staging", "deploy": "firebase deploy", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "lint:print": "npx eslint --print-config eslint.config.mjs > eslint-show-config.json", "fm:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx}\"", "fm:fix": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "fix:all": "npm run lint:fix && npm run fm:fix", "clean": "rm -rf node_modules .next out dist build"}, "engines": {"node": "22.x"}, "dependencies": {"@emotion/cache": "^11.13.5", "@emotion/react": "^11.13.5", "@emotion/styled": "^11.13.5", "@fontsource-variable/dm-sans": "^5.1.0", "@fontsource-variable/inter": "^5.1.0", "@fontsource-variable/nunito-sans": "^5.1.0", "@fontsource-variable/public-sans": "^5.1.1", "@fontsource/barlow": "^5.1.0", "@hookform/resolvers": "^3.9.1", "@iconify/react": "^5.0.2", "@mui/icons-material": "^6.4.3", "@mui/joy": "^5.0.0-beta.51", "@mui/lab": "^6.0.0-beta.18", "@mui/material": "^6.1.10", "@mui/x-data-grid": "^7.23.1", "@mui/x-date-pickers": "^7.23.1", "@mui/x-tree-view": "^7.23.0", "@reduxjs/toolkit": "^2.5.0", "@rtk-query/graphql-request-base-query": "^2.3.1", "@sentry/react": "^9.8.0", "@sentry/vite-plugin": "^3.2.2", "@speechmatics/browser-audio-input": "^2.0.1", "@speechmatics/browser-audio-input-react": "^2.0.1", "@speechmatics/real-time-client-react": "^1.0.0", "@tiptap/extension-code-block-lowlight": "^2.11.7", "@tiptap/extension-image": "^2.11.7", "@tiptap/extension-link": "^2.11.7", "@tiptap/extension-placeholder": "^2.11.7", "@tiptap/extension-table": "^2.14.0", "@tiptap/extension-table-cell": "^2.14.0", "@tiptap/extension-table-header": "^2.14.0", "@tiptap/extension-table-row": "^2.14.0", "@tiptap/extension-text-align": "^2.11.7", "@tiptap/extension-underline": "^2.11.7", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "@types/lodash": "^4.17.16", "@vidstack/react": "^1.12.13", "@wavesurfer/react": "^1.0.9", "autosuggest-highlight": "^3.3.4", "axios": "^1.7.9", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dexie": "^4.0.11", "docx": "^9.2.0", "es-toolkit": "^1.29.0", "eventemitter3": "^5.0.1", "file-saver": "^2.0.5", "firebase": "^11.8.1", "framer-motion": "^11.13.1", "graphql": "^16.10.0", "graphql-request": "^7.1.2", "gsap": "^3.13.0", "i18next": "^24.2.0", "i18next-browser-languagedetector": "^8.0.2", "i18next-resources-to-backend": "^1.2.1", "lodash": "^4.17.21", "marked": "^15.0.7", "minimal-shared": "^1.0.3", "nprogress": "^0.2.0", "posthog-js": "^1.164.3", "qs": "^6.14.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.53.2", "react-i18next": "^15.4.0", "react-markdown": "^9.0.3", "react-pdf": "^9.2.1", "react-player": "^2.16.0", "react-redux": "^9.2.0", "react-router": "^7.0.2", "redux-persist": "^6.0.0", "rehype-highlight": "^7.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "simplebar-react": "^3.2.6", "sonner": "^1.7.1", "stylis": "^4.3.4", "stylis-plugin-rtl": "^2.1.1", "swr": "^2.3.0", "turndown": "^7.2.0", "usehooks-ts": "^3.1.1", "uuid": "^11.0.5", "vidstack": "^0.6.15", "wavesurfer.js": "^7.9.2", "yet-another-react-lightbox": "^3.21.7", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.16.0", "@graphql-codegen/cli": "^5.0.5", "@graphql-codegen/introspection": "^4.0.3", "@graphql-codegen/typescript": "^4.1.5", "@graphql-codegen/typescript-operations": "^4.5.1", "@graphql-codegen/typescript-rtk-query": "^3.1.1", "@types/autosuggest-highlight": "^3.2.3", "@types/file-saver": "^2.0.7", "@types/node": "^22.10.1", "@types/nprogress": "^0.2.3", "@types/qs": "^6.9.18", "@types/react": "^18.3.13", "@types/react-dom": "^18.3.1", "@types/stylis": "^4.2.7", "@types/turndown": "^5.0.5", "@types/webpack-env": "^1.18.8", "@typescript-eslint/parser": "^8.17.0", "@vitejs/plugin-react-swc": "^3.7.2", "eslint": "^9.16.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-perfectionist": "^4.2.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.13.0", "prettier": "^3.4.2", "typescript": "^5.7.2", "typescript-eslint": "^8.17.0", "vite": "^6.0.3", "vite-plugin-checker": "^0.8.0", "vite-plugin-static-copy": "^2.3.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}