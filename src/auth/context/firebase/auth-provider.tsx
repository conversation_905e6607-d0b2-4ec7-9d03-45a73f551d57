import type { UserInfo } from 'firebase/auth';

import { doc, getDoc } from 'firebase/firestore';
import { useSetState } from 'minimal-shared/hooks';
import { onAuthStateChanged } from 'firebase/auth';
import { useMemo, useEffect, useCallback } from 'react';

import useAnalytics from 'src/hooks/analytics';

import { AUTH, FIRESTORE } from 'src/lib/firebase';

import { AuthContext } from '../auth-context';

import type { AuthState } from '../../types';

// ----------------------------------------------------------------------

/**
 * NOTE:
 * We only build demo at basic level.
 * Customer will need to do some extra handling yourself if you want to extend the logic and other features...
 */

type Props = {
  children: React.ReactNode;
};

export function AuthProvider({ children }: Props) {
  const { identify } = useAnalytics();
  const { state, setState } = useSetState<AuthState>({ user: null, loading: true });

  const checkUserSession = useCallback(async () => {
    try {
      onAuthStateChanged(AUTH, async (user: AuthState['user']) => {
        const isTrustedProvider = user?.providerData.some(
          (provider: UserInfo) => provider.providerId === 'microsoft.com'
        )
        if (user && (user.emailVerified || isTrustedProvider)) {
          /*
           * (1) If skip emailVerified
           * Remove the condition (if/else) : user.emailVerified
           */
          const userProfile = doc(FIRESTORE, 'users', user.uid);

          const docSnap = await getDoc(userProfile);

          const profileData = docSnap.data();

          setState({ user: { ...user, ...profileData }, loading: false });

          // Identify user in posthog
          identify({
            userId: user.uid,
            email: user.email,
            displayName: user?.displayName,
            createdDate: user?.metadata?.creationTime,
          });
        } else {
          setState({ user: null, loading: false });
        }
      });
    } catch (error) {
      console.error(error);
      setState({ user: null, loading: false });
    }
  }, [setState]);

  useEffect(() => {
    checkUserSession();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // ----------------------------------------------------------------------

  const checkAuthenticated = state.user ? 'authenticated' : 'unauthenticated';

  const status = state.loading ? 'loading' : checkAuthenticated;

  const memoizedValue = useMemo(
    () => ({
      user: state.user
        ? {
            ...state.user,
            id: state.user?.uid,
            accessToken: state.user?.accessToken,
            displayName: state.user?.displayName,
            photoURL: state.user?.photoURL,
            role: state.user?.role ?? 'admin',
          }
        : null,
      checkUserSession,
      loading: status === 'loading',
      authenticated: status === 'authenticated',
      unauthenticated: status === 'unauthenticated',
    }),
    [checkUserSession, state.user, status]
  );

  return <AuthContext.Provider value={memoizedValue}>{children}</AuthContext.Provider>;
}
