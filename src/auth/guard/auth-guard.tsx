import { useState, useEffect } from 'react';

import { paths } from 'src/routes/paths';
import { useRouter, usePathname, useSearchParams } from 'src/routes/hooks';

import { useCreateProjectDefaultMutation } from 'src/store/api/projects';

import { SplashScreen } from 'src/components/loading-screen';

import { useAuthContext } from '../hooks';

// ----------------------------------------------------------------------

type AuthGuardProps = {
  children: React.ReactNode;
};

export function AuthGuard({ children }: AuthGuardProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [createProjectDefault] = useCreateProjectDefaultMutation();

  const { authenticated, loading } = useAuthContext();

  const [isChecking, setIsChecking] = useState<boolean>(true);

  const createRedirectPath = (currentPath: string) => {
    let _pathname = pathname;
    const currentHash = window.location.hash;

    if (_pathname.includes(paths.inviteProject.acceptInvitation)) {
      const queryCode = searchParams.get('code');
      const projectId = searchParams.get('projectId');
      _pathname = `${_pathname}?code=${queryCode}&projectId=${projectId}`;
    } else if (_pathname.includes(paths.inviteProject.root)) {
      const queryToken = searchParams.get('token');
      _pathname = `${_pathname}?token=${queryToken}`;
    }

    // Include hash in the returnTo parameter
    const fullReturnPath = _pathname + currentHash;
    const queryString = new URLSearchParams({ 
      returnTo: encodeURIComponent(fullReturnPath) 
    }).toString();

    return `${currentPath}?${queryString}`;
  };

  const checkPermissions = async (): Promise<void> => {
    if (loading) {
      return;
    }

    if (!authenticated) {
      const signInPath = paths.auth.signIn;
      const redirectPath = createRedirectPath(signInPath);

      router.replace(redirectPath);
      return;
    }

    console.log('authenticated');

    // In case the user is authenticated, create a default project for them
    await createProjectDefault({});

    setIsChecking(false);
  };

  useEffect(() => {
    checkPermissions();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [authenticated, loading]);

  if (isChecking) {
    return <SplashScreen />;
  }

  return <>{children}</>;
}
