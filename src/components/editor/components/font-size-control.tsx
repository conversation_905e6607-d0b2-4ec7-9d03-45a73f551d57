import { useState, useEffect } from 'react';

import Box from '@mui/material/Box';
import Menu from '@mui/material/Menu';
import { Button } from '@mui/material';
import Divider from '@mui/material/Divider';
import TextField from '@mui/material/TextField';
import { listClasses } from '@mui/material/List';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import { buttonBaseClasses } from '@mui/material/ButtonBase';

import useFeatureFlags from 'src/hooks/feature-flags';

import { AppFeatures } from 'src/types';

import { Iconify } from '../../iconify';
import { ToolbarItem } from './toolbar-item';

import type { EditorToolbarProps } from '../types';

// ----------------------------------------------------------------------

const FONT_SIZE_OPTIONS = [8, 10, 12, 14, 16, 18, 20, 24, 28, 32, 36, 48];

export function FontSizeControl({ editor }: Pick<EditorToolbarProps, 'editor'>) {
  const { isFlagEnabled } = useFeatureFlags();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [inputValue, setInputValue] = useState<string>('');
  const [isFocused, setIsFocused] = useState(false);

  const handleDropdownClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleDropdownClose = () => {
    setAnchorEl(null);
  };

  const getCurrentFontSize = (): string => {
    if (!editor) return '14';

    // Get current fontSize from textStyle attributes
    const attributes = editor.getAttributes('textStyle');
    if (attributes.fontSize) {
      // Remove 'px' from the value for display
      return attributes.fontSize.replace('px', '');
    }

    // Check if we're in a heading and get its default size
    if (editor.isActive('heading', { level: 1 })) return '32';
    if (editor.isActive('heading', { level: 2 })) return '28';
    if (editor.isActive('heading', { level: 3 })) return '24';
    if (editor.isActive('heading', { level: 4 })) return '20';
    if (editor.isActive('heading', { level: 5 })) return '18';
    if (editor.isActive('heading', { level: 6 })) return '16';

    // Default font size for paragraph
    return '14';
  };

  const setFontSize = (size: number | string) => {
    if (!editor) return;

    const fontSize = typeof size === 'number' ? `${size}px` : `${size}px`;
    editor.chain().focus().setFontSize(fontSize).run();
  };

  const unsetFontSize = () => {
    if (!editor) return;
    editor.chain().focus().unsetFontSize().run();
  };

  // Update input value when editor selection changes
  useEffect(() => {
    if (!isFocused) {
      setInputValue(getCurrentFontSize());
    }
  }, [editor?.state.selection, isFocused]);

  // Initialize input value
  useEffect(() => {
    setInputValue(getCurrentFontSize());
  }, [editor]);

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(event.target.value);
  };

  const handleInputSubmit = () => {
    const size = parseInt(inputValue, 10);
    if (!isNaN(size) && size > 0) {
      if (size >= 8 && size <= 200) {
        setFontSize(size);
      } else {
        // Reset to current value if out of range
        setInputValue(getCurrentFontSize());
      }
    } else if (inputValue === '' || inputValue === '0') {
      // Empty or 0 means reset to default
      unsetFontSize();
      setInputValue(getCurrentFontSize());
    } else {
      // Invalid input, reset to current value
      setInputValue(getCurrentFontSize());
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      handleInputSubmit();
      (event.target as HTMLInputElement).blur();
    } else if (event.key === 'Escape') {
      // Reset to current value on escape
      setInputValue(getCurrentFontSize());
      (event.target as HTMLInputElement).blur();
    }
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
    handleInputSubmit();
  };

  const handleDropdownSelect = (size: number) => {
    setFontSize(size);
    setInputValue(size.toString());
    handleDropdownClose();
  };

  const handleDefaultSelect = () => {
    unsetFontSize();
    setInputValue(getCurrentFontSize());
    handleDropdownClose();
  };

  if (!editor) {
    return null;
  }

  const currentSize = getCurrentFontSize();

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          position: 'relative',
        }}
      >
        <TextField
          size="small"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyPress}
          onFocus={handleFocus}
          onBlur={handleBlur}
          type="number"
          slotProps={{
            input: {
              startAdornment: isFlagEnabled(AppFeatures.NEW_PREVIEW_EDITOR_FONT_SIZE_CONTROL) ? (
                <InputAdornment sx={{ m: 0 }} position="start" component="div">
                  <Box sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                  }}>
                    <Button sx={{ p: 0, m: 0, minWidth: 0 }} onClick={() => setFontSize(parseInt(inputValue, 10) + 2)}>
                      <Iconify icon="material-symbols:arrow-drop-up-rounded" sx={{ width: 24, height: 16 }} />
                    </Button>
                    <Button sx={{ p: 0, m: 0, minWidth: 0 }} onClick={() => setFontSize(parseInt(inputValue, 10) - 2)}>
                      <Iconify icon="material-symbols:arrow-drop-down-rounded" sx={{ width: 24, height: 16 }} />
                    </Button>
                  </Box>
                </InputAdornment>
              ) : null,
              endAdornment: (
                <InputAdornment position="end" sx={{ ml: 0 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <Box
                      component="span"
                      sx={(theme) => ({
                        fontSize: 12,
                        color: theme.vars.palette.text.secondary,
                      })}
                    >
                      px
                    </Box>
                    <IconButton
                      size="small"
                      onClick={handleDropdownClick}
                      aria-label="Font size options"
                      aria-controls={anchorEl ? 'font-size-menu' : undefined}
                      aria-haspopup="true"
                      aria-expanded={anchorEl ? 'true' : undefined}
                      sx={(theme) => ({
                        width: 20,
                        height: 20,
                        borderRadius: 0.5,
                        '&:hover': {
                          backgroundColor: theme.vars.palette.action.hover,
                        },
                      })}
                    >
                      <Iconify
                        width={14}
                        icon={
                          anchorEl
                            ? 'material-symbols:keyboard-arrow-up'
                            : 'material-symbols:keyboard-arrow-down'
                        }
                      />
                    </IconButton>
                  </Box>
                </InputAdornment>
              ),
              inputProps: {
                min: 8,
                max: 200,
                step: 1,
              },
              style: {
                textAlign: 'center',
                fontSize: 14,
                paddingRight: 0,
                paddingLeft: 0,
              },
            },
          }}
          sx={(theme) => ({
            width: 'auto',
            pl: 0,
            '& .MuiOutlinedInput-root': {
              height: 32,
              borderRadius: 0.75,
            },
            '& .MuiOutlinedInput-input': {
              padding: '6px 8px',
              textAlign: 'center',
            },
          })}
          placeholder="14"
          title="Font size (8-200px)"
        />
      </Box>

      <Menu
        id="font-size-menu"
        anchorEl={anchorEl}
        open={!!anchorEl}
        onClose={handleDropdownClose}
        MenuListProps={{ 'aria-labelledby': 'font-size-options' }}
        slotProps={{
          paper: {
            sx: {
              width: 120,
              maxHeight: 400,
              [`& .${listClasses.root}`]: { gap: 0.5, display: 'flex', flexDirection: 'column' },
              [`& .${buttonBaseClasses.root}`]: {
                px: 1,
                width: 1,
                height: 34,
                borderRadius: 0.75,
                justifyContent: 'flex-start',
                '&:hover': { backgroundColor: 'action.hover' },
              },
            },
          },
        }}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
      >
        {/* Default/Reset option */}
        <ToolbarItem
          component="li"
          label="Default"
          active={!editor.getAttributes('textStyle').fontSize}
          onClick={handleDefaultSelect}
          sx={{ fontStyle: 'italic' }}
        />

        <Divider sx={{ my: 0.5 }} />

        {/* Predefined font sizes */}
        {FONT_SIZE_OPTIONS.map((size) => (
          <ToolbarItem
            component="li"
            key={size}
            label={`${size}px`}
            active={currentSize === size.toString()}
            onClick={() => handleDropdownSelect(size)}
            sx={{
              fontSize: Math.min(size * 0.8, 18), // Scale down for display
              fontWeight: currentSize === size.toString() ? 600 : 400,
            }}
          />
        ))}
      </Menu>
    </>
  );
}
