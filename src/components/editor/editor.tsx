import { common, createLowlight } from 'lowlight';
import LinkExtension from '@tiptap/extension-link';
import Underline from '@tiptap/extension-underline';
import { mergeClasses } from 'minimal-shared/utils';
import ImageExtension from '@tiptap/extension-image';
import TableExtension from '@tiptap/extension-table';
import StarterKitExtension from '@tiptap/starter-kit';
import TableRowExtension from '@tiptap/extension-table-row';
import TextAlignExtension from '@tiptap/extension-text-align';
import TextStyleExtension from '@tiptap/extension-text-style';
import TableCellExtension from '@tiptap/extension-table-cell';
import PlaceholderExtension from '@tiptap/extension-placeholder';
import TableHeaderExtension from '@tiptap/extension-table-header';
import { useState, useEffect, forwardRef, useCallback } from 'react';
import CodeBlockLowlightExtension from '@tiptap/extension-code-block-lowlight';
import { useEditor, EditorContent, ReactNodeViewRenderer } from '@tiptap/react';

import Box from '@mui/material/Box';
import Portal from '@mui/material/Portal';
import Backdrop from '@mui/material/Backdrop';
import { Stack, Collapse } from '@mui/material';
import FormHelperText from '@mui/material/FormHelperText';

import { Toolbar } from './toolbar';
import { EditorRoot } from './styles';
import { FontSize } from './extensions';
import { editorClasses } from './classes';
import { CodeHighlightBlock } from './components/code-highlight-block';

import type { EditorProps } from './types';

// ----------------------------------------------------------------------

export const Editor = forwardRef<HTMLDivElement, EditorProps>((props, ref) => {
  const {
    sx,
    error,
    onChange,
    slotProps,
    helperText,
    resetValue,
    className,
    editable = true,
    fullItem = false,
    value: content = '',
    placeholder = 'Write something awesome...',
    toolbarVisible = true,
    ...other
  } = props;

  const [fullScreen, setFullScreen] = useState(false);

  const handleToggleFullScreen = useCallback(() => {
    setFullScreen((prev) => !prev);
  }, []);

  const lowlight = createLowlight(common);

  const editor = useEditor({
    content,
    editable,
    immediatelyRender: false,
    shouldRerenderOnTransaction: false,
    extensions: [
      StarterKitExtension.configure({
        codeBlock: false,
        code: { HTMLAttributes: { class: editorClasses.content.codeInline } },
        heading: { HTMLAttributes: { class: editorClasses.content.heading } },
        horizontalRule: { HTMLAttributes: { class: editorClasses.content.hr } },
        listItem: { HTMLAttributes: { class: editorClasses.content.listItem } },
        blockquote: { HTMLAttributes: { class: editorClasses.content.blockquote } },
        bulletList: { HTMLAttributes: { class: editorClasses.content.bulletList } },
        orderedList: { HTMLAttributes: { class: editorClasses.content.orderedList } },
      }),
      Underline,
      TextStyleExtension.configure({ 
        mergeNestedSpanStyles: true,
        HTMLAttributes: {
          class: 'text-style-span',
        },
      }),
      FontSize,
      PlaceholderExtension.configure({
        placeholder,
        emptyEditorClass: editorClasses.content.placeholder,
      }),
      ImageExtension.configure({ HTMLAttributes: { class: editorClasses.content.image } }),
      TextAlignExtension.configure({ types: ['heading', 'paragraph'] }),
      LinkExtension.configure({
        autolink: true,
        openOnClick: false,
        HTMLAttributes: { class: editorClasses.content.link },
      }),
      CodeBlockLowlightExtension.extend({
        addNodeView() {
          return ReactNodeViewRenderer(CodeHighlightBlock);
        },
      }).configure({ lowlight, HTMLAttributes: { class: editorClasses.content.codeBlock } }),
      TableExtension.configure({
        resizable: true,
        HTMLAttributes: { class: editorClasses.content.table },
      }),
      TableRowExtension.configure({
        HTMLAttributes: { class: editorClasses.content.tableRow },
      }),
      TableHeaderExtension.configure({
        HTMLAttributes: { class: editorClasses.content.tableHeader },
      }),
      TableCellExtension.configure({
        HTMLAttributes: { class: editorClasses.content.tableCell },
      }),
    ],
    onUpdate({ editor: _editor }) {
      const html = _editor.getHTML();
      onChange?.(html);
    },

    ...other,
  });

  useEffect(() => {
    const timer = setTimeout(() => {
      if (editor?.isEmpty && content !== '<p></p>') {
        editor.commands.setContent(content);
      }
    }, 100);
    return () => clearTimeout(timer);
  }, [content, editor]);

  useEffect(() => {
    if (resetValue && !content) {
      editor?.commands.clearContent();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [content]);

  useEffect(() => {
    if (fullScreen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
  }, [fullScreen]);

  return (
    <Portal disablePortal={!fullScreen}>
      {fullScreen && <Backdrop open sx={[(theme) => ({ zIndex: theme.zIndex.modal - 1 })]} />}

      <Box
        {...slotProps?.wrapper}
        sx={[
          () => ({
            display: 'flex',
            flexDirection: 'column',
          }),
          ...(Array.isArray(slotProps?.wrapper?.sx)
            ? (slotProps?.wrapper?.sx ?? [])
            : [slotProps?.wrapper?.sx]),
        ]}
      >
        <EditorRoot
          error={!!error}
          disabled={false}
          fullScreen={fullScreen}
          className={mergeClasses([editorClasses.root, className])}
          sx={{
            ...sx,
            ...(!fullScreen && {
              overflow: 'hidden',
              position: 'relative',
            }),
            '& .minimal__editor__content__root': {
              pb: (theme) => theme.spacing(fullScreen ? 12 : 6)
            },
          }}
        >

          <EditorContent
            ref={ref}
            spellCheck="false"
            autoComplete="off"
            autoCapitalize="off"
            editor={editor}
            className={editorClasses.content.root}
          />
          <Stack direction="row" justifyContent="center" alignItems="center" sx={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            zIndex: 1,
            backgroundColor: 'transparent',
            ...(fullScreen && {
              pb: 4
            }),
          }}>
            <Collapse in={toolbarVisible} sx={{
              '&.MuiCollapse-root': {
                minHeight: 'initial !important',
              },
              [`& .${editorClasses.toolbar.root}`]: (theme) => ({
                borderRadius: 4,
                px: `${theme.spacing(2)} !important`,
                overflow: 'hidden',
                p: 1,
                mb: 1
              })
            }}>
              <Toolbar
                editor={editor}
                fullItem={fullItem}
                fullScreen={fullScreen}
                onToggleFullScreen={handleToggleFullScreen}
              />
            </Collapse>
          </Stack>
        </EditorRoot>

        {helperText && (
          <FormHelperText error={!!error} sx={{ px: 2 }}>
            {helperText}
          </FormHelperText>
        )}
      </Box>
    </Portal>
  );
});
