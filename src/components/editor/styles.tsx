import { varAlpha } from 'minimal-shared/utils';

import { styled } from '@mui/material/styles';

import { editorClasses } from './classes';

// ----------------------------------------------------------------------

const MARGIN = '0.75em';

type EditorRootProps = {
  error?: boolean;
  disabled?: boolean;
  fullScreen?: boolean;
};

export const EditorRoot = styled('div', {
  shouldForwardProp: (prop: string) => !['error', 'disabled', 'fullScreen', 'sx'].includes(prop),
})<EditorRootProps>(({ error, disabled, fullScreen, theme }) => ({
  minHeight: 240,
  display: 'flex',
  flexDirection: 'column',
  borderRadius: theme.shape.borderRadius,
  border: `solid 1px ${varAlpha(theme.vars.palette.grey['500Channel'], 0.2)}`,
  scrollbarWidth: 'thin',
  scrollbarColor: `${varAlpha(theme.vars.palette.text.disabledChannel, 0.4)} ${varAlpha(theme.vars.palette.text.disabledChannel, 0.08)}`,
  /**
   * State: error
   */
  ...(error && { border: `solid 1px ${theme.vars.palette.error.main}` }),
  /**
   * State: disabled
   */
  ...(disabled && { opacity: 0.48, pointerEvents: 'none' }),
  /**
   * State: fullScreen
   */
  ...(fullScreen && {
    top: 0,
    left: 0,
    position: 'fixed',
    zIndex: theme.zIndex.modal,
    maxHeight: 'unset !important',
    width: '100vw',
    height: '100vh',
    backgroundColor: theme.vars.palette.background.default,
    borderRadius: 0,
    boxShadow: 'none',
    transition: theme.transitions.create(
      ['width', 'height', 'top', 'left'],
      {
        easing: theme.transitions.easing.easeInOut,
        duration: theme.transitions.duration.standard,
      }
    ),
  }),
  /**
   * Placeholder
   */
  [`& .${editorClasses.content.placeholder}`]: {
    '&:first-of-type::before': {
      ...theme.typography.body2,
      height: 0,
      float: 'left',
      pointerEvents: 'none',
      content: 'attr(data-placeholder)',
      color: theme.vars.palette.text.disabled,
    },
  },
  /**
   * Content
   */
  [`& .${editorClasses.content.root}`]: {
    display: 'flex',
    flex: '1 1 auto',
    overflowY: 'auto',
    flexDirection: 'column',
    borderBottomLeftRadius: 'inherit',
    borderBottomRightRadius: 'inherit',
    backgroundColor: varAlpha(theme.vars.palette.grey['500Channel'], 0.08),
    ...(error && { backgroundColor: varAlpha(theme.vars.palette.error.mainChannel, 0.08) }),
    // Fullscreen specific styles
    ...(fullScreen && {
      flex: '1 1 auto',
      minHeight: 0,
      borderRadius: 0,
      backgroundColor: theme.vars.palette.background.default, // Clean background
    }),
    '& .tiptap': {
      '> * + *': { marginTop: 0, marginBottom: MARGIN },
      '&.ProseMirror': { 
        flex: '1 1 auto', 
        outline: 'none', 
        padding: theme.spacing(2, 3),
        lineHeight: 1.6,
        fontSize: '14px',
        fontFamily: theme.typography.fontFamily,
        // Fullscreen specific styles for ProseMirror
        ...(fullScreen && {
          padding: theme.spacing(4, 6, 12, 6), // Extra padding, especially bottom for floating toolbar
          margin: '0 auto', // Center the content
        }),
      },
      /**
       * Enhanced Document-style formatting
       */
      // Global text formatting
      '*': {
        lineHeight: 1.6,
      },
      // Better paragraph spacing for document-like appearance
      'p, div': {
        margin: '0 0 12px 0',
        lineHeight: 1.6,
        fontSize: '14px',
        color: theme.vars.palette.text.primary,
        '&:last-child': {
          marginBottom: 0,
        },
        // Handle nested content better
        '& > *': {
          lineHeight: 'inherit',
        },

      },
      // Preserve inline formatting
      'strong, b': {
        fontWeight: 600,
        color: theme.vars.palette.text.primary,
      },
      'em, i': {
        fontStyle: 'italic',
        color: theme.vars.palette.text.primary,
      },
      'u': {
        textDecoration: 'underline',
      },
      's, strike': {
        textDecoration: 'line-through',
      },
      // Better span handling for inline content
      'span': {
        lineHeight: 'inherit',
        // Preserve common text formatting but allow inline font-size
        '&[style*="font-weight"]': {
          fontWeight: 'inherit !important',
        },
        '&[style*="font-style"]': {
          fontStyle: 'inherit !important',
        },
        '&[style*="text-decoration"]': {
          textDecoration: 'inherit !important',
        },
        // Font-size should be preserved from inline styles (no override needed)
      },
      /**
       * Heading & Paragraph - Enhanced for document similarity
       */
      h1: { 
        ...theme.typography.h4, 
        marginTop: '24px', 
        marginBottom: '12px',
        fontWeight: 600,
        lineHeight: 1.3,
        color: theme.vars.palette.text.primary,
        '&:first-child': { marginTop: 0 },
      },
      h2: { 
        ...theme.typography.h5, 
        marginTop: '20px', 
        marginBottom: '10px',
        fontWeight: 600,
        lineHeight: 1.4,
        color: theme.vars.palette.text.primary,
        '&:first-child': { marginTop: 0 },
      },
      h3: { 
        ...theme.typography.h6, 
        marginTop: '18px', 
        marginBottom: '8px',
        fontWeight: 600,
        lineHeight: 1.4,
        color: theme.vars.palette.text.primary,
        '&:first-child': { marginTop: 0 },
      },
      h4: { 
        fontSize: '16px',
        fontWeight: 600,
        marginTop: '16px', 
        marginBottom: '8px',
        lineHeight: 1.4,
        color: theme.vars.palette.text.primary,
        '&:first-child': { marginTop: 0 },
      },
      h5: { 
        fontSize: '15px',
        fontWeight: 600,
        marginTop: '14px', 
        marginBottom: '6px',
        lineHeight: 1.4,
        color: theme.vars.palette.text.primary,
        '&:first-child': { marginTop: 0 },
      },
      h6: { 
        fontSize: '14px',
        fontWeight: 600,
        marginTop: '12px', 
        marginBottom: '6px',
        lineHeight: 1.4,
        color: theme.vars.palette.text.primary,
        '&:first-child': { marginTop: 0 },
      },
      [`& .${editorClasses.content.heading}`]: {},
      /**
       * Link - Enhanced for better visibility
       */
      [`& .${editorClasses.content.link}`]: { 
        color: theme.vars.palette.primary.main,
        textDecoration: 'underline',
        textDecorationColor: varAlpha(theme.vars.palette.primary.mainChannel, 0.4),
        '&:hover': {
          textDecorationColor: theme.vars.palette.primary.main,
        },
      },
      // Handle external links that might not have the class
      'a': {
        color: theme.vars.palette.primary.main,
        textDecoration: 'underline',
        textDecorationColor: varAlpha(theme.vars.palette.primary.mainChannel, 0.4),
        '&:hover': {
          textDecorationColor: theme.vars.palette.primary.main,
        },
      },
      /**
       * Hr Divider
       */
      [`& .${editorClasses.content.hr}`]: {
        flexShrink: 0,
        borderWidth: 0,
        margin: '20px 0',
        msFlexNegative: 0,
        WebkitFlexShrink: 0,
        borderStyle: 'solid',
        borderBottomWidth: 'thin',
        borderColor: theme.vars.palette.divider,
      },
      'hr': {
        flexShrink: 0,
        borderWidth: 0,
        margin: '20px 0',
        msFlexNegative: 0,
        WebkitFlexShrink: 0,
        borderStyle: 'solid',
        borderBottomWidth: 'thin',
        borderColor: theme.vars.palette.divider,
      },
      /**
       * Image - Enhanced for better document display
       */ 
      [`& .${editorClasses.content.image}`]: {
        width: '100%',
        height: 'auto',
        maxWidth: '100%',
        margin: '16px auto',
        borderRadius: theme.shape.borderRadius,
        display: 'block',
      },
      'img': {
        width: '100%',
        height: 'auto',
        maxWidth: '100%',
        margin: '16px auto',
        borderRadius: theme.shape.borderRadius,
        display: 'block',
      },
      /**
       * List - Enhanced for Confluence-like appearance
       */ 
      [`& .${editorClasses.content.bulletList}`]: { 
        paddingLeft: '24px', 
        margin: '12px 0',
        listStyleType: 'disc',
        '& li': {
          marginBottom: '6px',
          lineHeight: 1.6,
          '&:last-child': {
            marginBottom: 0,
          },
        },
      },
      [`& .${editorClasses.content.orderedList}`]: { 
        paddingLeft: '24px',
        margin: '12px 0',
        listStyleType: 'decimal',
        '& li': {
          marginBottom: '6px',
          lineHeight: 1.6,
          '&:last-child': {
            marginBottom: 0,
          },
        },
      },
      // Handle generic lists that might not have classes
      'ul': {
        paddingLeft: '24px',
        margin: '12px 0',
        listStyleType: 'disc',
        '& li': {
          marginBottom: '6px',
          lineHeight: 1.6,
          '&:last-child': {
            marginBottom: 0,
          },
        },
        // Nested lists
        '& ul': {
          listStyleType: 'circle',
          margin: '6px 0',
          '& ul': {
            listStyleType: 'square',
          },
        },
      },
      'ol': {
        paddingLeft: '24px',
        margin: '12px 0',
        listStyleType: 'decimal',
        '& li': {
          marginBottom: '6px',
          lineHeight: 1.6,
          '&:last-child': {
            marginBottom: 0,
          },
        },
        // Nested lists
        '& ol': {
          listStyleType: 'lower-alpha',
          margin: '6px 0',
          '& ol': {
            listStyleType: 'lower-roman',
          },
        },
      },
      [`& .${editorClasses.content.listItem}`]: { 
        lineHeight: 1.6, 
        marginBottom: '6px',
        '& > p': { 
          margin: 0,
          lineHeight: 1.6,
        },
        '&:last-child': {
          marginBottom: 0,
        },
      },
      'li': {
        lineHeight: 1.6,
        marginBottom: '6px',
        '& > p': {
          margin: 0,
          lineHeight: 1.6,
        },
        '&:last-child': {
          marginBottom: 0,
        },
      },
      /**
       * Blockquote - Enhanced for better document display
       */
      [`& .${editorClasses.content.blockquote}`]: {
        lineHeight: 1.6,
        fontSize: '14px',
        margin: '16px 0',
        position: 'relative',
        fontStyle: 'italic',
        padding: theme.spacing(2, 2, 2, 3),
        color: theme.vars.palette.text.secondary,
        borderLeft: `solid 4px ${theme.vars.palette.primary.main}`,
        backgroundColor: varAlpha(theme.vars.palette.primary.mainChannel, 0.04),
        borderRadius: '0 4px 4px 0',
        '& p': { 
          margin: '0 0 8px 0', 
          fontSize: 'inherit', 
          fontStyle: 'inherit',
          lineHeight: 1.6,
          '&:last-child': {
            marginBottom: 0,
          },
        },
      },
      'blockquote': {
        lineHeight: 1.6,
        fontSize: '14px',
        margin: '16px 0',
        position: 'relative',
        fontStyle: 'italic',
        padding: theme.spacing(2, 2, 2, 3),
        color: theme.vars.palette.text.secondary,
        borderLeft: `solid 4px ${theme.vars.palette.primary.main}`,
        backgroundColor: varAlpha(theme.vars.palette.primary.mainChannel, 0.04),
        borderRadius: '0 4px 4px 0',
        '& p': { 
          margin: '0 0 8px 0', 
          fontSize: 'inherit', 
          fontStyle: 'inherit',
          lineHeight: 1.6,
          '&:last-child': {
            marginBottom: 0,
          },
        },
      },
      /**
       * Code inline - Enhanced for better readability
       */
      [`& .${editorClasses.content.codeInline}`]: {
        padding: theme.spacing(0.25, 0.5),
        color: theme.vars.palette.text.primary,
        fontSize: '13px',
        fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
        borderRadius: theme.shape.borderRadius / 2,
        backgroundColor: varAlpha(theme.vars.palette.grey['500Channel'], 0.1),
        border: `1px solid ${varAlpha(theme.vars.palette.grey['500Channel'], 0.2)}`,
        ...theme.applyStyles('dark', {
          backgroundColor: varAlpha(theme.vars.palette.grey['500Channel'], 0.2),
          border: `1px solid ${varAlpha(theme.vars.palette.grey['500Channel'], 0.3)}`,
        }),
      },
      'code': {
        padding: theme.spacing(0.25, 0.5),
        color: theme.vars.palette.text.primary,
        fontSize: '13px',
        fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
        borderRadius: theme.shape.borderRadius / 2,
        backgroundColor: varAlpha(theme.vars.palette.grey['500Channel'], 0.1),
        border: `1px solid ${varAlpha(theme.vars.palette.grey['500Channel'], 0.2)}`,
        ...theme.applyStyles('dark', {
          backgroundColor: varAlpha(theme.vars.palette.grey['500Channel'], 0.2),
          border: `1px solid ${varAlpha(theme.vars.palette.grey['500Channel'], 0.3)}`,
        }),
      },
      /**
       * Code block
       */
      [`& .${editorClasses.content.codeBlock}`]: {
        position: 'relative',
        margin: '16px 0',
        '& pre': {
          overflowX: 'auto',
          color: theme.vars.palette.text.primary,
          padding: theme.spacing(3),
          borderRadius: theme.shape.borderRadius,
          backgroundColor: theme.vars.palette.grey[100],
          border: `1px solid ${theme.vars.palette.divider}`,
          fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
          lineHeight: 1.5,
          ...theme.applyStyles('dark', {
            backgroundColor: theme.vars.palette.grey[800],
            color: theme.vars.palette.common.white,
            border: `1px solid ${theme.vars.palette.grey[700]}`,
          }),
          '& code': { 
            fontSize: '13px',
            padding: 0,
            backgroundColor: 'transparent',
            border: 'none',
            color: 'inherit',
          },
        },
        [`& .${editorClasses.content.langSelect}`]: {
          top: 8,
          right: 8,
          zIndex: 1,
          padding: 4,
          outline: 'none',
          borderRadius: 4,
          position: 'absolute',
          color: theme.vars.palette.text.primary,
          fontWeight: theme.typography.fontWeightMedium,
          borderColor: varAlpha(theme.vars.palette.grey['500Channel'], 0.2),
          backgroundColor: varAlpha(theme.vars.palette.grey['500Channel'], 0.1),
          ...theme.applyStyles('dark', {
            color: theme.vars.palette.common.white,
            borderColor: varAlpha(theme.vars.palette.grey['500Channel'], 0.3),
            backgroundColor: varAlpha(theme.vars.palette.grey['500Channel'], 0.2),
          }),
        },
      },
      'pre': {
        position: 'relative',
        margin: '16px 0',
        overflowX: 'auto',
        color: theme.vars.palette.text.primary,
        padding: theme.spacing(3),
        borderRadius: theme.shape.borderRadius,
        backgroundColor: theme.vars.palette.grey[100],
        border: `1px solid ${theme.vars.palette.divider}`,
        fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
        lineHeight: 1.5,
        ...theme.applyStyles('dark', {
          backgroundColor: theme.vars.palette.grey[800],
          color: theme.vars.palette.common.white,
          border: `1px solid ${theme.vars.palette.grey[700]}`,
        }),
        '& code': { 
          fontSize: '13px',
          padding: 0,
          backgroundColor: 'transparent',
          border: 'none',
          color: 'inherit',
        },
      },
      /**
       * Table - Enhanced for better document display
       */
      [`& .${editorClasses.content.table}`]: {
        width: '100%',
        borderCollapse: 'collapse',
        border: `1px solid ${theme.vars.palette.divider}`,
        marginTop: '16px',
        marginBottom: '16px',
        borderRadius: theme.shape.borderRadius,
        overflow: 'hidden',
        fontSize: '14px',
      },
      'table': {
        width: '100%',
        borderCollapse: 'collapse',
        border: `1px solid ${theme.vars.palette.divider}`,
        marginTop: '16px',
        marginBottom: '16px',
        borderRadius: theme.shape.borderRadius,
        overflow: 'hidden',
        fontSize: '14px',
      },
      [`& .${editorClasses.content.tableHeader}`]: {
        backgroundColor: theme.vars.palette.grey[100],
        fontWeight: 600,
        color: theme.vars.palette.text.primary,
        padding: theme.spacing(1.5, 2),
        border: `1px solid ${theme.vars.palette.divider}`,
        textAlign: 'left',
        verticalAlign: 'top',
        lineHeight: 1.5,
        ...theme.applyStyles('dark', {
          backgroundColor: theme.vars.palette.grey[800],
        }),
      },
      'th': {
        backgroundColor: theme.vars.palette.grey[100],
        fontWeight: 600,
        color: theme.vars.palette.text.primary,
        padding: theme.spacing(1.5, 2),
        border: `1px solid ${theme.vars.palette.divider}`,
        textAlign: 'left',
        verticalAlign: 'top',
        lineHeight: 1.5,
        ...theme.applyStyles('dark', {
          backgroundColor: theme.vars.palette.grey[800],
        }),
      },
      [`& .${editorClasses.content.tableCell}`]: {
        padding: theme.spacing(1.5, 2),
        border: `1px solid ${theme.vars.palette.divider}`,
        textAlign: 'left',
        verticalAlign: 'top',
        lineHeight: 1.5,
      },
      'td': {
        padding: theme.spacing(1.5, 2),
        border: `1px solid ${theme.vars.palette.divider}`,
        textAlign: 'left',
        verticalAlign: 'top',
        lineHeight: 1.5,
      },
      [`& .${editorClasses.content.tableRow}`]: {
        '&:nth-of-type(odd)': {
          backgroundColor: theme.vars.palette.background.neutral,
        },
        '&:hover': {
          backgroundColor: varAlpha(theme.vars.palette.primary.mainChannel, 0.04),
        },
      },
      'tr': {
        '&:nth-of-type(odd)': {
          backgroundColor: theme.vars.palette.background.neutral,
        },
        '&:hover': {
          backgroundColor: varAlpha(theme.vars.palette.primary.mainChannel, 0.04),
        },
      },
      /**
       * Enhanced formatting for pasted content
       */
      // Handle common HTML elements that might come from external sources
      'div[style]': {
        // Preserve some inline styles that are commonly used
        '& p': {
          margin: '0 0 12px 0',
        },
      },
      // Better spacing for consecutive elements
      'p + ul, p + ol': {
        marginTop: '8px',
      },
      'ul + p, ol + p': {
        marginTop: '12px',
      },
      'h1 + p, h2 + p, h3 + p, h4 + p, h5 + p, h6 + p': {
        marginTop: '8px',
      },
      // Ensure proper text formatting is preserved
      '.confluence-content, .docs-content': {
        '& *': {
          lineHeight: 1.6,
          fontSize: '14px',
        },
      },
    },
  },
}));
