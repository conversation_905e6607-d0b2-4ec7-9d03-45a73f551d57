import React from 'react';
import * as Sentry from '@sentry/react';

import { ErrorView } from 'src/sections/error';

interface ErrorBoundaryProps {
  children: React.ReactNode;
  Fallback?: React.ComponentType<{ error: Error }>;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: ErrorBoundaryState) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error(error, { errorInfo });

    // Send error to Sentry with additional context
    Sentry.withScope((scope) => {
      scope.setTag('errorBoundary', true);
      scope.setContext('errorInfo', {
        componentStack: errorInfo.componentStack,
      });
      Sentry.captureException(error);
    });
  }

  render() {
    if (this.state.hasError && this.state.error) {
      const CustomErrorFallback = this.props.Fallback;
      if (CustomErrorFallback) {
        return <CustomErrorFallback error={this.state.error} />;
      }
      // You can render any custom fallback UI
      return this.props.Fallback || <ErrorView />;
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
