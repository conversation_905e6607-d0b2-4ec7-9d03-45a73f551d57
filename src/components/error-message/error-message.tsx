import type { ButtonProps } from '@mui/material/Button';

import { forwardRef } from 'react';

import { Box, Alert, Stack, Button, Typography, AlertTitle } from '@mui/material';

import { Iconify } from 'src/components/iconify';

interface ErrorMessageProps {
  error: any;
  title?: string;
  message?: string;
  onRetry?: () => void;
  retryButtonProps?: ButtonProps;
  showIcon?: boolean;
  severity?: 'error' | 'warning';
}

export const ErrorMessage = forwardRef<HTMLDivElement, ErrorMessageProps>(
  (
    {
      error,
      title = 'Something went wrong',
      message,
      onRetry,
      retryButtonProps,
      showIcon = true,
      severity = 'error',
    },
    ref
  ) => {
    const getErrorMessage = () => {
      if (message) return message;

      if (error?.message) return error.message;
      if (error?.data?.message) return error.data.message;
      if (typeof error === 'string') return error;

      return 'An unexpected error occurred. Please try again.';
    };

    return (
      <Box ref={ref}>
        <Alert
          severity={severity}
          sx={{
            '& .MuiAlert-message': {
              width: '100%',
            },
          }}
        >
          <AlertTitle>{title}</AlertTitle>

          <Stack spacing={2} sx={{ mt: 1 }}>
            <Typography variant="body2">{getErrorMessage()}</Typography>

            {onRetry && (
              <Box>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={onRetry}
                  startIcon={showIcon ? <Iconify icon="material-symbols:refresh" /> : undefined}
                  {...retryButtonProps}
                >
                  Try Again
                </Button>
              </Box>
            )}
          </Stack>
        </Alert>
      </Box>
    );
  }
);

ErrorMessage.displayName = 'ErrorMessage';
