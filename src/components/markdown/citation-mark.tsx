import type { Citation } from 'src/sections/chat/types';

import { useState } from 'react';

import { styled } from '@mui/material/styles';
import { Box, Chip, Tooltip, Typography } from '@mui/material';

import { useCitationContext } from 'src/contexts/citation-context';

// ----------------------------------------------------------------------

const CitationMarkRoot = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.primary.lighter,
  color: theme.palette.primary.contrastText,
  padding: '0 2px',
  borderRadius: 2,
  cursor: 'pointer',
  fontSize: '11px',
  fontWeight: 500,
  display: 'inline-block',
  minWidth: '11px',
  textAlign: 'center',
  lineHeight: 1,
  marginLeft: '2px',
  marginRight: '2px',
  '&:hover': {
    backgroundColor: theme.palette.primary.light,
    color: theme.palette.primary.contrastText,
  },
}));

const CitationTooltip = styled(({ className, ...props }: any) => (
  <Tooltip {...props} arrow classes={{ popper: className }} />
))(({ theme }) => ({
  '& .MuiTooltip-tooltip': {
    backgroundColor: theme.palette.background.paper,
    color: theme.palette.text.primary,
    maxWidth: 400, // Increased for better reading
    maxHeight: 300, // Fixed height to prevent overly tall tooltips
    fontSize: theme.typography.pxToRem(12),
    border: `1px solid ${theme.palette.divider}`,
    boxShadow: theme.shadows[8],
    padding: 0, // Remove padding to let inner content handle it
    overflow: 'hidden', // Hide overflow on the tooltip itself
    '& > *': {
      // Style the inner content box
      maxHeight: 300,
      overflowY: 'auto',
      padding: theme.spacing(1.5),
      // Custom scrollbar styling
      '&::-webkit-scrollbar': {
        width: 6,
      },
      '&::-webkit-scrollbar-track': {
        backgroundColor: theme.palette.action.hover,
        borderRadius: 3,
      },
      '&::-webkit-scrollbar-thumb': {
        backgroundColor: theme.palette.action.disabled,
        borderRadius: 3,
        '&:hover': {
          backgroundColor: theme.palette.action.focus,
        },
      },
    },
  },
}));

// ----------------------------------------------------------------------

interface CitationMarkProps {
  citation: Citation;
  citationNumber: number;
  onCitationClick?: (fileId: string, timestampSeconds?: number) => void;
}

export const CitationMark = ({ citation, citationNumber, onCitationClick }: CitationMarkProps) => {
  const { onCitationClick: contextCitationClick } = useCitationContext();
  
  // Use the provided onCitationClick prop or fall back to the context
  const citationClickHandler = onCitationClick || contextCitationClick;
  const [isHovered, setIsHovered] = useState(false);

  const handleCitationClick = (event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    
    // Find the first reference with a file_id that matches our criteria
    const reference = citation.references.find(ref => 
      ref.file?.metadata?.file_id || ref.file?.id
    );
    
    if (reference && citationClickHandler) {
      const fileId = reference.file?.metadata?.file_id || reference.file?.id;
      const timestampSeconds = reference.timestampSeconds;
      
      if (fileId) {
        citationClickHandler(fileId, timestampSeconds);
      }
    }
  };

  // Create tooltip content
  const tooltipContent = (
    <Box>
      {citation.references.map((ref, refIndex) => {
        const displayName =
          ref.file.displayName ||
          ref.file.metadata.originalFilename ||
          ref.file.metadata.original_filename ||
          ref.file.name;
        const textContent = ref.text || '';

        return (
          <Box key={ref.id} sx={{ mb: refIndex < citation.references.length - 1 ? 2 : 0 }}>
            {/* File name and type */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
              <Typography variant="subtitle2" sx={{ fontSize: '0.85rem', fontWeight: 600 }}>
                {displayName}
              </Typography>
              {ref.type && (
                <Chip
                  label={ref.type}
                  size="small"
                  variant="outlined"
                  sx={{
                    height: 18,
                    fontSize: '0.65rem',
                    textTransform: 'capitalize',
                  }}
                />
              )}
            </Box>

            {/* Text content if available */}
            {textContent && (
              <Typography
                variant="body2"
                sx={{
                  fontSize: '0.8rem',
                  color: 'text.secondary',
                  fontStyle: 'italic',
                  lineHeight: 1.4,
                  mb: 1,
                  whiteSpace: 'pre-wrap', // Preserve line breaks and whitespace
                }}
              >
                &ldquo;{textContent}&rdquo;
              </Typography>
            )}

            {/* Timestamp if available */}
            {import.meta.env.DEV && ref.timestampSeconds !== undefined && (
              <Typography
                variant="caption"
                sx={{
                  fontSize: '0.7rem',
                  color: 'text.secondary',
                  fontFamily: 'monospace',
                }}
              >
                Timestamp: {ref.timestampFormatted || `${Math.floor(ref.timestampSeconds / 60)}:${String(ref.timestampSeconds % 60).padStart(2, '0')}`}
              </Typography>
            )}
          </Box>
        );
      })}
    </Box>
  );

  return (
    <CitationTooltip
      title={tooltipContent}
      arrow
      placement="top"
      onOpen={() => setIsHovered(true)}
      onClose={() => setIsHovered(false)}
    >
      <CitationMarkRoot onClick={citationClickHandler ? handleCitationClick : undefined}>{citationNumber}</CitationMarkRoot>
    </CitationTooltip>
  );
};
