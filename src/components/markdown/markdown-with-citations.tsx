import type { Options } from 'react-markdown';
import type { Citation } from 'src/sections/chat/types';

import { useMemo } from 'react';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import rehypeHighlight from 'rehype-highlight';
import { mergeClasses } from 'minimal-shared/utils';

import { MarkdownRoot } from './styles';
import { markdownClasses } from './classes';
import { CitationMark } from './citation-mark';

import type { MarkdownProps } from './types';

// ----------------------------------------------------------------------

interface MarkdownWithCitationsProps extends MarkdownProps {
  citations?: Citation[];
}

// Plugins
const remarkPlugins = [[remarkGfm, { singleTilde: false }]];
const rehypePlugins = [rehypeRaw, rehypeHighlight];

// Helper function to convert [cite: ...] format to citation markers
function processCiteReferences(text: string, citations: Citation[]): string {
  if (!citations.length) return text;

  // Create a map of reference IDs to citation objects
  const refToCitationMap = new Map<string, Citation>();
  citations.forEach((citation) => {
    citation.references.forEach((ref) => {
      refToCitationMap.set(ref.id, citation);
    });
  });

  // Find all [cite: ...] patterns and replace them
  return text.replace(/\[cite:\s*([^\]]+)\]/g, (match, citationRefs) => {
    // Parse the citation references (can be comma-separated)
    const refs = citationRefs.split(',').map((ref: string) => ref.trim());

    // Find the corresponding citation objects
    const matchingCitations = new Set<Citation>();
    refs.forEach((ref: string) => {
      // Try to find citation by reference ID
      let citation = refToCitationMap.get(ref);

      // If not found, try alternative formats
      if (!citation) {
        // Try with citation- prefix
        citation = refToCitationMap.get(`citation-${ref}`);
      }

      // If still not found and ref is numeric, try to find by citation index
      if (!citation && /^\d+$/.test(ref)) {
        const citationIndex = parseInt(ref, 10);
        if (citationIndex < citations.length) {
          citation = citations[citationIndex];
        }
      }

      if (citation) {
        matchingCitations.add(citation);
      }
    });

    if (matchingCitations.size === 0) {
      return match;
    }

    // Convert to citation markers
    const citationNumbers = Array.from(matchingCitations).map((citation) => {
      const citationIndex = citations.findIndex((c) => c.id === citation.id);
      return citationIndex + 1;
    });

    // Create citation markers for each citation
    return citationNumbers
      .map(
        (num) =>
          `<span class='citation-marker' data-citation-id='${citations[num - 1].id}' data-citation-number='${num}'>${num}</span>`
      )
      .join('');
  });
}

// Helper function to find paragraph boundaries and group citations
function groupCitationsByParagraph(text: string, citations: any[]): Map<number, any[]> {
  const paragraphMap = new Map<number, any[]>();

  // Split text into paragraphs (double newline or single newline followed by markdown elements)
  const paragraphBoundaries: number[] = [0];

  // Find paragraph boundaries
  const paragraphSeparators = /\n\s*\n|\n(?=#{1,6}\s)|\n(?=\d+\.\s)|\n(?=[-*+]\s)/g;
  let match;

  while ((match = paragraphSeparators.exec(text)) !== null) {
    paragraphBoundaries.push(match.index + match[0].length);
  }
  paragraphBoundaries.push(text.length);

  // Group citations by paragraph
  citations.forEach((citation) => {
    if (citation.position < 0 || citation.position > text.length) return;

    // Find which paragraph this citation belongs to
    let paragraphIndex = 0;
    for (let i = 0; i < paragraphBoundaries.length - 1; i++) {
      if (
        citation.position >= paragraphBoundaries[i] &&
        citation.position < paragraphBoundaries[i + 1]
      ) {
        paragraphIndex = i;
        break;
      }
    }

    // Get the end position of this paragraph (before the separator)
    const paragraphEnd = paragraphBoundaries[paragraphIndex + 1];
    let insertPosition = paragraphEnd;

    // Find the actual end of content in this paragraph (skip trailing whitespace)
    while (
      insertPosition > paragraphBoundaries[paragraphIndex] &&
      /\s/.test(text[insertPosition - 1])
    ) {
      insertPosition--;
    }

    if (!paragraphMap.has(insertPosition)) {
      paragraphMap.set(insertPosition, []);
    }
    paragraphMap.get(insertPosition)!.push(citation);
  });

  return paragraphMap;
}

export function MarkdownWithCitations({
  children,
  citations = [],
  sx,
  className,
  ...other
}: MarkdownWithCitationsProps) {
  // Create components with access to current citations
  const componentsWithCitations = useMemo(
    () => ({
      span: ({ className: spanClassName, ...spanProps }: any) => {
        // Check if this is a citation marker span
        if (spanClassName === 'citation-marker') {
          const citationId = spanProps['data-citation-id'];
          const citationNumber = spanProps['data-citation-number'];

          if (!citations || !citationId) {
            return <span>{spanProps.children}</span>;
          }

          const citation = citations.find((c: any) => c.id === citationId);
          if (!citation) {
            return <span>{spanProps.children}</span>;
          }

          return <CitationMark citation={citation} citationNumber={parseInt(citationNumber, 10)} />;
        }
        // Regular span
        return <span className={spanClassName} {...spanProps} />;
      },
    }),
    [citations]
  );

  // Pre-process the text to insert citation markers
  const processedText = useMemo(() => {
    if (!citations.length || !children) return children;

    let text = children.toString();

    // Check if we have [cite: ...] format citations in the text
    const hasCiteReferences = /\[cite:\s*[^\]]+\]/g.test(text);

    if (hasCiteReferences) {
      // Process [cite: ...] format citations
      text = processCiteReferences(text, citations);
    } else {
      // Process position-based citations - group by paragraph and place at end
      const validCitations = citations.filter((citation) => {
        const isValid = citation.position >= 0 && citation.position <= text.length;
        if (!isValid && process.env.NODE_ENV === 'development') {
          console.warn(
            `Invalid citation position: ${citation.id} at position ${citation.position} (text length: ${text.length})`
          );
        }
        return isValid;
      });

      if (validCitations.length === 0) return text;

      // Group citations by paragraph
      const paragraphCitations = groupCitationsByParagraph(text, validCitations);

      // Sort insertion positions in descending order to process from end to start
      const sortedPositions = Array.from(paragraphCitations.keys()).sort((a, b) => b - a);

      // Process each paragraph's citations
      sortedPositions.forEach((insertPosition) => {
        const paragraphCits = paragraphCitations.get(insertPosition)!;

        // Sort citations within this paragraph by original position for consistent numbering
        const sortedParagraphCits = paragraphCits.sort((a, b) => a.position - b.position);

        // Create citation markers for this paragraph
        const citationNumbers = sortedParagraphCits.map((citation) => {
          const citationIndex = validCitations
            .sort((a, b) => a.position - b.position)
            .findIndex((c) => c.id === citation.id);
          return citationIndex + 1;
        });

        // Create combined citation markers
        const markers = citationNumbers
          .map((num, index) => {
            const citation = sortedParagraphCits[index];
            return `<span class='citation-marker' data-citation-id='${citation.id}' data-citation-number='${num}'>${num}</span>`;
          })
          .join('');

        // Insert all markers at the end of this paragraph
        const before = text.slice(0, insertPosition);
        const after = text.slice(insertPosition);
        text = before + markers + after;
      });
    }

    return text;
  }, [children, citations]);

  return (
    <MarkdownRoot
      components={componentsWithCitations as Options['components']}
      remarkPlugins={remarkPlugins as Options['remarkPlugins']}
      rehypePlugins={rehypePlugins as Options['rehypePlugins']}
      className={mergeClasses([markdownClasses.root, className])}
      sx={sx}
      {...other}
    >
      {processedText}
    </MarkdownRoot>
  );
}
