import type { ProjectRole } from 'src/store/api/projects';

import { useState } from 'react';
import { useBoolean } from 'minimal-shared/hooks';

import { Box } from '@mui/material';
import Stack from '@mui/material/Stack';
import { alpha } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import LoadingButton from '@mui/lab/LoadingButton';

import { canDeleteNote } from 'src/utils/permissions';

import { CONFIG } from 'src/global-config';

import { Iconify } from '../iconify';
import { truncateNoteContent } from './utils';
import { ConfirmDialog } from '../custom-dialog';

import type { NoteItem as NoteItemType } from './types';

interface NoteItemProps {
  item: NoteItemType;
  onSelect: (note: NoteItemType) => void;
  onDelete?: (noteId: string) => Promise<void>;
  isCanDeleteResource?: boolean;
  role?: ProjectRole | string;
  isOwner?: boolean;
  currentUserId?: string;
}

export function NoteItem({
  item,
  onSelect,
  onDelete,
  isCanDeleteResource,
  role,
  isOwner = false,
  currentUserId = '',
}: NoteItemProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const confirmDialog = useBoolean();

  // Check if current user can delete this specific note
  const canDeleteThisNote = canDeleteNote(role, isOwner, item.createdById, currentUserId);
  const showDeleteButton = isCanDeleteResource && canDeleteThisNote;

  const handleDelete = async () => {
    if (!onDelete) return;
    setIsDeleting(true);
    await onDelete(item.id);
    setIsDeleting(false);
    confirmDialog.onFalse();
  };

  const renderConfirmDialog = () => (
    <ConfirmDialog
      open={confirmDialog.value}
      onClose={confirmDialog.onFalse}
      title={`Delete ${item.title}`}
      content="Are you sure want to delete this note? This action cannot be undone."
      action={
        <LoadingButton
          variant="contained"
          color="error"
          onClick={handleDelete}
          loading={isDeleting}
        >
          Delete
        </LoadingButton>
      }
    />
  );

  return (
    <Stack
      spacing={2}
      direction="row"
      alignItems="center"
      sx={{
        p: 2,
        borderRadius: 2,
        cursor: 'pointer',
        bgcolor: (theme) => theme.palette.background.neutral,
        transition: (theme) =>
          theme.transitions.create('all', {
            duration: theme.transitions.duration.shortest,
          }),
        '&:hover': {
          bgcolor: (theme) => alpha(theme.palette.grey[500], 0.12),
          '& .delete-button': {
            opacity: 1,
          },
        },
      }}
    >
      <Box
        component="img"
        src={`${CONFIG.assetsDir}/assets/icons/files/ic-doc.svg`}
        sx={{ width: 32, height: 32 }}
      />

      <Stack spacing={0.5} sx={{ flex: 1 }} onClick={() => onSelect(item)}>
        <Typography variant="subtitle2" color="text.primary">
          {item.title}
        </Typography>

        <Box sx={{ maxHeight: '4em', overflow: 'hidden' }}>
          <Typography
            variant="body2"
            sx={{
              margin: 0,
              color: 'text.secondary',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              lineHeight: '1.5em',
            }}
          >
            {truncateNoteContent(item.content, 20)}
          </Typography>
        </Box>
      </Stack>

      <IconButton
        className="delete-button"
        disabled={!showDeleteButton}
        onClick={(e) => {
          e.stopPropagation();
          confirmDialog.onTrue();
        }}
        sx={{
          opacity: 0,
          color: 'error.main',
          transition: (theme) =>
            theme.transitions.create('opacity', {
              duration: theme.transitions.duration.shorter,
            }),
          '&:hover': {
            bgcolor: (theme) => alpha(theme.palette.error.main, 0.08),
          },
        }}
      >
        <Iconify icon="material-symbols:delete" />
      </IconButton>
      {onDelete && renderConfirmDialog()}
    </Stack>
  );
}
