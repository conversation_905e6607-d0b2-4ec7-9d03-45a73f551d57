import type { ProjectRole } from 'src/store/api/projects';

import { toast } from 'sonner';
import debounce from 'lodash/debounce';
import { useBoolean } from 'minimal-shared/hooks';
import { useDispatch, useSelector } from 'react-redux';
import { useState, useEffect, useCallback } from 'react';

import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import { alpha } from '@mui/material/styles';
import TextField from '@mui/material/TextField';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import LoadingButton from '@mui/lab/LoadingButton';

import { canConvertNoteToResource } from 'src/utils/permissions';

import { CONFIG } from 'src/global-config';
import { useAppSelector } from 'src/store';
import { addNote, selectNote } from 'src/store/slices/notes/slice';
import { selectFocusedResource } from 'src/store/slices/resources/selectors';
import { selectNotes, selectSelectedNote } from 'src/store/slices/notes/selectors';
import { useCreateNoteMutation, useUpdateNoteMutation } from 'src/store/api/notes';

import useConvertToResource from 'src/sections/resources/hooks/convert-to-resource';

import { Editor } from '../editor';
import { Iconify } from '../iconify';
import { NoteList } from './note-list';
import { SvgColor } from '../svg-color';
import { ContentPanel } from '../content-panel';

import type { NoteItem as NoteItemType } from './types';

interface NotesProps {
  isOpen?: boolean;
  onClose?: () => void;
  onMaximize?: () => void;
  isCanEdit?: boolean;
  role?: ProjectRole | string;
  isOwner?: boolean;
  currentUserId?: string;
}

export function Notes({
  isOpen,
  onClose,
  onMaximize,
  isCanEdit = true,
  role,
  isOwner = false,
  currentUserId = '',
}: NotesProps) {
  const [editorContent, setEditorContent] = useState<string>('');
  const [editorTitle, setEditorTitle] = useState<string>('');
  const [isEditing, setIsEditing] = useState({ title: false, content: false });
  const [createNote] = useCreateNoteMutation();
  const [updateNote, { isLoading: isUpdatingNote }] = useUpdateNoteMutation();
  const notes = useAppSelector(selectNotes);
  const selectedNote = useAppSelector(selectSelectedNote);
  const dispatch = useDispatch();
  const focusedResource = useSelector(selectFocusedResource);
  const { convertNoteToResource, isConverting } = useConvertToResource();
  // Check if user can convert notes to resources
  const canConvert = canConvertNoteToResource(role, isOwner);
  const toolbar = useBoolean(false);

  const handleSelectNote = useCallback(
    (note: NoteItemType) => {
      dispatch(selectNote(note));
      setEditorContent(note.content);
      setEditorTitle(note.title);
    },
    [dispatch]
  );

  const saveNote = useCallback(
    (content: string, title: string) => {
      if (selectedNote && (content !== selectedNote.content || title !== selectedNote.title)) {
        updateNote({
          id: selectedNote.id,
          payload: {
            content,
            title,
            resourceId: focusedResource?.id,
          },
        })
          .catch((err) => {
            toast.error('Failed to save note');
          })
          .finally(() => {
            setIsEditing((prev) => ({ ...prev, title: false, content: false }));
          });
      }
    },
    [selectedNote, focusedResource?.id, updateNote]
  );

  const debouncedSaveNote = useCallback(
    debounce((content: string, title: string) => {
      saveNote(content, title);
    }, 1000),
    [saveNote]
  );

  const handleEditorChange = useCallback(
    (content: string) => {
      setIsEditing((prev) => ({ ...prev, content: true }));
      setEditorContent(content);
      debouncedSaveNote(content, editorTitle);
    },
    [debouncedSaveNote, editorTitle]
  );

  const handleSaveContent = useCallback(() => {
    saveNote(editorContent, editorTitle);
  }, [saveNote, editorContent, editorTitle]);

  const handleTitleChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setIsEditing((prev) => ({ ...prev, title: true }));
    if (event.target.value.length > 40) {
      toast.error('Title cannot be longer than 40 characters');
      return;
    }
    setEditorTitle(event.target.value);
  }, []);

  const handleTitleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      if (event.key === 'Enter') {
        event.preventDefault();
        saveNote(editorContent, editorTitle);
      }
    },
    [saveNote, editorContent, editorTitle]
  );

  const handleTitleBlur = useCallback(() => {
    saveNote(editorContent, editorTitle);
    setIsEditing((prev) => ({ ...prev, title: false }));
  }, [editorContent, editorTitle, saveNote]);

  const handleTitleClick = useCallback(() => {
    setIsEditing((prev) => ({ ...prev, title: true }));
  }, []);

  const handleConvertToSource = useCallback(async () => {
    convertNoteToResource(editorContent, editorTitle);
  }, [editorContent, editorTitle, convertNoteToResource]);

  const handleAddNewNote = useCallback(() => {
    createNote({
      payload: {
        content: '',
        title: 'New Note',
        resourceId: focusedResource?.id,
      },
    })
      .then((res) => {
        if (res.data) {
          handleSelectNote(res.data);
          dispatch(addNote(res.data));
        }
      })
      .catch((err) => {
        toast.error('Failed to add new note');
      });
  }, [createNote, dispatch, focusedResource?.id, handleSelectNote]);

  useEffect(() => {
    if (selectedNote) {
      setEditorContent(selectedNote.content);
      setEditorTitle(selectedNote.title);
    }
  }, [selectedNote]);

  const EditorFooter = useCallback(
    () => (
      <Stack direction="row" justifyContent="space-between" spacing={2} width="100%">
        <Stack direction="row" spacing={2} alignItems="center">
          <Typography variant="body2">
            {isEditing.title || isEditing.content ? 'Unsaved changes' : 'Saved'}
          </Typography>
          {(isEditing.title || isEditing.content) && (
            <LoadingButton
              variant="contained"
              onClick={handleSaveContent}
              loading={isUpdatingNote}
              size="small"
            >
              Save
            </LoadingButton>
          )}
        </Stack>
        <Stack direction="row" spacing={1} alignItems="center">
          <Tooltip title={toolbar.value ? 'Hide Toolbar' : 'Show Toolbar'}>
            <IconButton size='small' onClick={toolbar.onToggle}>
              <Iconify sx={{
                color: toolbar.value ? 'primary.main' : 'text.secondary',
              }} icon="material-symbols:format-color-text" />
            </IconButton>
          </Tooltip>
          {canConvert && (
            <LoadingButton
              variant="contained"
              onClick={handleConvertToSource}
              color="primary"
              loading={isConverting}
            >
              {isConverting ? 'Converting...' : 'Convert to Source'}
            </LoadingButton>
          )}
        </Stack>
      </Stack>
    ),
    [isEditing.title, isEditing.content, handleSaveContent, isUpdatingNote, toolbar.onToggle, toolbar.value, canConvert, handleConvertToSource, isConverting]
  );

  const PanelHeader = (
    <Stack
      direction="row"
      alignItems="center"
      justifyContent="space-between"
      sx={{
        px: 2,
        py: 1.5,
        borderBottom: (theme) => `1px solid ${alpha(theme.palette.grey[500], 0.12)}`,
      }}
    >
      <Stack direction="row" alignItems="center" spacing={2}>
        {selectedNote ? (
          <>
            {isEditing.title ? (
              <Stack direction="row" spacing={1} alignItems="center">
                <TextField
                  value={editorTitle}
                  onChange={handleTitleChange}
                  onKeyDown={handleTitleKeyDown}
                  onBlur={handleTitleBlur}
                  autoFocus
                  variant="standard"
                  sx={{
                    '& .MuiInputBase-root': {
                      fontSize: '1.25rem',
                      fontWeight: 600,
                    },
                  }}
                  slotProps={{
                    input: {
                      endAdornment: (
                        <IconButton size="small" onClick={handleSaveContent} sx={{ pl: 1 }}>
                          <Tooltip title="Press Enter or click to save">
                            <Iconify icon="material-symbols:subdirectory-arrow-left" />
                          </Tooltip>
                        </IconButton>
                      ),
                    },
                  }}
                />
              </Stack>
            ) : (
              <Typography
                variant="subtitle2"
                sx={{
                  color: 'text.primary',
                  cursor: 'pointer',
                  '&:hover': {
                    textDecoration: 'underline',
                  },
                }}
                onClick={handleTitleClick}
              >
                {editorTitle}
              </Typography>
            )}
          </>
        ) : (
          <Typography variant="subtitle2" sx={{ color: 'text.primary' }}>
            Notes
          </Typography>
        )}
      </Stack>
      <Stack direction="row" spacing={1}>
        <IconButton size="small" onClick={onClose}>
          <SvgColor
            sx={{
              width: 20,
              height: 20,
            }}
            src={`${CONFIG.assetsDir}/assets/icons/files/ic-exit.svg`}
          />
        </IconButton>
      </Stack>
    </Stack>
  );

  return (
    <ContentPanel
      header={PanelHeader}
      isOpen={isOpen}
      onClose={onClose}
      onMaximize={onMaximize}
      contentPanelSx={selectedNote ? { p: 0 } : {}}
      footer={selectedNote && isCanEdit ? <EditorFooter /> : undefined}
      onAddNewNote={isCanEdit ? handleAddNewNote : undefined}
    >
      {selectedNote ? (
        isCanEdit ? (
          <Editor
            slotProps={{ wrapper: { sx: { height: '100%' } } }}
            sx={{ height: '100%' }}
            value={editorContent}
            onChange={handleEditorChange}
            toolbarVisible={toolbar.value}
          />
        ) : (
          <div dangerouslySetInnerHTML={{ __html: editorContent }} />
        )
      ) : (
        <NoteList
          notes={notes}
          onSelectNote={handleSelectNote}
          onAddNote={isCanEdit ? handleAddNewNote : undefined}
          isCanDeleteResource={isCanEdit}
          role={role}
          isOwner={isOwner}
          currentUserId={currentUserId}
        />
      )}
    </ContentPanel>
  );
}
