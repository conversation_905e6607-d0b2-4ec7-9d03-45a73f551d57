import type { SyntheticEvent } from 'react';
import type { Project } from 'src/types/project';
import type { AutocompleteRenderInputParams } from '@mui/material/Autocomplete';

import { useMemo, useState } from 'react';
import { useDebounce } from 'minimal-shared/hooks';

import Box from '@mui/material/Box';
import Tooltip from '@mui/material/Tooltip';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import Autocomplete from '@mui/material/Autocomplete';
import InputAdornment from '@mui/material/InputAdornment';

import useUserInitialContext from 'src/hooks/user-initial-context';

import { Iconify } from 'src/components/iconify';
import { LoadingScreen } from 'src/components/loading-screen';
import { ProjectItemAvatar } from 'src/components/project-item-avatar';

const MAX_PROJECTS = 5;
const DEBOUNCE_MS = 300;

// ----------------------------------------------------------------------

export interface ProjectAutocompleteProps {
  value?: Project | null;
  onChange?: (event: SyntheticEvent, value: Project | null) => void;
  placeholder?: string;
  disabled?: boolean;
  size?: 'small' | 'medium';
  fullWidth?: boolean;
}

export function ProjectAutocomplete({
  value = null,
  onChange,
  placeholder = 'Search projects...',
  disabled = false,
  size = 'small',
  fullWidth = true,
}: ProjectAutocompleteProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const debouncedSearchQuery = useDebounce(searchQuery, DEBOUNCE_MS);
  
  const { projects, isLoading } = useUserInitialContext();

  // Filter and limit projects based on search query
  const filteredProjects = useMemo(() => {
    if (!debouncedSearchQuery.trim()) {
      return projects.slice(0, MAX_PROJECTS); // Show first 5 projects when no search
    }

    const query = debouncedSearchQuery.toLowerCase();
    const filtered = projects.filter((project) =>
      project.name.toLowerCase().includes(query)
    );

    return filtered.slice(0, MAX_PROJECTS); // Limit to max 5 results
  }, [projects, debouncedSearchQuery]);

  const handleInputChange = (event: SyntheticEvent, newInputValue: string) => {
    setSearchQuery(newInputValue);
  };

  const handleChange = (event: SyntheticEvent, newValue: Project | null) => {
    onChange?.(event, newValue);
  };

  const renderInput = (params: AutocompleteRenderInputParams) => (
    <Tooltip 
      title="Search projects by name"
      placement="top"
      arrow
    >
      <TextField
        {...params}
        placeholder={placeholder}
        disabled={disabled}
        size={size}
        slotProps={{
          input: {
            ...params.InputProps,
            startAdornment: (
              <>
                <InputAdornment position="start">
                  <Iconify 
                    icon="material-symbols:search" 
                    sx={{ color: 'text.disabled' }} 
                  />
                </InputAdornment>
                {params.InputProps.startAdornment}
              </>
            ),
          },
        }}
      />
    </Tooltip>
  );

  const renderOption = (props: any, option: Project) => (
    <Tooltip
      title={
        <Box>
          <Typography variant="body2" sx={{ fontWeight: 600 }}>
            {option.name}
          </Typography>
          <Typography variant="caption" color="inherit">
            Created: {new Date(option.createdAt).toLocaleDateString()}
          </Typography>
        </Box>
      }
      placement="right"
      arrow
    >
      <Box component="li" {...props} key={option.id}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', flexDirection: 'column', flex: 1, minWidth: 0, px: 2, py: 1,width: '100%' }}>
            <Typography variant="subtitle2" noWrap>
              {option.name}
            </Typography>
          </Box>
          <ProjectItemAvatar user={option.createdBy} size={20} />
        </Box>
      </Box>
    </Tooltip>
  );



  return (
    <Autocomplete
      value={value}
      onChange={handleChange}
      onInputChange={handleInputChange}
      options={filteredProjects as unknown as Project[]}
      getOptionLabel={(option) => option.name}
      isOptionEqualToValue={(option, selectedValue) => option.id === selectedValue.id}
      loading={isLoading}
      disabled={disabled}
      fullWidth={fullWidth}
      renderInput={renderInput}
      renderOption={renderOption}
      noOptionsText={
        searchQuery ? 'No projects found' : 'Start typing to search projects'
      }
      loadingText={
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, p: 1 }}>
          <LoadingScreen />
          <Typography variant="body2">Loading projects...</Typography>
        </Box>
      }
      popupIcon={null}
      clearIcon={
        <Iconify 
          icon="material-symbols:close" 
          sx={{ width: 18, height: 18 }} 
        />
      }
    />
  );
} 
