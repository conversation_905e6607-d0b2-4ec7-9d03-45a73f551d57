
import type { ProjectUser } from 'src/types';

import { Avatar } from '@mui/material';

export const ProjectItemAvatar: React.FC<{ user: ProjectUser, size: number }> = ({ user, size }) =>
  <Avatar
    alt={user.displayName}
    src={user.photoURL}
    sx={{
      width: size - 1, 
      height: size - 1,
      fontSize: size * 0.4,
      cursor: 'pointer',
      border: `1px solid ${"primary.main"} !important`,
    }}
  >
    {user.displayName?.charAt(0)?.toUpperCase() || user.email?.charAt(0)?.toUpperCase()}
  </Avatar>
