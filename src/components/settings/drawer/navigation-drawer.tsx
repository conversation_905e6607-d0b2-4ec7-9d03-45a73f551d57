import type { Theme, SxProps } from '@mui/material/styles';

import { useEffect, useCallback } from 'react';
import { varAlpha } from 'minimal-shared/utils';

import Box from '@mui/material/Box';
import Drawer from '@mui/material/Drawer';
import Typography from '@mui/material/Typography';

import { paths } from 'src/routes/paths';
import { useRouter, usePathname } from 'src/routes/hooks';

import useNavConfig from 'src/hooks/nav-config';

import { Logo } from 'src/components/logo';
import { Scrollbar } from 'src/components/scrollbar';
import { NavSectionVertical } from 'src/components/nav-section';
import { ProjectAutocomplete } from 'src/components/project-autocomplete';

// ----------------------------------------------------------------------

export interface NavigationDrawerProps {
  sx?: SxProps<Theme>;
  open: boolean;
  onClose: () => void;
}

export function NavigationDrawer({ sx, open, onClose }: NavigationDrawerProps) {
  const pathname = usePathname();
  const router = useRouter();
  const navData = useNavConfig();

  const handleCloseDrawer = useCallback(() => {
    onClose();
  }, [onClose]);

  // Close drawer when route changes (navigation item clicked)
  useEffect(() => {
    if (open) {
      onClose();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname]);

  const handleProjectSelect = useCallback((event: any, project: any) => {
    if (project) {
      // You can add navigation logic here, for example:
      router.push(paths.project.details(project.id));
      onClose();
    }
  }, []);

  const renderTopArea = () => (
    <Box sx={{ px: 2, pt: 2.5, pb: 1 }}>
      <Box sx={{ pb: 2 }}>
        <Logo />
      </Box>
      
      <Box sx={{ pb: 1 }}>
        <Typography 
          variant="overline" 
          sx={{ 
            color: 'text.disabled',
            display: 'block',
            mb: 1,
            fontSize: '0.75rem',
            fontWeight: 600,
            letterSpacing: 1.2
          }}
        >
          Quick Project Search
        </Typography>
        <ProjectAutocomplete
          placeholder="Search projects..."
          onChange={handleProjectSelect}
          size="small"
        />
      </Box>
    </Box>
  );

  const renderNavigation = () => (
    <Scrollbar fillContent>
      <NavSectionVertical data={navData} sx={{ px: 2, flex: '1 1 auto' }} />
    </Scrollbar>
  );

  return (
    <Drawer
      anchor="left"
      open={open}
      onClose={handleCloseDrawer}
      slotProps={{ backdrop: { invisible: true } }}
      PaperProps={{
        sx: [
          (theme) => ({
            ...theme.mixins.paperStyles(theme, {
              color: varAlpha(theme.vars.palette.background.defaultChannel, 0.9),
            }),
            width: 360,
            display: 'flex',
            flexDirection: 'column',
          }),
          ...(Array.isArray(sx) ? sx : [sx]),
        ],
      }}
    >
      <Box sx={{ flex: '1 1 auto', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
        {renderTopArea()}
        {renderNavigation()}
      </Box>
    </Drawer>
  );
}
