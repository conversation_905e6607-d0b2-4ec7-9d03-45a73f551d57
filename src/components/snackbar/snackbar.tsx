import Portal from '@mui/material/Portal';

import { Iconify } from '../iconify';
import { SnackbarRoot } from './styles';
import { snackbarClasses } from './classes';

// ----------------------------------------------------------------------

export function Snackbar() {
  return (
    <Portal>
      <SnackbarRoot
        expand
        gap={12}
        closeButton
        offset={16}
        visibleToasts={4}
        position="bottom-right"
        className={snackbarClasses.root}
        toastOptions={{
          unstyled: true,
          classNames: {
            toast: snackbarClasses.toast,
            icon: snackbarClasses.icon,
            // content
            content: snackbarClasses.content,
            title: snackbarClasses.title,
            description: snackbarClasses.description,
            // button
            actionButton: snackbarClasses.actionButton,
            cancelButton: snackbarClasses.cancelButton,
            closeButton: snackbarClasses.closeButton,
            // state
            default: snackbarClasses.default,
            info: snackbarClasses.info,
            error: snackbarClasses.error,
            success: snackbarClasses.success,
            warning: snackbarClasses.warning,
          },
        }}
        icons={{
          loading: <span className={snackbarClasses.loadingIcon} />,
          info: <Iconify className={snackbarClasses.iconSvg} icon="material-symbols:info" />,
          success: (
            <Iconify className={snackbarClasses.iconSvg} icon="material-symbols:check-circle" />
          ),
          warning: <Iconify className={snackbarClasses.iconSvg} icon="material-symbols:warning" />,
          error: <Iconify className={snackbarClasses.iconSvg} icon="material-symbols:error" />,
        }}
      />
    </Portal>
  );
}
