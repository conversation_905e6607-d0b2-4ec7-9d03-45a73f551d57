import { useState } from 'react';

import Box from '@mui/material/Box';
import Menu from '@mui/material/Menu';
import { styled } from '@mui/material';
import MenuItem from '@mui/material/MenuItem';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';

import { Iconify } from 'src/components/iconify';

export type QualityOption = 'high' | 'normal';

const StyledQualityButton = styled(IconButton)(({ theme }) => ({
  background: 'none',
  border: 'none',
  cursor: 'pointer',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  padding: 0,
  margin: 0,
  width: theme.spacing(4),
  height: theme.spacing(4),
  borderRadius: theme.spacing(0.5),
  color: theme.palette.common.white,
  '&:hover': {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
}));

export interface CustomQualityButtonProps {
  currentQuality: QualityOption;
  onQualityChange: (quality: QualityOption) => void;
}

export const CustomQualityButton = ({ 
  currentQuality, 
  onQualityChange 
}: CustomQualityButtonProps) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleQualitySelect = (quality: QualityOption) => {
    onQualityChange(quality);
    handleClose();
  };

  return (
    <>
      <StyledQualityButton
        onClick={handleClick}
        aria-label="Quality settings"
        aria-controls={open ? 'quality-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        aria-hidden={false}
      >
        <Iconify
          width={18}
          icon="mingcute:settings-6-line"
          sx={{
            width: (theme) => theme.spacing(4),
            height: (theme) => theme.spacing(4),
            borderRadius: (theme) => theme.spacing(0.5),
            display: 'block !important',
            color: 'inherit',
          }}
          aria-hidden="false"
          className="plyr__control"
          aria-label="Quality settings"
        />
      </StyledQualityButton>

      <Menu
        id="quality-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          'aria-labelledby': 'quality-button',
        }}
        PaperProps={{
          sx: {
            backgroundColor: 'rgba(0, 0, 0, 0.95)',
            backdropFilter: 'blur(10px)',
            border: '1px solid',
            borderColor: 'divider',
            minWidth: 160,
            '& .MuiMenuItem-root': {
              color: 'common.white',
              fontSize: '13px',
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.08)',
              },
              '&.Mui-selected': {
                backgroundColor: 'primary.main',
                '&:hover': {
                  backgroundColor: 'primary.dark',
                },
              },
            },
          },
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'top' }}
      >
        <MenuItem
          selected={currentQuality === 'high'}
          onClick={() => handleQualitySelect('high')}
        >
          <Box>
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              High Quality
            </Typography>
            <Typography variant="caption" sx={{ opacity: 0.7, display: 'block' }}>
              Original file
            </Typography>
          </Box>
        </MenuItem>
        <MenuItem
          selected={currentQuality === 'normal'}
          onClick={() => handleQualitySelect('normal')}
        >
          <Box>
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              Normal Quality
            </Typography>
            <Typography variant="caption" sx={{ opacity: 0.7, display: 'block' }}>
              Encoded file
            </Typography>
          </Box>
        </MenuItem>
      </Menu>
    </>
  );
}; 