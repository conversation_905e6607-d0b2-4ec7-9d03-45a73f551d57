import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';

import type { LoadingOverlayProps } from '../types';

export const LoadingOverlay = ({ 
  isLoading, 
  isBuffering, 
  mode = 'full' 
}: LoadingOverlayProps) => {
  const showLoadingState = isLoading || isBuffering;

  if (!showLoadingState) {
    return null;
  }

  return (
    <Box
      className="video-player-loading-overlay"
      sx={{
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        zIndex: 1000,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        gap: 1,
        background: "primary.main",
      }}
    >
      <CircularProgress
        size={mode === 'thumbnail' ? 24 : 40}
      />
    </Box>
  )
}; 
