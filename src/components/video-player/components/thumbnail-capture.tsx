import { toast } from 'sonner';
import { useCallback } from 'react';

import Tooltip from '@mui/material/Tooltip';
import LoadingButton from '@mui/lab/LoadingButton';

import { useUploadResourceThumbnailMutation } from 'src/store/api/resources';

import { Iconify } from 'src/components/iconify';

import type { ThumbnailCaptureProps } from '../types';

export const ThumbnailCapture = ({
  resource,
  showControls,
  isLoading,
  hasError,
}: ThumbnailCaptureProps) => {
  const [uploadResourceThumbnail, { isLoading: isUploading }] =
    useUploadResourceThumbnailMutation();

  const handleCaptureFrame = useCallback(async () => {
    try {
      // Get the video element from the player
      const videoElement = document.querySelector('.data-media-player video') as HTMLVideoElement;

      if (!videoElement) {
        toast.error('Video element not found');
        return;
      }

      if (videoElement.readyState < 2) {
        toast.error('Video not ready for capture');
        return;
      }

      // Pause the video for capture
      videoElement.pause();

      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');

      if (!context) {
        toast.error('Cannot create canvas context');
        return;
      }

      canvas.width = videoElement.videoWidth || videoElement.offsetWidth;
      canvas.height = videoElement.videoHeight || videoElement.offsetHeight;

      context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

      const imageUrl = canvas.toDataURL('image/png', 0.95);

      await uploadResourceThumbnail({ id: resource.id, payload: { imageUrl } }).unwrap();

      toast.success('Thumbnail updated successfully! Close modal to see changes.', {
        duration: 4000,
      });

      canvas.remove();
    } catch (error) {
      toast.error('Failed to capture thumbnail');
    }
  }, [resource.id, uploadResourceThumbnail]);

  return (
    <Tooltip arrow title="Capture current frame as thumbnail">
      <LoadingButton
        loading={isUploading}
        variant="contained"
        size="small"
        color="primary"
        className="thumbnail-capture-button"
        sx={{
          position: 'absolute',
          top: 8,
          left: 8,
          zIndex: 10,
          minWidth: 'auto',
          padding: 1,
          opacity: showControls ? 1 : 0,
          transition: (theme) =>
            theme.transitions.create('opacity', {
              duration: theme.transitions.duration.short,
            }),
        }}
        onClick={handleCaptureFrame}
        disabled={hasError || isLoading}
      >
        <Iconify
          icon="material-symbols:photo-camera-outline"
          sx={{
            fontSize: 24,
            color: 'inherit',
          }}
        />
      </LoadingButton>
    </Tooltip>
  );
}; 
