import type { Resource } from 'src/types';

export interface VideoPlayerProps {
  /** Video source URL */
  src: string;
  /** Optional poster/thumbnail image URL */
  poster?: string;
  /** Resource data (required for thumbnail capture) */
  resource?: Resource;
  /** Display mode - full player or thumbnail preview */
  mode?: 'full' | 'thumbnail';
  /** Disable loading overlay */
  disableLoadingOverlay?: boolean;
  /** Whether to show video controls */
  showControls?: boolean;
  /** Whether to enable thumbnail capture button */
  enableThumbnailCapture?: boolean;
  /** Whether to enable hover effects (thumbnail mode only) */
  enableHoverEffects?: boolean;
  /** Video title/alt text */
  title?: string;
  /** Custom width (defaults based on mode) */
  width?: string | number;
  /** Custom height (defaults based on mode) */
  height?: string | number;
  /** Custom aspect ratio */
  aspectRatio?: string;
  /** Whether video should autoplay (thumbnail mode only) */
  autoplay?: boolean;
  /** Whether video should be muted by default */
  muted?: boolean;
  /** Whether video should loop */
  loop?: boolean;
  /** Video loading strategy - 'eager' loads immediately, 'idle' delays loading */
  load?: 'eager' | 'idle';
  /** Loading callback */
  onLoad?: () => void;
  /** Error callback */
  onError?: () => void;
  /** Click callback (useful for thumbnail mode) */
  onClick?: () => void;
  /** Fullscreen click callback - opens detail view instead of native fullscreen */
  onFullscreenClick?: () => void;

  plyrLayout?: React.ReactNode;
  captionVtts?: string[];
}

export interface ThumbnailCaptureProps {
  resource: Resource;
  showControls: boolean;
  isLoading: boolean;
  hasError: boolean;
}

export interface LoadingOverlayProps {
  isLoading: boolean;
  isBuffering: boolean;
  mode?: 'full' | 'thumbnail';
}
