.plyr {
  --plyr-color-main: var(--palette-primary-main);
  --plyr-border-radius: 8px;

  --plyr-font-family: inherit;
  --plyr-font-weight-regular: 400;
  --plyr-font-size-small: 13px;
  --plyr-font-size-base: 15px;
  --plyr-font-size-large: 18px;
  --plyr-font-size-xlarge: 21px;
  --plyr-line-height: 1.7;

  --plyr-audio-border: 1px solid var(--palette-divider);

  --plyr-video-controls-background: linear-gradient(rgba(var(--palette-common-blackChannel) / 0), rgba(var(--palette-common-blackChannel) / 0.75));

  /* Focus */
  --plyr-focus-visible-color: var(--palette-primary-main);

  /* Control */
  --plyr-control-spacing: 10px;
  --plyr-control-radius: 8px;
  --plyr-control-icon-size: 18px;
  --plyr-control-toggle-checked-background: var(--palette-primary-main);

  --plyr-audio-controls-background: var(--palette-background-paper);
  --plyr-audio-control-background-hover: var(--palette-primary-main);
  --plyr-audio-control-color: var(--palette-text-primary);
  --plyr-audio-control-color-hover: var(--palette-primary-contrastText);

  --plyr-video-control-color: var(--palette-common-white);
  --plyr-video-control-color-hover: var(--palette-common-white);
  --plyr-video-control-background-hover: var(--palette-primary-main);

  /* Badge */
  --plyr-font-size-badge: 9px;

  --plyr-badge-background: var(--palette-text-primary);
  --plyr-badge-border-radius: 8px;
  --plyr-badge-text-color: var(--palette-background-paper);

  /* Captions */
  --plyr-captions-background: rgba(var(--palette-common-blackChannel) / 0.8);
  --plyr-captions-text-color: var(--palette-common-white);

  /* Menu */
  --plyr-font-size-menu: 13px;

  --plyr-menu-background: rgba(var(--palette-background-paperChannel) / 0.95);
  --plyr-menu-color: var(--palette-text-primary);
  --plyr-menu-radius: 8px;
  --plyr-menu-shadow: var(--customShadows-dropdown);

  --plyr-menu-arrow-size: 4px;
  --plyr-menu-arrow-color: var(--palette-text-secondary);
  --plyr-menu-item-arrow-size: 4px;

  --plyr-menu-back-border-color: var(--palette-divider);
  --plyr-menu-back-border-shadow-color: var(--palette-background-paper);

  /* Range */
  --plyr-range-track-height: 5px;
  --plyr-range-fill-background: var(--palette-primary-main);

  --plyr-range-thumb-height: 13px;
  --plyr-range-thumb-active-shadow-width: 3px;
  --plyr-range-thumb-background: var(--palette-common-white);
  --plyr-range-thumb-shadow: var(--customShadows-z1);

  --plyr-audio-range-track-background: rgba(var(--palette-grey-500Channel) / 0.3);
  --plyr-audio-progress-buffered-background: rgba(var(--palette-grey-500Channel) / 0.6);
  --plyr-audio-range-thumb-active-shadow-color: rgba(var(--palette-primary-mainChannel) / 0.2);

  --plyr-video-range-track-background: rgba(var(--palette-common-whiteChannel) / 0.25);
  --plyr-video-progress-buffered-background: rgba(var(--palette-common-whiteChannel) / 0.25);

  /* Progress */
  --plyr-font-size-progress-marker: 13px;

  --plyr-progress-loading-background: rgba(var(--palette-text-primaryChannel) / 0.6);
  --plyr-progress-loading-size: 25px;

  --plyr-progress-marker-width: 3px;
  --plyr-progress-marker-background: var(--palette-common-white);

  --plyr-progress-live-color: var(--palette-common-white);
  --plyr-progress-live-edge-color: var(--palette-primary-main);

  --plyr-audio-progress-buffered-background: rgba(var(--palette-grey-500Channel) / 0.6);

  --plyr-video-progress-buffered-background: rgba(var(--palette-common-whiteChannel) / 0.25);

  /* Time */
  --plyr-font-size-time: 13px;

  --plyr-time-radius: 8px;

  /* Tooltip */
  --plyr-tooltip-background: var(--palette-background-paper);
  --plyr-tooltip-radius: 8px;
  --plyr-tooltip-shadow: var(--customShadows-z4);
  --plyr-tooltip-color: var(--palette-text-primary);
  --plyr-tooltip-arrow-size: 4px;

  /* Thumbnail */
  --plyr-thumbnail-aspect-ratio: 16 / 9;

  --plyr-thumbnail-min-width: 140px;
  --plyr-thumbnail-min-height: calc(var(--min-width) / var(--aspect-ratio));

  --plyr-thumbnail-max-width: 180px;
  --plyr-thumbnail-max-height: calc(var(--max-width) / var(--aspect-ratio));

  /* Live Button */
  --plyr-font-size-live-button: 12px;
  --plyr-font-weight-live-button: 600;

  --plyr-live-button-bg: var(--palette-grey-500);
  --plyr-live-button-border-radius: 8px;
  --plyr-live-button-color: var(--palette-common-white);
  --plyr-live-button-letter-spacing: 1.5px;
  --plyr-live-button-padding: 1px 4px;

  --plyr-live-button-edge-bg: var(--palette-error-main);
  --plyr-live-button-edge-color: var(--palette-error-contrastText);
}

/* Additional theme-aware customizations */
.data-media-player {
  border-radius: 8px !important;
  box-shadow: var(--customShadows-z8) !important;
  overflow: hidden !important;
}

/* Mode-specific styling */
.data-media-player.thumbnail-mode {
  box-shadow: var(--customShadows-z1) !important;
}

.data-media-player.thumbnail-mode.hover-enabled:hover {
  box-shadow: var(--customShadows-z4) !important;
  transition: all 0.2s ease !important;
}

/* Custom loading overlay styling */
.video-player-loading-overlay {
  animation: fadeIn 0.3s ease !important;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* Thumbnail capture button custom styling */
.thumbnail-capture-button {
  background: rgba(var(--palette-primary-mainChannel) / 0.9) !important;
  backdrop-filter: blur(8px) !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
  box-shadow: var(--customShadows-primary) !important;
}

.thumbnail-capture-button:hover {
  background: var(--palette-primary-main) !important;
  transform: scale(1.05) !important;
  box-shadow: var(--customShadows-primary) !important;
} 
