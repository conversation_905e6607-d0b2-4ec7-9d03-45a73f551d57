import '@vidstack/react/player/styles/base.css';
import './video-player-theme.css';

import { useRef, useMemo, useState, useCallback } from 'react';
import { Track , MediaPlayer, MediaProvider } from '@vidstack/react';
import { PlyrLayout, plyrLayoutIcons } from '@vidstack/react/player/layouts/plyr';

import Box from '@mui/material/Box';

import { getVTTFromResource } from 'src/utils/transcription-to-vtt';

import { LoadingOverlay, ThumbnailCapture, CustomQualityButton } from './components';

import type { VideoPlayerProps } from './types';
import type { QualityOption } from './components';

export const VideoPlayer = ({
  src,
  poster,
  resource,
  mode = 'full',
  showControls = true,
  enableThumbnailCapture = false,
  enableHoverEffects = true,
  title,
  width,
  height,
  aspectRatio = '16/9',
  autoplay = false,
  muted = mode === 'thumbnail',
  loop = false,
  load = 'eager',
  disableLoadingOverlay = false,
  onLoad,
  onError,
  onClick,
  plyrLayout,
  captionVtts = [],
}: VideoPlayerProps) => {
  const [isVideoLoading, setIsVideoLoading] = useState(true);
  const [isBuffering, setIsBuffering] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [currentQuality, setCurrentQuality] = useState<QualityOption>(()=>
    resource?.transcodedUrl ? 'normal' : 'high'
  );
  const [pendingVideoState, setPendingVideoState] = useState<{
    time: number;
    wasPlaying: boolean;
  } | null>(null);
  
  const playerRef = useRef<any>(null);

  // Event handlers
  const handleError = useCallback(() => {
    setIsVideoLoading(false);
    setIsBuffering(false);
    setHasError(true);
    setPendingVideoState(null);
    onError?.();
  }, [onError]);

  const handleCanPlay = useCallback(() => {
    setIsVideoLoading(false);
    setIsBuffering(false);
    setHasError(false);
    
    if (pendingVideoState && playerRef.current) {
      try {
        playerRef.current.currentTime = pendingVideoState.time;
        
        if (pendingVideoState.wasPlaying) {
          playerRef.current.play().catch((error: any) => {
            console.warn('Failed to resume playing after quality change:', error);
          });
        }
        
        setPendingVideoState(null);
      } catch (error) {
        console.warn('Failed to seek to pending time:', error);
        setPendingVideoState(null);
      }
    }
    
    onLoad?.();
  }, [onLoad, pendingVideoState]);

  const handleLoadStart = useCallback(() => {
    setIsVideoLoading(true);
    setIsBuffering(false);
    setHasError(false);
  }, []);

  const handleWaiting = useCallback(() => {
    setIsBuffering(true);
  }, []);

  const handlePlaying = useCallback(() => {
    setIsVideoLoading(false);
    setIsBuffering(false);
  }, []);

  const handleLoadedData = useCallback(() => {
    setIsVideoLoading(false);
    
    if (pendingVideoState && playerRef.current && !hasError) {
      try {
        playerRef.current.currentTime = pendingVideoState.time;
        
        if (pendingVideoState.wasPlaying) {
          playerRef.current.play().catch((error: any) => {
            console.warn('Failed to resume playing after quality change in loadedData:', error);
          });
        }
        
        setPendingVideoState(null);
      } catch (error) {
        console.warn('Failed to seek to pending time in loadedData:', error);
        setPendingVideoState(null);
      }
    }
  }, [pendingVideoState, hasError]);

  const handleClick = useCallback(() => {
    onClick?.();
  }, [onClick]);

  // Determine if quality selection should be enabled
  const shouldEnableQualitySelection = useMemo(
    () => !!(resource?.transcodedUrl && resource?.isSpriteSheets),
    [resource?.transcodedUrl, resource?.isSpriteSheets]
  );

  // Get current video source based on quality selection
  const currentVideoSource = useMemo(() => {
    if (!shouldEnableQualitySelection) {
      return src;
    }

    if (currentQuality === 'normal' && resource?.transcodedUrl) {
      return resource.transcodedUrl;
    }

    return src; 
  }, [src, resource?.transcodedUrl, currentQuality, shouldEnableQualitySelection]);

  // Quality selection handler
  const handleQualityChange = useCallback((quality: QualityOption) => {
    if (playerRef.current && !isVideoLoading && !hasError) {
      try {
        const currentTime = playerRef.current.currentTime;
        const isPlaying = !playerRef.current.paused;
        
        if (currentTime > 0) {
          setPendingVideoState({
            time: currentTime,
            wasPlaying: isPlaying,
          });
        }
      } catch (error) {
        console.warn('Failed to get current time/state before quality change:', error);
      }
    }
    
    setCurrentQuality(quality);
  }, [isVideoLoading, hasError]);

  // Enhanced layout with custom slots for quality selection
  const playerLayout = useMemo(() => {
    if (plyrLayout) {
      return plyrLayout;
    }

    // Create slots object with quality button when conditions are met
    const slots =
      shouldEnableQualitySelection && mode === 'full'
        ? {
            beforeFullscreenButton: (
              <CustomQualityButton
                currentQuality={currentQuality}
                onQualityChange={handleQualityChange}
              />
            ),
          }
        : {};

    return (
      <PlyrLayout icons={plyrLayoutIcons} thumbnails={getVTTFromResource(resource)} slots={slots} />
    );
  }, [
    plyrLayout,
    resource,
    shouldEnableQualitySelection,
    mode,
    currentQuality,
    handleQualityChange,
  ]);

  // Calculate dimensions based on mode
  const playerWidth = width || (mode === 'thumbnail' ? '100%' : '80%');
  const playerHeight = height || (mode === 'thumbnail' ? '100%' : 'auto');
  const playerMaxWidth = mode === 'thumbnail' ? 'none' : 1280;
  const playerMaxHeight = mode === 'thumbnail' ? 'none' : 720;

  // CSS class for mode-specific styling
  const playerClassName = `data-media-player ${
    mode === 'thumbnail' ? `thumbnail-mode${enableHoverEffects ? ' hover-enabled' : ''}` : ''
  }`;

  return (
    <Box
      onClick={mode === 'thumbnail' ? handleClick : undefined}
      sx={{
        width: '100%',
        aspectRatio,
        position: 'relative',
        bgcolor: 'black',
        overflow: 'hidden',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        cursor: mode === 'thumbnail' && onClick ? 'pointer' : 'default',
        borderRadius: mode === 'thumbnail' ? 1 : 0,
      }}
    >
      <MediaPlayer
        src={currentVideoSource}
        poster={poster}
        crossOrigin
        playsInline
        streamType="on-demand"
        load={load}
        posterLoad="eager"
        autoPlay={autoplay}
        muted={muted}
        loop={loop}
        className={playerClassName}
        onError={handleError}
        onCanPlay={handleCanPlay}
        onLoadStart={handleLoadStart}
        onWaiting={handleWaiting}
        onPlaying={handlePlaying}
        onLoadedData={handleLoadedData}
        style={{
          width: playerWidth,
          height: playerHeight,
          maxWidth: playerMaxWidth,
          maxHeight: playerMaxHeight,
          aspectRatio,
          backgroundColor: 'black',
          display: 'block',
        }}
        title={title}
        ref={playerRef}
      >
        <MediaProvider />

        {/* Add captions track if available */}
        {captionVtts
          ?.filter(Boolean)
          .map((vtt, index) => (
            <Track
              key={`vtt-${index}`}
              src={vtt}
              kind="captions"
              label="Transcription"
              lang="en-US"
              default
            />
          ))}

        {/* Show controls only in full mode or when explicitly enabled */}
        {(mode === 'full' || showControls) && playerLayout}

        {/* Thumbnail capture component - only in full mode */}
        {mode === 'full' && enableThumbnailCapture && resource && (
          <ThumbnailCapture
            resource={resource}
            showControls={showControls}
            isLoading={isVideoLoading}
            hasError={hasError}
          />
        )}
      </MediaPlayer>

      {/* Loading overlay */}
      {!disableLoadingOverlay && (
        <LoadingOverlay isLoading={isVideoLoading} isBuffering={isBuffering} mode={mode} />
      )}
    </Box>
  );
};
