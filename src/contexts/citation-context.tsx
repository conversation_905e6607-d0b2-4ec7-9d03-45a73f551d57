import type { ReactNode } from 'react';

import { useState, useContext, useCallback, createContext } from 'react';

interface CitationContextValue {
  onCitationClick: ((fileId: string, timestampSeconds?: number) => void) | null;
  setCitationClickHandler: (handler: (fileId: string, timestampSeconds?: number) => void) => void;
  clearCitationClickHandler: () => void;
}

const CitationContext = createContext<CitationContextValue | null>(null);

interface CitationProviderProps {
  children: ReactNode;
}

export const CitationProvider = ({ children }: CitationProviderProps) => {
  const [onCitationClick, setOnCitationClick] = useState<((fileId: string, timestampSeconds?: number) => void) | null>(null);

  const setCitationClickHandler = useCallback((handler: (fileId: string, timestampSeconds?: number) => void) => {
    setOnCitationClick(() => handler);
  }, []);

  const clearCitationClickHandler = useCallback(() => {
    setOnCitationClick(null);
  }, []);

  const value: CitationContextValue = {
    onCitationClick,
    setCitationClickHandler,
    clearCitationClickHandler,
  };

  return (
    <CitationContext.Provider value={value}>
      {children}
    </CitationContext.Provider>
  );
};

export const useCitationContext = () => {
  const context = useContext(CitationContext);
  if (!context) {
    throw new Error('useCitationContext must be used within a CitationProvider');
  }
  return context;
}; 