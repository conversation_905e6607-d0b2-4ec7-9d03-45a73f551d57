import type { SSEConnectionState } from 'src/types/project-events';

import { useContext, createContext } from 'react';

// ----------------------------------------------------------------------

export interface ProjectSSEContextType {
  connectionState: SSEConnectionState;
  reconnect: () => void;
  disconnect: () => void;
}

// ----------------------------------------------------------------------

export const ProjectSSEContext = createContext<ProjectSSEContextType | null>(null);

ProjectSSEContext.displayName = 'ProjectSSEContext';

// ----------------------------------------------------------------------

export const useProjectSSEContext = (): ProjectSSEContextType => {
  const context = useContext(ProjectSSEContext);
  
  if (!context) {
    throw new Error('useProjectSSEContext must be used within a ProjectSSEProvider');
  }
  
  return context;
};
