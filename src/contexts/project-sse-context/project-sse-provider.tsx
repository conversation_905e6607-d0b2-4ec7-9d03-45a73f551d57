import type { ReactNode } from 'react';

import { useCallback } from 'react';

import { useProjectSSE } from 'src/hooks/use-project-sse';

import eventBus from 'src/lib/event-bus';

import { SSEEventType, type ProjectEvent } from 'src/types/project-events';

import { ProjectSSEContext } from './project-sse-context';

// ----------------------------------------------------------------------

interface ProjectSSEProviderProps {
  projectId: string;
  children: ReactNode;
  refetchProject?: () => void;
  refetchProjectMembership?: () => void;
}

// ----------------------------------------------------------------------

export const ProjectSSEProvider = ({
  projectId,
  children,
  refetchProject,
  refetchProjectMembership,
}: ProjectSSEProviderProps) => {
  const handleProjectEvent = useCallback(
    (event: ProjectEvent) => {
      // Emit all events to the global event bus
      eventBus.emit(event.type, event);

      // Handle specific events that require additional actions
      switch (event.type) {
        case SSEEventType.ACL_MEMBER_CHANGE_ROLE:
          // Trigger refetch of project data when member roles change
          refetchProject?.();
          refetchProjectMembership?.();
          break;
        
        case SSEEventType.ACL_MEMBER_NEW_MEMBER:
        case SSEEventType.ACL_MEMBER_ACCEPT_INVITATION:
        case SSEEventType.ACL_MEMBER_REMOVED:
        case SSEEventType.ACL_MEMBER_LEAVE:
          // Trigger refetch for membership changes
          refetchProjectMembership?.();
          break;
          
        default:
          break;
      }
    },
    [refetchProject, refetchProjectMembership]
  );

  const { connectionState, disconnect, reconnect } = useProjectSSE({
    projectId,
    enabled: !!projectId,
    onEvent: handleProjectEvent,
  });

  const contextValue = {
    connectionState,
    disconnect,
    reconnect,
  };

  return (
    <ProjectSSEContext.Provider value={contextValue}>
      {children}
    </ProjectSSEContext.Provider>
  );
};
