import type { NavSectionProps, NavItemDataProps } from 'src/components/nav-section';

import React, { useMemo } from 'react';

import { paths } from 'src/routes/paths';

import { CONFIG } from 'src/global-config';

import { SvgColor } from 'src/components/svg-color';
import { ProjectItemAvatar } from 'src/components/project-item-avatar';

import CreateProjectNavAction from 'src/sections/projects/components/nav-actions/create-project';
import ProjectItemActions from 'src/sections/projects/components/project-breadcrumbs/project-item-actions';

import useFeatureFlags from './feature-flags';
import useUserInitialContext from './user-initial-context';

const icon = (name: string) => (
  <SvgColor src={`${CONFIG.assetsDir}/assets/icons/navbar/${name}.svg`} />
);

export const ICONS = {
  job: icon('ic-job'),
  blog: icon('ic-blog'),
  chat: icon('ic-chat'),
  mail: icon('ic-mail'),
  user: icon('ic-user'),
  file: icon('ic-file'),
  lock: icon('ic-lock'),
  tour: icon('ic-tour'),
  order: icon('ic-order'),
  label: icon('ic-label'),
  blank: icon('ic-blank'),
  kanban: icon('ic-kanban'),
  folder: icon('ic-folder'),
  course: icon('ic-course'),
  banking: icon('ic-banking'),
  booking: icon('ic-booking'),
  invoice: icon('ic-invoice'),
  product: icon('ic-product'),
  calendar: icon('ic-calendar'),
  disabled: icon('ic-disabled'),
  external: icon('ic-external'),
  menuItem: icon('ic-menu-item'),
  ecommerce: icon('ic-ecommerce'),
  analytics: icon('ic-analytics'),
  dashboard: icon('ic-dashboard'),
  parameter: icon('ic-parameter'),
};

const useNavConfig = () => {
  const { isFlagEnabled } = useFeatureFlags();

  const { projects } = useUserInitialContext();

  const navConfig: NavSectionProps['data'] = useMemo(
    () =>
      [
        {
          items: [
            {
              title: 'Files',
              path: paths.files.root,
              icon: ICONS.file,
            },
            {
              title: 'Projects',
              path: paths.project.root,
              icon: ICONS.folder,
              action: <CreateProjectNavAction />,
              children: projects.map((project) => ({
                id: project.id,
                title: project.name,
                path: paths.project.details(project.id),
                leftAction: <ProjectItemAvatar user={project.createdBy} size={20} />,
                action: <ProjectItemActions data={project} />,
              })),
            },
          ],
        },
      ] as NavSectionProps['data'],
    [projects]
  );

  // Filter nav data based on feature flags (similar to layout.tsx)
  const navData = useMemo(
    () =>
      navConfig.map((navItem) => {
        // Filter out nav items that are not enabled for the current user
        const filteredItems = navItem.items.filter(
          (item) => !item.featureFlag || isFlagEnabled(item.featureFlag)
        ) as NavItemDataProps[];

        return { ...navItem, items: filteredItems };
      }),
    [navConfig, isFlagEnabled]
  );

  return navData;
};

export default useNavConfig;
