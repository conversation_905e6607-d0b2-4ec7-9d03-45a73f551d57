import { useState, useEffect, useCallback } from 'react';

import { AUTH } from 'src/lib/firebase';
import { CONFIG } from 'src/global-config';

// Helper function to get auth token
async function getAuthToken(): Promise<string> {
  // Import Firebase auth dynamically to avoid circular dependencies
  const token = await AUTH.currentUser?.getIdToken();
  return token || '';
}

export interface ModelInfo {
  id: string;
  name: string;
  provider: 'google' | 'anthropic';
  tier: 'economy' | 'balanced' | 'premium';
  description: string;
  features: {
    maxTokens: number;
    supportsImages: boolean;
    supportsStreaming: boolean;
  };
}

interface ModelsResponse {
  success: boolean;
  data: {
    models: {
      anthropic: ModelInfo[];
      google: ModelInfo[];
    };
    all: ModelInfo[];
    total: number;
  };
}

export const useAvailableModels = () => {
  const [models, setModels] = useState<ModelInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [fallbackToLegacy, setFallbackToLegacy] = useState(false);

  const fetchModels = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`${CONFIG.aidaApiUrl}/agent/models`, {
        headers: {
          Authorization: `Bearer ${await getAuthToken()}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch available models');
      }

      const result: ModelsResponse = await response.json();
      setModels(result.data.all);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch models';
      setError(errorMessage);
      setFallbackToLegacy(true);
      console.error('Error fetching models:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchModels();
  }, [fetchModels]);

  const getModelsByProvider = (provider: string) =>
    models.filter((model) => model.provider === provider);

  const getModelById = (id: string) => models.find((model) => model.id === id);

  return {
    models,
    loading,
    error,
    fallbackToLegacy,
    getModelsByProvider,
    getModelById,
    refetch: fetchModels,
  };
};
