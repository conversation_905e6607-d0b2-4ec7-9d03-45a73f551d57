import type { AIModel } from 'src/sections/chat/types';

import { toast } from 'sonner';
import * as Sentry from '@sentry/react';
import { useState, useCallback } from 'react';

import useAnalytics from './analytics';
import { useCreateConversationMutation } from '../store/api/chat/hooks';

interface UseConversationOptions {
  initialConversationId?: string;
  projectId?: string;
  model: AIModel;
}

export const useConversation = ({
  initialConversationId,
  projectId,
  model,
}: UseConversationOptions) => {
  const [conversationId, setConversationId] = useState<string | null>(
    initialConversationId || null
  );
  const [isLoading, setIsLoading] = useState(false);

  const { trackEvent } = useAnalytics();
  const [createConversation] = useCreateConversationMutation();

  const createNewConversation = useCallback(async (): Promise<string> => {
    if (conversationId) {
      return conversationId;
    }

    // Validate that projectId is available - now mandatory per new API specs
    if (!projectId) {
      const error = new Error('Project ID is required to create a conversation');
      toast.error('Project required', {
        description: 'Please select a project before starting a conversation',
      });
      Sentry.captureException(error);
      throw error;
    }

    try {
      setIsLoading(true);
      const response = await createConversation({
        payload: { projectId },
      }).unwrap();

      const newConversationId = response.data.id;
      setConversationId(newConversationId);

      trackEvent({
        eventCategory: 'Aida Chat',
        eventAction: 'Created conversation',
        properties: {
          conversationId: newConversationId,
          model,
        },
      });

      return newConversationId;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create conversation';

      // Handle 400 error specifically for missing projectId (per new API specs)
      const statusCode = (err as any)?.data?.statusCode || (err as any)?.status;
      if (statusCode === 400) {
        toast.error('Invalid request', {
          description: (err as any)?.data?.error || 'Project ID is required',
        });
      } else {
        toast.error('Failed to create conversation', {
          description: errorMessage,
        });
      }

      Sentry.captureException(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [conversationId, createConversation, projectId, model, trackEvent]);

  const resetConversation = useCallback(() => {
    setConversationId(null);
    setIsLoading(false);
  }, []);

  return {
    conversationId,
    isLoading,
    createNewConversation,
    resetConversation,
  };
};
