// hooks/useEventListener.js
import { useEffect, useCallback } from 'react';

import eventBus from 'src/lib/event-bus';

export function useEventListener(eventName: string, callback: (...args: any[]) => void) {
  
  const callbackFn = useCallback((...args: any[]) => {
    callback(...args);
  }, [callback]);

  useEffect(() => {
    if (!eventName) return;

    eventBus.on(eventName, callbackFn);

    // eslint-disable-next-line consistent-return
    return () => {
      eventBus.off(eventName, callbackFn);
    };
  }, [eventName, callbackFn]);

  return null;
}
