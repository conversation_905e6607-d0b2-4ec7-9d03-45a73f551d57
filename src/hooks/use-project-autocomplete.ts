import type { SyntheticEvent } from 'react';
import type { Project } from 'src/types/project';

import { useState, useCallback } from 'react';
import { useDebounce } from 'minimal-shared/hooks';

import { useProjectSearch } from './use-project-search';

// ----------------------------------------------------------------------

export interface UseProjectAutocompleteProps {
  initialValue?: Project | null;
  onSelect?: (project: Project | null) => void;
  limit?: number;
  debounceMs?: number;
}

export interface UseProjectAutocompleteReturn {
  value: Project | null;
  searchQuery: string;
  projects: Project[];
  isLoading: boolean;
  error: any;
  hasMore: boolean;
  handleChange: (event: SyntheticEvent, newValue: Project | null) => void;
  handleInputChange: (event: SyntheticEvent, newInputValue: string) => void;
  setValue: (project: Project | null) => void;
  clearValue: () => void;
}

export function useProjectAutocomplete({
  initialValue = null,
  onSelect,
  limit = 5,
  debounceMs = 300,
}: UseProjectAutocompleteProps = {}): UseProjectAutocompleteReturn {
  const [value, setValue] = useState<Project | null>(initialValue);
  const [searchQuery, setSearchQuery] = useState('');
  
  const debouncedSearchQuery = useDebounce(searchQuery, debounceMs);
  
  const { projects, isLoading, error, hasMore } = useProjectSearch({
    searchQuery: debouncedSearchQuery,
    limit,
  });

  const handleChange = useCallback(
    (event: SyntheticEvent, newValue: Project | null) => {
      setValue(newValue);
      onSelect?.(newValue);
    },
    [onSelect]
  );

  const handleInputChange = useCallback(
    (event: SyntheticEvent, newInputValue: string) => {
      setSearchQuery(newInputValue);
    },
    []
  );

  const handleSetValue = useCallback((project: Project | null) => {
    setValue(project);
    onSelect?.(project);
  }, [onSelect]);

  const clearValue = useCallback(() => {
    setValue(null);
    setSearchQuery('');
    onSelect?.(null);
  }, [onSelect]);

  return {
    value,
    searchQuery,
    projects,
    isLoading,
    error,
    hasMore,
    handleChange,
    handleInputChange,
    setValue: handleSetValue,
    clearValue,
  };
} 
