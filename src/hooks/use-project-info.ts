import type { ProjectDetails } from 'src/types/project';

import { useGetProjectInfoQuery } from 'src/store/api/projects';

interface UseProjectInfoOptions {
  projectId: string;
  enabled?: boolean;
  refetchOnFocus?: boolean;
  refetchOnMountOrArgChange?: boolean;
}

interface UseProjectInfoReturn {
  project: ProjectDetails | undefined;
  isLoading: boolean;
  isFetching: boolean;
  error: any;
  refetch: () => void;
}

/**
 * Custom hook for fetching project information without pagination
 * Uses the /projects/:id/info endpoint
 * 
 * @param options - Configuration options
 * @returns Project info data and loading states
 */
export const useProjectInfo = ({
  projectId,
  enabled = true,
  refetchOnFocus = false,
  refetchOnMountOrArgChange = false,
}: UseProjectInfoOptions): UseProjectInfoReturn => {
  const {
    data: project,
    isLoading,
    isFetching,
    error,
    refetch,
  } = useGetProjectInfoQuery(
    { id: projectId },
    {
      skip: !projectId || !enabled,
      refetchOnFocus,
      refetchOnMountOrArgChange,
    }
  );

  return {
    project,
    isLoading,
    isFetching,
    error,
    refetch,
  };
};

export default useProjectInfo; 