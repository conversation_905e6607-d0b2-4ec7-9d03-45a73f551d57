import type { Project } from 'src/types/project';

import { useMemo } from 'react';

import { useGetProjectsQuery } from 'src/store/api/projects/hooks';

// ----------------------------------------------------------------------

export interface UseProjectSearchProps {
  searchQuery?: string;
  limit?: number;
  enabled?: boolean;
}

export interface UseProjectSearchReturn {
  projects: Project[];
  allProjects: Project[];
  isLoading: boolean;
  error: any;
  hasMore: boolean;
}

export function useProjectSearch({
  searchQuery = '',
  limit = 5,
  enabled = true,
}: UseProjectSearchProps = {}): UseProjectSearchReturn {
  const { data: allProjects = [], isLoading, error } = useGetProjectsQuery({}, {
    skip: !enabled,
  });

  const filteredProjects = useMemo(() => {
    if (!searchQuery.trim()) {
      return allProjects.slice(0, limit);
    }

    const query = searchQuery.toLowerCase();
    const filtered = allProjects.filter((project) => {
      const nameMatch = project.name.toLowerCase().includes(query);
      const descriptionMatch = project.description.toLowerCase().includes(query);
      
      return nameMatch || descriptionMatch;
    });

    return filtered.slice(0, limit);
  }, [allProjects, searchQuery, limit]);

  const hasMore = useMemo(() => {
    if (!searchQuery.trim()) {
      return allProjects.length > limit;
    }

    const query = searchQuery.toLowerCase();
    const totalMatches = allProjects.filter((project) => {
      const nameMatch = project.name.toLowerCase().includes(query);
      const descriptionMatch = project.description.toLowerCase().includes(query);
      
      return nameMatch || descriptionMatch;
    }).length;

    return totalMatches > limit;
  }, [allProjects, searchQuery, limit]);

  return {
    projects: filteredProjects,
    allProjects,
    isLoading,
    error,
    hasMore,
  };
}
