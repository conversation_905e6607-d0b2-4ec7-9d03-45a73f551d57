import type {
  ProjectEvent,
  SSEConnectionState,
} from 'src/types/project-events';

import { toast } from 'sonner';
import { useRef, useState, useEffect, useCallback } from 'react';

import { AUTH } from 'src/lib/firebase';
import { buildProjectSSEUrl } from 'src/utils';

// ----------------------------------------------------------------------

interface UseProjectSSEProps {
  projectId: string;
  enabled?: boolean;
  maxReconnectAttempts?: number;
  reconnectDelay?: number;
  onEvent?: (event: ProjectEvent) => void;
}

interface UseProjectSSEReturn {
  connectionState: SSEConnectionState;
  disconnect: () => void;
  reconnect: () => void;
}

// ----------------------------------------------------------------------

export function useProjectSSE({
  projectId,
  enabled = true,
  maxReconnectAttempts = 5,
  reconnectDelay = 3000,
  onEvent,
}: UseProjectSSEProps): UseProjectSSEReturn {
  const eventSourceRef = useRef<EventSource | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);

  const [connectionState, setConnectionState] = useState<SSEConnectionState>({
    isConnected: false,
    isReconnecting: false,
    reconnectAttempts: 0,
  });

  // Handle individual event types and update Redux store
  const handleProjectEvent = useCallback(
    (event: ProjectEvent) => {
      onEvent?.(event);

      // Update last event timestamp
      setConnectionState((prev) => ({
        ...prev,
        lastEventTimestamp: event.timestamp,
      }));
    },
    [onEvent]
  );

  // Connect to SSE endpoint
  const connect = useCallback(async() => {
    if (!projectId || !enabled) return;

    const firebaseToken = await AUTH.currentUser?.getIdToken();

    if (!firebaseToken) {
      throw new Error('No authentication token available');
    }

    const url = buildProjectSSEUrl({ projectId, token: firebaseToken });

    try {
      const eventSource = new EventSource(url);

      eventSource.onopen = () => {
        reconnectAttemptsRef.current = 0;
        setConnectionState((prev) => ({
          ...prev,
          isConnected: true,
          isReconnecting: false,
          reconnectAttempts: 0,
          lastError: undefined,
        }));
      };

      eventSource.onmessage = (messageEvent) => {
        try {
          const event: ProjectEvent = JSON.parse(messageEvent.data);
          handleProjectEvent(event);
        } catch (error) {
          console.error('[SSE] Error parsing event data:', error, messageEvent.data);
        }
      };

      eventSource.onerror = (error) => {
        setConnectionState((prev) => ({
          ...prev,
          isConnected: false,
          lastError: 'Connection error occurred',
        }));

        // Attempt reconnection if under max attempts
        if (reconnectAttemptsRef.current < maxReconnectAttempts) {
          reconnectAttemptsRef.current += 1;
          setConnectionState((prev) => ({
            ...prev,
            isReconnecting: true,
            reconnectAttempts: reconnectAttemptsRef.current,
          }));

          reconnectTimeoutRef.current = setTimeout(() => {
            eventSource.close();
            connect();
          }, reconnectDelay);
        } else {
          setConnectionState((prev) => ({
            ...prev,
            isReconnecting: false,
            lastError: 'Max reconnection attempts reached',
          }));

          // only show toast once
          if(reconnectAttemptsRef.current > maxReconnectAttempts) return;

          reconnectAttemptsRef.current += 1;
          toast.error('Lost connection to project updates', {
            duration: 5000,
            position: 'bottom-right',
          });
        }
      };

      eventSourceRef.current = eventSource;

    } catch {
      setConnectionState((prev) => ({
        ...prev,
        lastError: 'Failed to create connection',
      }));
    }
  }, [projectId, enabled, maxReconnectAttempts, reconnectDelay, handleProjectEvent]);

  // Disconnect from SSE
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }

    reconnectAttemptsRef.current = 0;
    setConnectionState({
      isConnected: false,
      isReconnecting: false,
      reconnectAttempts: 0,
    });
  }, []);

  // Force reconnect
  const reconnect = useCallback(() => {
    disconnect();
    reconnectAttemptsRef.current = 0;
    setTimeout(connect, 100);
  }, [disconnect, connect]);

  // Initialize connection
  useEffect(() => {
    if (enabled && projectId) {
      connect();
    }

    return disconnect;
  }, [enabled, projectId, connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => disconnect, [disconnect]);

  return {
    connectionState,
    disconnect,
    reconnect,
  };
} 
