import type { Resource } from 'src/types';
import type { FileType } from 'src/types/project';

export type SortOption = 'latest' | 'oldest' | 'title-asc' | 'title-desc';

export interface ResourcePaginationOptions {
  projectId: string;
  initialPage?: number;
  initialLimit?: number;
  initialSearch?: string;
  initialSort?: SortOption;
  initialFileTypes?: FileType[];
  includeAllResources?: boolean;
}

export interface ResourcePaginationReturn {
  // Data
  resources: Resource[];
  totalCount: number;
  
  // Loading states
  isLoading: boolean;
  isFetching: boolean;
  error: any;
  
  // Pagination state
  page: number;
  limit: number;
  totalPages: number;
  
  // Search state
  searchQuery: string;
  deferredSearchQuery: string;
  
  // Sort state
  sortOption: SortOption;
  
  // File type filter state
  selectedFileTypes: FileType[];
  
  // Actions
  onPageChange: (newPage: number) => void;
  onLimitChange: (newLimit: number) => void;
  onSearchChange: (query: string) => void;
  onSortChange: (sort: SortOption) => void;
  onFileTypesChange: (fileTypes: FileType[]) => void;
  resetPage: () => void;
  refetch: () => void;
  onResetPagination: () => void;
} 