import type { Resource, ResourceFileType } from 'src/types';
import type { FileType , ProjectResourceItem } from 'src/types/project';
import type { GetProjectResourcesParams } from 'src/store/api/projects/types';

import { useMemo, useState, useCallback, useDeferredValue } from 'react';

import { useGetProjectResourcesQuery } from 'src/store/api/projects';

import { TranscriptStatus } from 'src/types/transcription';
import { SortBy, SortOrder, ProcessingStatus } from 'src/types/project';

import type { SortOption, ResourcePaginationReturn, ResourcePaginationOptions } from './types';

const DEFAULT_LIMIT = 10;
const DEFAULT_PAGE = 1;
const DEFAULT_SORT: SortOption = 'latest';
const DEFAULT_FILE_TYPES: FileType[] = [];

// Helper function to transform ProjectResourceItem to Resource for backward compatibility
const transformProjectResourceToResource = (item: ProjectResourceItem): Resource => ({
  id: item.id,
  ffprobe: null,
  duration: item.duration || 0,
  thumbnailUrl: item.thumbnailUrl || '',
  fileSize: item.fileSize,
  fileName: item.fileName || item.filename || item.name || '',
  name: item.name || item.fileName || item.filename || '',
  createdById: item.createdById || '',
  url: item.url || '',
  createdAt: new Date(item.createdAt || item.dateAdded || new Date().toISOString()),
  fileLastModified: new Date(item.fileLastModified || item.lastModified || item.dateAdded || item.createdAt || new Date().toISOString()),
  orgId: item.orgId || undefined,
  sessionId: item.sessionId || undefined,
  topics: item.topics || undefined,
  transcription: item.transcription || [],
  transcriptionJobStatus: item.transcriptionJobStatus ? (item.transcriptionJobStatus as TranscriptStatus) : 
                          (item.processingStatus === ProcessingStatus.COMPLETED ? TranscriptStatus.Completed : 
                           item.processingStatus === ProcessingStatus.PROCESSING ? TranscriptStatus.InProgress :
                           item.processingStatus === ProcessingStatus.FAILED ? TranscriptStatus.Failed : TranscriptStatus.UnTranscript) || undefined,
  projectId: item.projectId || undefined,
  folderId: item.folderId || undefined,
  userPermissions: item.userPermissions || undefined,
  isTranscoding: item.isTranscoding || false,
  type: item.type as ResourceFileType || undefined,
  isSpriteSheets: item.isSpriteSheets || false,
  vttSrc: item.vttSrc || undefined,
  transcodedUrl: item.transcodedUrl || undefined,
});

// Helper function to convert SortOption to API parameters
const getSortParams = (sortOption: SortOption): Pick<GetProjectResourcesParams, 'sortBy' | 'sortOrder'> => {
  switch (sortOption) {
    case 'latest':
      return { sortBy: SortBy.DATE_ADDED, sortOrder: SortOrder.DESC };
    case 'oldest':
      return { sortBy: SortBy.DATE_ADDED, sortOrder: SortOrder.ASC };
    case 'title-asc':
      return { sortBy: SortBy.NAME, sortOrder: SortOrder.ASC };
    case 'title-desc':
      return { sortBy: SortBy.NAME, sortOrder: SortOrder.DESC };
    default:
      return { sortBy: SortBy.DATE_ADDED, sortOrder: SortOrder.DESC };
  }
};

// Separate hook for pagination state management
const usePaginationState = (initialPage: number, initialLimit: number) => {
  const [page, setPage] = useState(initialPage);
  const [limit, setLimit] = useState(initialLimit);

  const onPageChange = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  const onLimitChange = useCallback((newLimit: number) => {
    setLimit(newLimit);
    setPage(DEFAULT_PAGE); // Reset to first page when changing limit
  }, []);

  const resetPage = useCallback(() => {
    setPage(DEFAULT_PAGE);
  }, []);

  return {
    page,
    limit,
    onPageChange,
    onLimitChange,
    resetPage,
  };
};

// Separate hook for search state management
const useSearchState = (initialSearch: string) => {
  const [searchQuery, setSearchQuery] = useState(initialSearch);
  const deferredSearchQuery = useDeferredValue(searchQuery);

  const onSearchChange = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  return {
    searchQuery,
    deferredSearchQuery,
    onSearchChange,
  };
};

// Separate hook for sort state management
const useSortState = (initialSort: SortOption) => {
  const [sortOption, setSortOption] = useState(initialSort);

  const onSortChange = useCallback((sort: SortOption) => {
    setSortOption(sort);
  }, []);

  return {
    sortOption,
    onSortChange,
  };
};

// Separate hook for file type filter state management
const useFileTypeState = (initialFileTypes: FileType[]) => {
  const [selectedFileTypes, setSelectedFileTypes] = useState<FileType[]>(initialFileTypes);

  const onFileTypesChange = useCallback((fileTypes: FileType[]) => {
    setSelectedFileTypes(fileTypes);
  }, []);

  return {
    selectedFileTypes,
    onFileTypesChange,
  };
};

// Helper function to safely construct query parameters
const getSafeQueryParams = (
  search: string,
  selectedFileTypes: FileType[],
  sortParams: Pick<GetProjectResourcesParams, 'sortBy' | 'sortOrder'>,
  limit: number,
  page: number
) => {
  try {
    return {
      search: search || undefined,
      fileType: selectedFileTypes.length > 0 ? selectedFileTypes.join(',') : undefined,
      limit,
      page,
      ...sortParams,
    };
  } catch (error) {
    console.error('Error constructing query parameters:', error);
    // Return safe defaults
    return {
      search: undefined,
      fileType: undefined,
      limit: DEFAULT_LIMIT,
      page: DEFAULT_PAGE,
      sortBy: SortBy.DATE_ADDED,
      sortOrder: SortOrder.DESC,
    };
  }
};

// Hook for data fetching and computed values
const useProjectData = (
  projectId: string,
  deferredSearchQuery: string,
  sortOption: SortOption,
  selectedFileTypes: FileType[],
  limit: number,
  page: number
) => {
  const sortParams = getSortParams(sortOption);
  
  const queryParams = getSafeQueryParams(
    deferredSearchQuery,
    selectedFileTypes,
    sortParams,
    limit,
    page
  );

  const queryResult = useGetProjectResourcesQuery(
    {
      id: projectId,
      params: queryParams,
    },
    {
      skip: !projectId,
      refetchOnFocus: false,
    }
  );

  const resources = useMemo(() => 
    queryResult.data?.items?.map(transformProjectResourceToResource) ?? [], 
    [queryResult.data?.items]
  );
  
  const totalCount = useMemo(() => {
    const { data } = queryResult;
    // Use total from API response for pagination
    if (data?.total !== undefined) {
      return data.total;
    }
    // Fallback to current resources length
    return resources.length;
  }, [queryResult.data?.total, resources.length]);

  const totalPages = useMemo(() => 
    Math.ceil(totalCount / limit), 
    [totalCount, limit]
  );

  return {
    resources,
    totalCount,
    totalPages,
    isLoading: queryResult.isLoading,
    isFetching: queryResult.isFetching,
    error: queryResult.error,
    refetch: queryResult.refetch,
  };
};

export const useResourcePagination = (
  options: ResourcePaginationOptions
): ResourcePaginationReturn => {
  const {
    projectId,
    initialPage = DEFAULT_PAGE,
    initialLimit = DEFAULT_LIMIT,
    initialSearch = '',
    initialSort = DEFAULT_SORT,
    initialFileTypes = DEFAULT_FILE_TYPES,
  } = options;

  // Use composed hooks
  const pagination = usePaginationState(initialPage, initialLimit);
  const search = useSearchState(initialSearch);
  const sort = useSortState(initialSort);
  const fileTypeFilter = useFileTypeState(initialFileTypes);
  const projectData = useProjectData(
    projectId,
    search.deferredSearchQuery,
    sort.sortOption,
    fileTypeFilter.selectedFileTypes,
    pagination.limit,
    pagination.page
  );

  const onResetPagination = useCallback(() => {
    pagination.resetPage();
    search.onSearchChange('');
    fileTypeFilter.onFileTypesChange(DEFAULT_FILE_TYPES);
    sort.onSortChange(DEFAULT_SORT);
  }, [pagination, search, fileTypeFilter, sort]);

  // Combine pagination and search reset logic
  const handleSearchChange = useCallback((query: string) => {
    search.onSearchChange(query);
    pagination.resetPage();
  }, [search.onSearchChange, pagination.resetPage]);

  const handleLimitChange = useCallback((newLimit: number) => {
    pagination.onLimitChange(newLimit);
  }, [pagination.onLimitChange]);

  const handleSortChange = useCallback((sortOption: SortOption) => {
    sort.onSortChange(sortOption);
    pagination.resetPage();
  }, [sort.onSortChange, pagination.resetPage]);

  const handleFileTypesChange = useCallback((fileTypes: FileType[]) => {
    fileTypeFilter.onFileTypesChange(fileTypes);
    pagination.resetPage();
  }, [fileTypeFilter.onFileTypesChange, pagination.resetPage]);

  return {
    // Data
    resources: projectData.resources,
    totalCount: projectData.totalCount,
    
    // Loading states
    isLoading: projectData.isLoading,
    isFetching: projectData.isFetching,
    error: projectData.error,
    
    // Pagination state
    page: pagination.page,
    limit: pagination.limit,
    totalPages: projectData.totalPages,
    
    // Search state
    searchQuery: search.searchQuery,
    deferredSearchQuery: search.deferredSearchQuery,
    
    // Sort state
    sortOption: sort.sortOption,
    
    // File type filter state
    selectedFileTypes: fileTypeFilter.selectedFileTypes,
    
    // Actions
    onPageChange: pagination.onPageChange,
    onLimitChange: handleLimitChange,
    onSearchChange: handleSearchChange,
    onSortChange: handleSortChange,
    onFileTypesChange: handleFileTypesChange,
    resetPage: pagination.resetPage,
    refetch: projectData.refetch,
    onResetPagination,
  };
}; 