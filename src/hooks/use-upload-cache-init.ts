import { useEffect } from 'react';
import { useDispatch } from 'react-redux';

import { loadUploadQueueFromCache } from 'src/store/slices/resources/slice';

import { useUploadCache } from './use-upload-cache';

/**
 * Hook to initialize upload cache on app startup
 * This loads any pending uploads from IndexedDB back into the Redux store
 */
export const useUploadCacheInit = () => {
  const dispatch = useDispatch();
  const { loadFromCache } = useUploadCache();

  useEffect(() => {
    const initializeCache = async () => {
      try {
        // Load all pending/in-progress uploads from cache
        const cachedItems = await loadFromCache();
        
        if (cachedItems.length > 0) {
          console.log(`Loaded ${cachedItems.length} uploads from cache`);
          dispatch(loadUploadQueueFromCache(cachedItems));
        }
      } catch (error) {
        console.error('Failed to initialize upload cache:', error);
      }
    };

    initializeCache();
  }, [dispatch, loadFromCache]);
}; 