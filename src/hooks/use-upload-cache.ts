import type { ResourceUploadQueueItem } from 'src/types';

import { useCallback } from 'react';

// Comment out IndexedDB imports to disable cache temporarily
// import { 
//   fileToBuffer, 
//   bufferToFile, 
//   uploadCacheDB, 
//   type CachedUploadQueueItem 
// } from 'src/lib/upload-cache-db';

export const useUploadCache = () => {
  // Save upload queue item to cache - COMMENTED OUT
  const saveToCache = useCallback(async (item: ResourceUploadQueueItem): Promise<void> => {
    // try {
    //   const fileData = await fileToBuffer(item.file);
      
    //   const cachedItem: CachedUploadQueueItem = {
    //     id: item.id,
    //     fileName: item.file.name,
    //     fileSize: item.file.size,
    //     fileLastModified: new Date(item.file.lastModified),
    //     fileType: item.file.type,
    //     uploadUrlData: item.uploadUrlData,
    //     progress: item.progress,
    //     status: item.status,
    //     submittedAt: item.submittedAt,
    //     projectId: item.projectId,
    //     folderId: item.folderId,
    //     transcoded: item.transcoded,
    //     mode: item.mode,
    //     fileData,
    //   };

    //   await uploadCacheDB.uploadQueue.put(cachedItem);
    // } catch (error) {
    //   console.error('Failed to save upload item to cache:', error);
    // }
    
    // Temporary: No cache operations to avoid lag
    console.log('Cache save disabled temporarily for performance');
  }, []);

  // Load all cached items for a specific project - COMMENTED OUT
  const loadFromCache = useCallback(async (projectId?: string): Promise<ResourceUploadQueueItem[]> => {
    // try {
    //   let cachedItems: CachedUploadQueueItem[];
      
    //   if (projectId) {
    //     cachedItems = await uploadCacheDB.uploadQueue
    //       .where('projectId')
    //       .equals(projectId)
    //       .and(item => item.status !== 'completed' && item.status !== 'cancelled')
    //       .toArray();
    //   } else {
    //     cachedItems = await uploadCacheDB.uploadQueue
    //       .where('status')
    //       .noneOf(['completed', 'cancelled'])
    //       .toArray();
    //   }

    //   const uploadItems: ResourceUploadQueueItem[] = [];

    //   for (const cachedItem of cachedItems) {
    //     if (cachedItem.fileData) {
    //       const file = bufferToFile(
    //         cachedItem.fileData,
    //         cachedItem.fileName,
    //         cachedItem.fileType,
    //         cachedItem.fileLastModified
    //       );

    //       uploadItems.push({
    //         id: cachedItem.id,
    //         file,
    //         uploadUrlData: cachedItem.uploadUrlData,
    //         progress: cachedItem.progress,
    //         status: cachedItem.status || 'pending',
    //         submittedAt: cachedItem.submittedAt,
    //         projectId: cachedItem.projectId,
    //         folderId: cachedItem.folderId,
    //         transcoded: cachedItem.transcoded,
    //         mode: cachedItem.mode,
    //       });
    //     }
    //   }

    //   return uploadItems;
    // } catch (error) {
    //   console.error('Failed to load upload items from cache:', error);
    //   return [];
    // }
    
    // Temporary: Return empty array to avoid cache operations
    console.log('Cache load disabled temporarily for performance');
    return [];
  }, []);

  // Update cached item status/progress - COMMENTED OUT
  const updateCacheItem = useCallback(async (
    id: string, 
    updates: Partial<Pick<any, 'status' | 'progress'>>
  ): Promise<void> => {
    // try {
    //   await uploadCacheDB.uploadQueue.update(id, updates);
    // } catch (error) {
    //   console.error('Failed to update cached item:', error);
    // }
    
    // Temporary: No cache update to avoid lag
    console.log('Cache update disabled temporarily for performance', { id, updates });
  }, []);

  // Remove item from cache - COMMENTED OUT
  const removeFromCache = useCallback(async (id: string): Promise<void> => {
    // try {
    //   await uploadCacheDB.uploadQueue.delete(id);
    // } catch (error) {
    //   console.error('Failed to remove item from cache:', error);
    // }
    
    // Temporary: No cache removal to avoid lag
    console.log('Cache removal disabled temporarily for performance', { id });
  }, []);

  // Clear completed/cancelled uploads from cache - COMMENTED OUT
  const clearCompletedFromCache = useCallback(async (): Promise<void> => {
    // try {
    //   await uploadCacheDB.uploadQueue
    //     .where('status')
    //     .anyOf(['completed', 'cancelled'])
    //     .delete();
    // } catch (error) {
    //   console.error('Failed to clear completed items from cache:', error);
    // }
    
    // Temporary: No cache clearing to avoid lag
    console.log('Cache clear completed disabled temporarily for performance');
  }, []);

  // Clear uploads with 100% progress but wrong status - COMMENTED OUT
  const clearStaleUploads = useCallback(async (): Promise<void> => {
    // try {
    //   const allItems = await uploadCacheDB.uploadQueue.toArray();
    //   const shouldCleanup = allItems.filter(item => 
    //     item.progress === 100 && 
    //     item.status !== 'completed' && 
    //     item.status !== 'processing'
    //   );
      
    //   for (const item of shouldCleanup) {
    //     await uploadCacheDB.uploadQueue.delete(item.id);
    //   }
    // } catch (error) {
    //   console.error('Failed to clear stale uploads:', error);
    // }
    
    // Temporary: No stale upload clearing to avoid lag
  }, []);

  // Clear all cache for a specific project - COMMENTED OUT
  const clearProjectCache = useCallback(async (projectId: string): Promise<void> => {
    // try {
    //   await uploadCacheDB.uploadQueue
    //     .where('projectId')
    //     .equals(projectId)
    //     .delete();
    // } catch (error) {
    //   console.error('Failed to clear project cache:', error);
    // }
    
    // Temporary: No project cache clearing to avoid lag
    console.log('Project cache clearing disabled temporarily for performance', { projectId });
  }, []);

  return {
    saveToCache,
    loadFromCache,
    updateCacheItem,
    removeFromCache,
    clearCompletedFromCache,
    clearStaleUploads,
    clearProjectCache,
  };
}; 