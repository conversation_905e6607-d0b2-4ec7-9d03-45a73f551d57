import { useSelector } from 'react-redux';
import { useRef, useEffect, useCallback } from 'react';

import { selectActiveResourceUploadsForProject } from 'src/store/slices/resources/selectors';

interface UseUploadCompletionRefetchProps {
  projectId: string;
  onRefetch: () => void;
  debounceMs?: number;
}

export const useUploadCompletionRefetch = ({
  projectId,
  onRefetch,
  debounceMs = 500,
}: UseUploadCompletionRefetchProps) => {
  const pendingUploads = useSelector((state) =>
    selectActiveResourceUploadsForProject(state, projectId)
  );
  
  const previousUploadIdsRef = useRef<Set<string>>(new Set());
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const pendingRefetchRef = useRef(false);

  // Debounced refetch function
  const debouncedRefetch = useCallback(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      if (pendingRefetchRef.current) {
        onRefetch();
        pendingRefetchRef.current = false;
      }
    }, debounceMs);
  }, [onRefetch, debounceMs]);

  // Listen for changes in pending uploads
  useEffect(() => {
    const currentUploadIds = new Set(pendingUploads.map((upload) => upload.id));
    const previousUploadIds = previousUploadIdsRef.current;

    // Check if any uploads were completed (removed from pending list)
    const completedUploads = [...previousUploadIds].filter(
      (id) => !currentUploadIds.has(id)
    );

    // If there are completed uploads, schedule a refetch
    if (completedUploads.length > 0) {
      pendingRefetchRef.current = true;
      debouncedRefetch();
    }

    // Update ref for next comparison
    previousUploadIdsRef.current = currentUploadIds;
  }, [pendingUploads, debouncedRefetch]);

  // Cleanup timeout on unmount
  useEffect(() => () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    }, []);

  return {
    pendingUploads,
    hasPendingRefetch: pendingRefetchRef.current,
  };
};

export default useUploadCompletionRefetch; 