import type { ResourceUploadQueueItem } from 'src/types';

import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { selectResourceUploadQueue } from 'src/store/slices/resources/selectors';
import { 
  addToUploadQueue, 
  updateUploadQueueItem,
  removeFromUploadQueue,
  clearCompletedUploads
} from 'src/store/slices/resources/slice';

import { useUploadCache } from './use-upload-cache';

export const useUploadQueueCacheSync = (projectId?: string) => {
  const dispatch = useDispatch();
  const uploadQueue = useSelector(selectResourceUploadQueue);
  const {
    // saveToCache,
    loadFromCache,
    // updateCacheItem,
    // removeFromCache,
    // clearCompletedFromCache,
  } = useUploadCache();

  // Load cache on initial mount - COMMENTED OUT
  useEffect(() => {
    const loadCachedUploads = async () => {
      // try {
      //   const cachedItems = await loadFromCache(projectId);
      //   if (cachedItems.length > 0) {
      //     // Reset stuck uploads to pending status when restoring from cache
      //     const restoredItems = cachedItems.map(item => {
      //       if (item.status === 'uploading' || item.status === 'processing') {
      //         return {
      //           ...item,
      //           status: 'pending' as const,
      //           progress: 0,
      //           cancelToken: undefined,
      //         };
      //       }
      //       return item;
      //     });
          
      //     dispatch(loadUploadQueueFromCache(restoredItems));
          
      //     // Update cache with reset statuses
      //     for (const item of restoredItems) {
      //       if (item.status === 'pending' && (cachedItems.find(c => c.id === item.id)?.status !== 'pending')) {
      //         try {
      //           await updateCacheItem(item.id, { status: 'pending', progress: 0 });
      //         } catch (error) {
      //           console.error('Failed to update cache after reset:', error);
      //         }
      //       }
      //     }
      //   }
      // } catch (error) {
      //   console.error('Failed to load cached uploads:', error);
      // }
      
      // Temporary: Skip cache loading to avoid lag
      console.log('Cache loading disabled temporarily for performance');
    };

    loadCachedUploads();
  }, [dispatch, loadFromCache, projectId]);

  // Enhanced actions that sync with cache - CACHE OPERATIONS COMMENTED OUT
  const addToUploadQueueWithCache = async (item: ResourceUploadQueueItem) => {
    try {
      // Save to IndexedDB first for persistence - COMMENTED OUT
      // await saveToCache({
      //   ...item,
      //   status: 'pending',
      //   submittedAt: new Date(),
      // });
      
      // Only add to Redux (no cache to avoid lag)
      dispatch(addToUploadQueue(item));
      
      console.log('Cache save skipped for performance - added to Redux only');
      
    } catch (error) {
      console.error('Failed to add to Redux:', error);
      throw error;
    }
  };

  const updateUploadQueueItemWithCache = async (
    id: string, 
    data: Partial<ResourceUploadQueueItem>
  ) => {
    // Update Redux store first
    dispatch(updateUploadQueueItem({ id, data }));
    
    // try {
    //   // Then update cache - COMMENTED OUT
    //   if (data.status || data.progress !== undefined) {
    //     await updateCacheItem(id, {
    //       status: data.status,
    //       progress: data.progress,
    //     });
    //   }

    //   // Remove from cache if completed/cancelled/failed - COMMENTED OUT
    //   if (data.status && ['completed', 'cancelled', 'failed'].includes(data.status)) {
    //     await removeFromCache(id);
    //   }
    // } catch (error) {
    //   console.error('Failed to update cache item:', error);
    // }
    
    // Temporary: Skip cache operations to avoid lag
  };

  const removeFromUploadQueueWithCache = async (id: string) => {
    dispatch(removeFromUploadQueue({ id }));
    // try {
    //   await removeFromCache(id);
    // } catch (error) {
    //   console.error('Failed to remove from cache:', error);
    // }
    
    // Temporary: Skip cache removal to avoid lag
  };

  const clearCompletedUploadsWithCache = async () => {
    dispatch(clearCompletedUploads());
    // try {
    //   await clearCompletedFromCache();
    // } catch (error) {
    //   console.error('Failed to clear completed from cache:', error);
    // }
    
    // Temporary: Skip cache clearing to avoid lag
  };

  return {
    addToUploadQueueWithCache,
    updateUploadQueueItemWithCache,
    removeFromUploadQueueWithCache,
    clearCompletedUploadsWithCache,
  };
}; 