import { useEffect } from 'react';
import { useSelector } from 'react-redux';

import { selectResourceUploadQueue } from 'src/store/slices/resources/selectors';

/**
 * Custom hook that shows browser warning when user tries to leave page
 * while there are active uploads in progress
 */
export const useUploadWarning = () => {
  const uploadQueue = useSelector(selectResourceUploadQueue);

  useEffect(() => {
    // Check if there are any active uploads (not completed, cancelled, or failed)
    const hasActiveUploads = uploadQueue.some(item => 
      item.status === 'pending' || 
      item.status === 'uploading' || 
      item.status === 'processing'
    );

    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (hasActiveUploads) {
        // Standard message for modern browsers
        const message = 'Bạn có file đang upload. Thoát trang sẽ dừng quá trình upload. Bạn có chắc chắn muốn rời khỏi?';
        
        // Set returnValue for older browsers
        event.returnValue = message;
        
        // Return message for some browsers
        return message;
      }
      
      // Return undefined when no active uploads
      return undefined;
    };

    const handlePopState = (event: PopStateEvent) => {
      if (hasActiveUploads) {
        const confirmLeave = window.confirm(
          'Bạn có file đang upload. Rời khỏi trang sẽ dừng quá trình upload. Bạn có chắc chắn muốn tiếp tục?'
        );
        
        if (!confirmLeave) {
          // Push current state back to prevent navigation
          window.history.pushState(null, '', window.location.href);
        }
      }
    };

    if (hasActiveUploads) {
      // Add beforeunload listener for page refresh/close
      window.addEventListener('beforeunload', handleBeforeUnload);
      
      // Add popstate listener for back/forward navigation
      window.addEventListener('popstate', handlePopState);
      
      // Push a dummy state to handle back button
      window.history.pushState(null, '', window.location.href);
    }

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('popstate', handlePopState);
    };
  }, [uploadQueue]);

  // No need to return UI-related data since we're not showing upload status
}; 
