import { useMemo } from 'react';

import { paths } from 'src/routes/paths';

import { useAppSelector } from 'src/store';
import { useGetUserInitialContextQuery } from 'src/store/api/user';
import { selectLastViewedProjectId } from 'src/store/slices/settings/selectors';

import { useAuthContext } from 'src/auth/hooks';

const useUserInitialContext = () => {
  const currentProjectId = useAppSelector(selectLastViewedProjectId);
  const { authenticated } = useAuthContext();

  const { data, isLoading, refetch } = useGetUserInitialContextQuery(
    {},
    {
      refetchOnFocus: true,
      refetchOnMountOrArgChange: true,
      skip: !authenticated,
    }
  );

  const projects = useMemo(() => {
    const projectList = data?.projects || [];
    return [...projectList].sort((a, b) => {
      // First priority: Default project at the top
      if (a.isDefault && !b.isDefault) return -1;
      if (!a.isDefault && b.isDefault) return 1;

      // Second priority: Alphabetical sorting by name for non-default projects
      return a.name.localeCompare(b.name);
    });
  }, [data]);

  const defaultProject = useMemo(() => projects.find((p) => p.isDefault), [projects]);

  const currentProject = useMemo(
    () => projects.find((p) => p.id === currentProjectId),
    [projects, currentProjectId]
  );

  const getFolderLocation = (projectId?: string, folderId?: string) => {
    if (!projectId) return null;
    const project = projects.find((p) => p.id === projectId);
    if (!project) return null;

    if (folderId) {
      const folder = project.folders.find((f) => f.id === folderId);
      return folder ? { name: folder.name, url: paths.project.folder(projectId, folderId) } : null;
    }

    return { name: project.name, url: paths.project.details(projectId) };
  };

  return {
    projects,
    isLoading,
    getFolderLocation,
    defaultProject,
    currentProject,
    refetchGetUserInitialContext: refetch,
  };
};

export default useUserInitialContext;
