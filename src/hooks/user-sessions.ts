import type { Session } from 'src/types/session';
import type {
  SessionCreatedEvent,
  SessionDeletedEvent,
  ResourceCreatedEvent,
  SessionStatusUpdatedEvent,
} from 'src/types/project-events';

import { useRef, useState, useEffect } from 'react';

import { useGetOngoingSessionsQuery } from 'src/store/api/sessions';

import { RecallBotStatus } from 'src/types/recall';
import { SSEEventType } from 'src/types/project-events';

import { useEventListener } from './use-event-listener';

interface UseUserSessionsProps {
  projectId?: string;
}

const useUserSessions = ({ projectId }: UseUserSessionsProps) => {
  const [ongoingSessions, setOngoingSessions] = useState<Session[]>([]);

  // Use RTK Query for initial data load
  const { data: initialSessions = [] } = useGetOngoingSessionsQuery(
    projectId ? { projectId } : {},
    { skip: !projectId }
  );

  // Track the previous initialSessions to avoid infinite re-renders
  const prevInitialSessionsRef = useRef<Session[]>([]);
  const isInitialMountRef = useRef(true);
  const prevProjectIdRef = useRef<string | undefined>(projectId);

  // Reset initial mount flag when projectId changes
  if (prevProjectIdRef.current !== projectId) {
    isInitialMountRef.current = true;
    prevProjectIdRef.current = projectId;
    prevInitialSessionsRef.current = [];
  }

  // Initialize sessions from RTK Query
  useEffect(() => {
    // On initial mount, always set the data
    if (isInitialMountRef.current) {
      isInitialMountRef.current = false;
      prevInitialSessionsRef.current = initialSessions;
      // Always set initial sessions, even if empty (clears previous project's sessions)
      setOngoingSessions(initialSessions);
      return;
    }

    // For subsequent updates, do a more thorough comparison
    const prevSessions = prevInitialSessionsRef.current;

    // Check if arrays are actually different by content
    // Create maps for efficient comparison by ID (order-independent)
    const currentSessionsMap = new Map(
      initialSessions.map((session) => [
        session.id,
        {
          id: session.id,
          status: session.status,
          title: session.title,
          recallBotStatus: session.recallBotStatus,
          createdAt:
            session.createdAt instanceof Date
              ? session.createdAt.getTime()
              : session.createdAt
                ? new Date(session.createdAt).getTime()
                : 0,
        },
      ])
    );

    const prevSessionsMap = new Map(
      prevSessions.map((session) => [
        session.id,
        {
          id: session.id,
          status: session.status,
          title: session.title,
          recallBotStatus: session.recallBotStatus,
          createdAt:
            session.createdAt instanceof Date
              ? session.createdAt.getTime()
              : session.createdAt
                ? new Date(session.createdAt).getTime()
                : 0,
        },
      ])
    );

    // Check if sessions changed (length or content)
    const hasChanged =
      initialSessions.length !== prevSessions.length ||
      Array.from(currentSessionsMap.entries()).some(([id, current]) => {
        const prev = prevSessionsMap.get(id);
        if (!prev) return true; // New session

        return (
          current.status !== prev.status ||
          current.title !== prev.title ||
          current.recallBotStatus !== prev.recallBotStatus ||
          current.createdAt !== prev.createdAt
        );
      }) ||
      // Check for deleted sessions
      Array.from(prevSessionsMap.keys()).some((id) => !currentSessionsMap.has(id));

    if (!hasChanged) {
      return;
    }

    // Update the ref to track current sessions
    prevInitialSessionsRef.current = [...initialSessions];

    // Merge RTK Query data with existing SSE-created sessions
    setOngoingSessions((currentSessions) => {
      // If no previous sessions, just use RTK Query data
      if (currentSessions.length === 0) {
        return initialSessions;
      }

      // Create a map of existing sessions for efficient lookup
      const existingSessionsMap = new Map(currentSessions.map((session) => [session.id, session]));

      // Start with RTK Query sessions as the base (they're authoritative)
      const mergedSessions: Session[] = [];

      initialSessions.forEach((rtkSession) => {
        // Skip invalid sessions from RTK Query
        if (!rtkSession.id || !rtkSession.title) {
          return;
        }

        const existingSession = existingSessionsMap.get(rtkSession.id);

        if (existingSession) {
          // If session exists in both places, prefer the one with more recent data
          // RTK Query data is typically more authoritative for status updates
          mergedSessions.push({
            ...existingSession,
            ...rtkSession, // RTK Query data takes precedence
          });
          // Remove from map so we don't add it again
          existingSessionsMap.delete(rtkSession.id);
        } else {
          // New session from RTK Query
          mergedSessions.push(rtkSession);
        }
      });

      // Add any remaining SSE-only sessions (ones that haven't been synced to server yet)
      existingSessionsMap.forEach((sseSession) => {
        mergedSessions.push(sseSession);
      });

      // Sort by creation date (newest first) to maintain consistent order
      return mergedSessions.sort((a, b) => {
        const aTime = a.createdAt?.getTime() || 0;
        const bTime = b.createdAt?.getTime() || 0;
        return bTime - aTime;
      });
    });
  }, [initialSessions]);

  // Listen to SSE events for real-time updates

  useEventListener(SSEEventType.SESSION_CREATED, (sessionEvent: SessionCreatedEvent) => {
    if (!projectId) return;

    const newSession: Session = {
      id: sessionEvent.data.sessionId,
      createdById: sessionEvent.userId || '',
      meetingUrl: sessionEvent.data.meetingUrl,
      title: sessionEvent.data.title,
      description: '',
      createdAt: new Date(sessionEvent.data.updatedAt),
      startTime: new Date(sessionEvent.data.updatedAt),
      endTime: new Date(sessionEvent.data.updatedAt),
      status: sessionEvent.data.status as any,
      recallBotStatus: sessionEvent.data.recallBotStatus ?? RecallBotStatus.NotStarted,
    };

    setOngoingSessions((prev) => {
      const exists = prev.some((session) => session.id === newSession.id);
      const updated = exists ? prev : [newSession, ...prev];
      return updated;
    });
  });

  useEventListener(
    SSEEventType.SESSION_STATUS_UPDATED,
    (sessionStatusEvent: SessionStatusUpdatedEvent) => {
      if (!projectId) return;

      setOngoingSessions((prev) => {
        const sessionFound = prev.find(
          (session) => session.id === sessionStatusEvent.data.sessionId
        );

        if (!sessionFound) {
          return prev;
        }

        const updated = prev.map((session) =>
          session.id === sessionStatusEvent.data.sessionId
            ? {
                ...session,
                status: sessionStatusEvent.data.status,
                recallBotStatus: sessionStatusEvent.data.recallBotStatus ?? session.recallBotStatus,
              }
            : session
        );
        return updated;
      });
    }
  );

  useEventListener(SSEEventType.RESOURCE_CREATED, (resourceEvent: ResourceCreatedEvent) => {
    if (!projectId) return;

    if (resourceEvent.data.sessionId) {
      setOngoingSessions((prev) =>
        prev.filter((session) => session.id !== resourceEvent.data.sessionId)
      );
    }
  });

  useEventListener(SSEEventType.RESOURCE_DELETED, (deleteEvent: SessionDeletedEvent) => {
    if (!projectId) return;

    setOngoingSessions((prev) =>
      prev.filter((session) => session.id !== deleteEvent.data.sessionId)
    );
  });

  return {
    ongoingSessions,
  };
};

export default useUserSessions;
