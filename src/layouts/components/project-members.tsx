import type { ProjectUser, ProjectMember } from 'src/types/project';

import { useParams } from 'react-router';
import { useState, useEffect } from 'react';

import Box from '@mui/material/Box';
import { Avatar } from '@mui/material';
import Stack from '@mui/material/Stack';
import Popover from '@mui/material/Popover';
import Typography from '@mui/material/Typography';
import AvatarGroup from '@mui/material/AvatarGroup';

import { ProjectRole, useGetProjectInfoQuery } from 'src/store/api/projects';

import { RoleBadge } from 'src/components/role-badge';
import { AnimateBorder } from 'src/components/animate';

import { useInviteContext } from 'src/sections/projects/components/invite-dialog/context';
import { MembersList } from 'src/sections/projects/components/invite-dialog/components/list-components';

// ----------------------------------------------------------------------

interface MemberAvatarProps {
  member: ProjectMember;
  size?: number;
  isOwner?: boolean;
}

function MemberAvatar({ member, size = 32, isOwner = false }: MemberAvatarProps) {
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);

  const handleMouseEnter = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMouseLeave = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  const gradientColor = isOwner ? 'warning.main' : 'primary.main';

  return (
    <>
      <AnimateBorder
        sx={{ p: "1px", width: size, height: size, borderRadius: '50%', ml: `-${size/4}px`, cursor: 'pointer', mt: 1 }}
        slotProps={{
          primaryBorder: { size: size + 16, width: '2px', sx: { color: gradientColor } },
        }}
      >
        <Avatar
          alt={member.user.displayName}
          src={member.user.photoURL}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          sx={{
            width: size - 4,
            height: size - 4,
            fontSize: size * 0.4,
            cursor: 'pointer',
          }}
        >
          {member.user.displayName?.charAt(0)?.toUpperCase() || member.user.email?.charAt(0)?.toUpperCase()}
        </Avatar>
      </AnimateBorder>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleMouseLeave}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        transformOrigin={{ vertical: 'top', horizontal: 'center' }}
        slotProps={{
          paper: {
            sx: {
              p: 2,
              minWidth: 200,
              mt: 1,
              boxShadow: (theme) => theme.customShadows.dropdown,
              '& .MuiPopover-arrow': {
                color: 'background.paper',
              },
            },
          },
        }}
        disableRestoreFocus
        sx={{
          pointerEvents: 'none',
        }}
      >
        <Stack direction="row" spacing={2} alignItems="center" justifyContent="space-between">
          <Stack direction="column" spacing={.5}>
            <Typography variant="body2" sx={{ fontWeight: 600, mb: 0.5 }}>
              {member.user.displayName || member.user.email}
              {isOwner && (
                <Typography component="span" variant="caption" sx={{ ml: 1, color: 'warning.main' }}>
                  (Owner)
                </Typography>
              )}
            </Typography>
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
              {member.user.email}
            </Typography>
          </Stack>
          <RoleBadge role={member.role} />
        </Stack>
      </Popover>
    </>
  );
}

interface MemberAvatarGroupProps {
  members: ProjectMember[];
  maxVisible?: number;
  avatarSize?: number;
  onClick?: (event: React.MouseEvent<HTMLElement>) => void;
  projectCreated?: ProjectUser;
}

function MemberAvatarGroup({ members, maxVisible = 4, avatarSize = 32, onClick, projectCreated }: MemberAvatarGroupProps) {
  return (
    <AvatarGroup
      max={maxVisible}
      total={members.length + (projectCreated ? 1 : 0)}
      spacing="medium"
      onClick={onClick}
      sx={{ cursor: 'pointer' }}
      slotProps={{
        surplus: {
          sx: {
            width: avatarSize,
            height: avatarSize,
            fontSize: avatarSize * 0.4,
            bgcolor: 'action.hover',
            color: 'text.secondary',
            '&:hover': {
              bgcolor: 'action.selected',
            },
          },
        },
      }}
    >
      {(members ?? []).map((member) => (
        <MemberAvatar 
          key={member.id} 
          member={member} 
          size={avatarSize} 
          isOwner={false}
        />
      ))}
      {projectCreated && (
        <MemberAvatar 
          key={projectCreated.uid} 
          member={{
            id: projectCreated.uid,
            projectId: "",
            userId: projectCreated.uid,
            createdAt: new Date(),
            role: ProjectRole.OWNER,
            user: projectCreated,
          }} 
          size={avatarSize} 
          isOwner
        />
      )}
    </AvatarGroup>
  );
}

export function ProjectMembers() {
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const { id: projectId } = useParams();

  const {
    users,
    isLoading,
    isRemoving,
    fetchUsersData,
  } = useInviteContext();

  // Get project data to determine ownership
  const { data: project } = useGetProjectInfoQuery(
    { id: projectId! },
    {
      skip: !projectId,
    }
  );

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  useEffect(() => {
    fetchUsersData();
  }, [fetchUsersData]);

  const open = Boolean(anchorEl);

  if(users.length === 0) return null;

  return (
    <>
      <Box>
        <MemberAvatarGroup
          members={users}
          maxVisible={4}
          avatarSize={32}
          onClick={handleClick}
          projectCreated={project?.createdBy}
        />
      </Box>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        slotProps={{
          paper: {
            sx: {
              minWidth: 320,
              p: 0,
              pb: 2,
              mt: 1,
              overflow: 'hidden',
              boxShadow: (theme) => theme.customShadows.dropdown,
            },
          },
        }}
      >
        <Box sx={{ p: 2, pb: 1 }}>
          <Typography variant="body2" color="text.secondary">
            {users.length + (project?.createdBy ? 1 : 0)} member{users.length !== 1 ? 's' : ''}
          </Typography>
        </Box>
        <MembersList
          data={users}
          loading={isLoading}
          onRoleChange={() => {}}
          isRemoving={isRemoving}
          hideActions
          projectCreated={project?.createdBy}
        />
      </Popover>
    </>
  );
}
