import type { Breakpoint } from '@mui/material/styles';
import type { NavSectionProps } from 'src/components/nav-section';

import { merge } from 'es-toolkit';
import { useDispatch } from 'react-redux';
import { useBoolean } from 'minimal-shared/hooks';
import { isActiveLink } from 'minimal-shared/utils';

import Box from '@mui/material/Box';
import Alert from '@mui/material/Alert';
import { useTheme } from '@mui/material/styles';
import { iconButtonClasses } from '@mui/material/IconButton';

import { paths } from 'src/routes/paths';
import { useParams, usePathname } from 'src/routes/hooks';

import useNavConfig from 'src/hooks/nav-config';

import { useAppSelector } from 'src/store';
import {
  openNavigationDrawer,
  closeNavigationDrawer,
  selectNavigationDrawerOpen,
} from 'src/store/slices/ui';

import { useSettingsContext } from 'src/components/settings';
import { NavigationDrawer } from 'src/components/settings/drawer/navigation-drawer';

import ProjectBreadcrumbs from 'src/sections/projects/components/project-breadcrumbs';

import { NavMobile } from './nav-mobile';
import { layoutClasses } from '../core/classes';
import { MainSection } from '../core/main-section';
import { MenuButton } from '../components/menu-button';
import { HeaderSection } from '../core/header-section';
import { LayoutSection } from '../core/layout-section';
import { InviteButton } from '../components/invite-button';
import { AccountDrawer } from '../components/account-drawer';
import { RegionPopover } from '../components/region-popover';
import { ProjectMembers } from '../components/project-members';
import { SettingsButton } from '../components/settings-button';
import { LeaveProjectButton } from '../components/leave-project-button';
import { dashboardLayoutVars, dashboardNavColorVars } from './css-vars';

import type { MainSectionProps } from '../core/main-section';
import type { HeaderSectionProps } from '../core/header-section';
import type { LayoutSectionProps } from '../core/layout-section';

// ----------------------------------------------------------------------

type LayoutBaseProps = Pick<LayoutSectionProps, 'sx' | 'children' | 'cssVars'>;

export type DashboardLayoutProps = LayoutBaseProps & {
  layoutQuery?: Breakpoint;
  slotProps?: {
    header?: HeaderSectionProps;
    nav?: {
      data?: NavSectionProps['data'];
    };
    main?: MainSectionProps;
  };
};

export function DashboardLayout({
  sx,
  cssVars,
  children,
  slotProps,
  layoutQuery = 'lg',
}: DashboardLayoutProps) {
  const theme = useTheme();
  const dispatch = useDispatch();
  const pathname = usePathname();
  const { id: projectId = '' } = useParams();

  const settings = useSettingsContext();

  const navVars = dashboardNavColorVars(theme, settings.state.navColor, settings.state.navLayout);

  const { value: open, onFalse: onClose, onTrue: onOpen } = useBoolean();

  // Navigation drawer state from Redux
  const navigationDrawerOpen = useAppSelector(selectNavigationDrawerOpen);

  const handleOpenNavigationDrawer = () => {
    dispatch(openNavigationDrawer());
  };

  const handleCloseNavigationDrawer = () => {
    dispatch(closeNavigationDrawer());
  };

  const navData = useNavConfig();

  const isNavHorizontal = settings.state.navLayout === 'horizontal';
  const isNavVertical = settings.state.navLayout === 'vertical';

  // Check if we're on project details page (including project details or folder details)
  const isProjectDetailsPage =
    projectId &&
    (isActiveLink(pathname, paths.project.details(projectId)) ||
      pathname.startsWith(paths.project.details(projectId)));

  const renderHeader = () => {
    const headerSlotProps: HeaderSectionProps['slotProps'] = {
      container: {
        maxWidth: false,
        sx: {
          ...(isNavVertical && { px: { [layoutQuery]: 5 } }),
          ...(isNavHorizontal && {
            bgcolor: 'var(--layout-nav-bg)',
            height: { [layoutQuery]: 'var(--layout-nav-horizontal-height)' },
            [`& .${iconButtonClasses.root}`]: { color: 'var(--layout-nav-text-secondary-color)' },
          }),
        },
      },
    };

    const headerSlots: HeaderSectionProps['slots'] = {
      topArea: (
        <Alert severity="info" sx={{ display: 'none', borderRadius: 0 }}>
          This is an info Alert.
        </Alert>
      ),
      bottomArea: null,
      leftArea: (
        <>
          {navData.length > 0 && (
            <>
              {/** @slot Nav mobile */}
              <MenuButton
                onClick={onOpen}
                sx={{ mr: 1, ml: -1, [theme.breakpoints.up(layoutQuery)]: { display: 'none' } }}
              />
              <NavMobile data={navData} open={open} onClose={onClose} cssVars={navVars.section} />
            </>
          )}

          {/** @slot Menu Icon Button */}
          {isNavHorizontal && (
            <MenuButton
              onClick={handleOpenNavigationDrawer}
              sx={{ mr: 1, ml: -1, [theme.breakpoints.down(layoutQuery)]: { display: 'none' } }}
            />
          )}

          <ProjectBreadcrumbs />
        </>
      ),
      rightArea: (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: { xs: 0, sm: 0.75 } }}>
          {/** @slot Project members - only show on project details pages */}
          {isProjectDetailsPage && <ProjectMembers />}

          {/** @slot Project action buttons - only show on project details pages */}
          {isProjectDetailsPage && (
            <>
              <InviteButton />
              <LeaveProjectButton />
            </>
          )}

          {/** @slot Language popover */}
          <RegionPopover />

          {/** @slot Settings button */}
          <SettingsButton />

          {/** @slot Account drawer */}
          <AccountDrawer />
        </Box>
      ),
    };

    return (
      <HeaderSection
        layoutQuery={layoutQuery}
        disableElevation={isNavVertical}
        {...slotProps?.header}
        slots={{ ...headerSlots, ...slotProps?.header?.slots }}
        slotProps={merge(headerSlotProps, slotProps?.header?.slotProps ?? {})}
        sx={slotProps?.header?.sx}
      />
    );
  };

  const renderFooter = () => null;

  const renderMain = () => <MainSection {...slotProps?.main}>{children}</MainSection>;

  return (
    <LayoutSection
      /** **************************************
       * @Header
       *************************************** */
      headerSection={renderHeader()}
      /** **************************************
       * @Sidebar
       *************************************** */
      sidebarSection={null}
      /** **************************************
       * @Footer
       *************************************** */
      footerSection={renderFooter()}
      /** **************************************
       * @Styles
       *************************************** */
      cssVars={{ ...dashboardLayoutVars(theme), ...navVars.layout, ...cssVars }}
      sx={[
        {
          [`& .${layoutClasses.sidebarContainer}`]: {
            [theme.breakpoints.up(layoutQuery)]: {
              pl: 'var(--layout-nav-vertical-width)',
              transition: theme.transitions.create(['padding-left'], {
                easing: 'var(--layout-transition-easing)',
                duration: 'var(--layout-transition-duration)',
              }),
            },
          },
        },
        ...(Array.isArray(sx) ? sx : [sx]),
      ]}
    >
      {renderMain()}

      {/** @slot Navigation Drawer */}
      <NavigationDrawer open={navigationDrawerOpen} onClose={handleCloseNavigationDrawer} />
    </LayoutSection>
  );
}
