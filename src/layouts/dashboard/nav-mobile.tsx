import type { NavSectionProps } from 'src/components/nav-section';

import { useEffect, useCallback } from 'react';
import { mergeClasses } from 'minimal-shared/utils';

import Box from '@mui/material/Box';
import Drawer from '@mui/material/Drawer';
import { Typography } from '@mui/material';

import { paths } from 'src/routes/paths';
import { useRouter, usePathname } from 'src/routes/hooks';

import { Logo } from 'src/components/logo';
import { Scrollbar } from 'src/components/scrollbar';
import { NavSectionVertical } from 'src/components/nav-section';
import { ProjectAutocomplete } from 'src/components/project-autocomplete';

import { layoutClasses } from '../core/classes';

// ----------------------------------------------------------------------

type NavMobileProps = NavSectionProps & {
  open: boolean;
  onClose: () => void;
  slots?: {
    topArea?: React.ReactNode;
    bottomArea?: React.ReactNode;
  };
};

export function NavMobile({ data, open, onClose, slots, sx, className, ...other }: NavMobileProps) {
  const pathname = usePathname();
  const router = useRouter();

  useEffect(() => {
    if (open) {
      onClose();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname]);

  const handleProjectSelect = useCallback((event: any, project: any) => {
    if (project) {
      router.push(paths.project.details(project.id));
      onClose();
    }
  }, []);

  const renderTopArea = () => (
    <Box sx={{ px: 2, pt: 2.5, pb: 1 }}>
      <Box sx={{ pl: 3.5, pt: 2.5, pb: 1 }}>
        <Logo />
      </Box>
      <Box sx={{ pb: 1 }}>
        <Typography 
          variant="overline" 
          sx={{ 
            color: 'text.disabled',
            display: 'block',
            mb: 1,
            fontSize: '0.75rem',
            fontWeight: 600,
            letterSpacing: 1.2
          }}
        >
          Quick Project Search
        </Typography>
        <ProjectAutocomplete
          placeholder="Search projects..."
          onChange={handleProjectSelect}
          size="small"
        />
      </Box>
    </Box>
  );

  return (
    <Drawer
      open={open}
      onClose={onClose}
      PaperProps={{
        className: mergeClasses([layoutClasses.nav.root, layoutClasses.nav.vertical, className]),
        sx: [
          (theme) => ({
            overflow: 'unset',
            bgcolor: 'var(--layout-nav-bg)',
            width: 'var(--layout-nav-mobile-width)',
          }),
          ...(Array.isArray(sx) ? sx : [sx]),
        ],
      }}
    >
      {slots?.topArea ?? (
        renderTopArea()
      )}

      <Scrollbar fillContent>
        <NavSectionVertical data={data} sx={{ px: 2, flex: '1 1 auto' }} {...other} />
      </Scrollbar>

      {slots?.bottomArea}
    </Drawer>
  );
}
