import type { GenerativeModel } from 'firebase/vertexai';

import { getAuth } from 'firebase/auth';
import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getAnalytics } from 'firebase/analytics';
import { getAI, VertexAIBackend, getGenerativeModel } from 'firebase/vertexai';

import { CONFIG } from 'src/global-config';

// ----------------------------------------------------------------------

export const firebaseApp = initializeApp(CONFIG.firebase);

export const AUTH = getAuth(firebaseApp);

export const FIRESTORE = getFirestore(firebaseApp);

export const VERTEXAI = getAI(firebaseApp, {
  backend: new VertexAIBackend('us-central1'),
});

export type GeminiModelType = 'gemini-2.5-flash' | 'gemini-2.5-pro';

// Model-specific configurations based on latest Google Cloud best practices (2025)
const MODEL_CONFIGS = {
  'gemini-2.5-flash': {
    // Optimized for speed while maintaining quality
    temperature: 0.3, // Lower for more focused, consistent responses
    topK: 40, // Fixed at 40 (Google's recommended default)
    topP: 0.95, // High for natural generation while maintaining focus
    maxOutputTokens: 65536, // Maximum supported by Flash model
  },
  'gemini-2.5-pro': {
    // Optimized for advanced reasoning and comprehensive responses
    temperature: 0.2, // Even lower for more precise, analytical responses
    topK: 40, // Fixed at 40 (Google's recommended default)
    topP: 0.95, // Balanced for detailed yet focused responses
    maxOutputTokens: 65536, // Maximum supported by Pro model
  },
} as const;

// Create a model instance for a specific model type
export const createGeminiModel = (modelType: GeminiModelType): GenerativeModel => {
  const config = MODEL_CONFIGS[modelType];

  return getGenerativeModel(
    VERTEXAI,
    {
      model: modelType,
      generationConfig: config,
    },
    {
      timeout: 120 * 1000, // 2-minute timeout for chat responses
    }
  );
};

// Default model instance (for backward compatibility)
export const geminiModel: GenerativeModel = createGeminiModel(
  CONFIG.firebase.geminiModel as GeminiModelType
);
export const geminiFlashModel: GenerativeModel = createGeminiModel('gemini-2.5-flash');
export const firebaseAnalytics = getAnalytics(firebaseApp);
