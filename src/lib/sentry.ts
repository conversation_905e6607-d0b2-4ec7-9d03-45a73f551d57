import * as Sentry from '@sentry/react';

import { AUTH } from 'src/lib/firebase';
import { CONFIG } from 'src/global-config';

const initSentry = () => {
  Sentry.init({
    environment: import.meta.env.MODE,
    dsn: CONFIG.sentry.dsn,
    release:
      import.meta.env.VITE_SENTRY_RELEASE ||
      `aida-${import.meta.env.MODE}-${import.meta.env.VITE_GIT_SHA || 'dev'}`,
    // Filter out known noisy errors at SDK level (most efficient)
    ignoreErrors: [
      // Browser extension errors
      'top.GLOBALS',
      'originalCreateNotification',
      'canvas.contentDocument',
      'MyApp_RemoveAllHighlights',
      'http://tt.epicplay.com',
      "Can't find variable: ZiteReader",
      'jigsaw is not defined',
      'ComboSearch is not defined',
      'http://loading.retry.widdit.com/',
      'atomicFindClose',
      // Network/loading errors
      'Network request failed',
      'NetworkError when attempting to fetch resource',
      'Failed to fetch',
      'Load failed',
      // PostHog warnings
      /PostHog.*client.*already loaded/,
      // Firebase auth warnings that are handled in UI
      'Firebase: Error (auth/invalid-credential)',
      // Common user errors that are handled gracefully
      'Cache load disabled temporarily for performance',
      'Cache loading disabled temporarily for performance',
      'Cache save skipped for performance',
      // Firebase analytics warnings
      /@firebase\/analytics.*Failed to fetch.*measurement ID/,
      // Common minified errors that users can't act on
      /v\.onerror/,
      /y\.onerror/,
      /request\.onerror/,
      /v\.ontimeout/,
      /y\.ontimeout/,
      // Unknown/minified error codes
      /^[a-zA-Z]{1,3}$/,
      'k8e',
      'w4e',
      'afe',
      'jye',
      'f3e',
      'hDe',
      'jee',
      'nf',
      'Qse',
      'c5e',
      'gte',
      'boe',
      'oOe',
      'tOe',
      'jse',
      'X5e',
      'I5e',
      'Ire',
      'zye',
      'vG',
      // Generic errors
      '<unknown>',
      /baseApiCall/,
    ],
    integrations: [
      Sentry.browserTracingIntegration(),
      Sentry.replayIntegration(),
      // Enable automatic session tracking for release health
      Sentry.httpContextIntegration(),
      Sentry.captureConsoleIntegration({
        // Only capture console errors and warnings, not info/log/debug
        levels: ['error', 'warn'],
      }),
    ],
    // Tracing - environment-specific sampling rates
    tracesSampleRate:
      import.meta.env.MODE === 'production' ? 0.1 : import.meta.env.MODE === 'staging' ? 0.5 : 1.0, // development gets full tracing
    // Set 'tracePropagationTargets' to control for which URLs distributed tracing should be enabled
    tracePropagationTargets: [
      'localhost',
      /^https:\/\/.*\.beings\.ai\/api/,
      /^https:\/\/.*\.beings\.com\/api/,
      /^https:\/\/aida-service-.*\.europe-west2\.run\.app\/api/,
      new RegExp(`^${CONFIG.aidaApiUrl}`),
    ],
    // Session Replay - environment-specific rates
    replaysSessionSampleRate:
      import.meta.env.MODE === 'production' ? 0.1 : import.meta.env.MODE === 'staging' ? 0.2 : 1.0, // development gets full replay
    replaysOnErrorSampleRate: 1.0, // Always capture replays on errors
    // Enhanced beforeSend for better error filtering and context
    beforeSend(event) {
      // In development, don't send events to Sentry
      if (import.meta.env.MODE === 'development') {
        console.log('Sentry Event (dev mode):', event);
        return null;
      }

      // Add additional context for all events
      if (event.contexts) {
        event.contexts.app = {
          ...event.contexts.app,
          name: 'aida',
          version: CONFIG.appVersion,
          environment: import.meta.env.MODE,
        };
      }

      return event;
    },
  });

  // Set global context with build and deployment information
  Sentry.setContext('build', {
    environment: import.meta.env.MODE,
    version: CONFIG.appVersion,
    buildTime: new Date().toISOString(),
  });

  // Set up user context when authentication state changes
  AUTH.onAuthStateChanged((user) => {
    Sentry.setUser(
      user
        ? {
            id: user.uid,
            email: user.email || undefined,
            username: user.displayName || undefined,
          }
        : null
    );
  });
};

export default initSentry;
