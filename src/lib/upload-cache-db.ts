// TEMPORARILY DISABLED - This file contains IndexedDB operations that cause performance lag
// All operations from this file are currently commented out in:
// - src/hooks/use-upload-cache.ts 
// - src/hooks/use-upload-queue-cache-sync.ts

import Dexie, { type EntityTable } from 'dexie';

export interface CachedUploadQueueItem {
  id: string;
  fileName: string;
  fileSize: number;
  fileLastModified: Date;
  fileType: string;
  uploadUrlData: any; // Store serialized upload URL data
  progress?: number;
  status?: 'pending' | 'uploading' | 'processing' | 'completed' | 'failed' | 'cancelled';
  submittedAt?: Date;
  projectId?: string;
  folderId?: string;
  transcoded?: boolean;
  mode?: 'delete-original' | 'keep-both';
  // Serialize file as ArrayBuffer for storage
  fileData?: ArrayBuffer;
}

export class UploadCacheDB extends Dexie {
  // Declare tables
  uploadQueue!: EntityTable<CachedUploadQueueItem, 'id'>;

  constructor() {
    super('UploadCacheDB');
    
    this.version(1).stores({
      uploadQueue: 'id, projectId, status, submittedAt',
    });
  }
}

export const uploadCacheDB = new UploadCacheDB();

// Helper functions to convert between File and CachedUploadQueueItem
export const fileToBuffer = async (file: File): Promise<ArrayBuffer> => new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as ArrayBuffer);
    reader.onerror = () => reject(reader.error);
    reader.readAsArrayBuffer(file);
  });

export const bufferToFile = (buffer: ArrayBuffer, fileName: string, fileType: string, lastModified: Date): File => {
  const blob = new Blob([buffer], { type: fileType });
  return new File([blob], fileName, { 
    lastModified: lastModified.getTime() 
  });
}; 