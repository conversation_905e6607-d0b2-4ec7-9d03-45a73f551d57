import { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';

import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import { Box, Button } from '@mui/material';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';

import { paths } from 'src/routes/paths';
import { useRouter, useSearchParams } from 'src/routes/hooks';

import useUserInitialContext from 'src/hooks/user-initial-context';

import { CONFIG } from 'src/global-config';
import { useAcceptInvitationMutation } from 'src/store/api/projects';

import { LoadingScreen } from 'src/components/loading-screen';

// ----------------------------------------------------------------------

const metadata = { title: `${CONFIG.appName} - Accept Project Invitation` };

export default function AcceptProjectInvitationPage() {
  useUserInitialContext();

  const router = useRouter();
  const searchParams = useSearchParams();

  // Get the invitation code and projectId from URL parameters
  const code = searchParams.get('code');
  const projectId = searchParams.get('projectId');

  const [status, setStatus] = useState<'loading' | 'error' | 'success'>('loading');
  const [errorMessage, setErrorMessage] = useState<string>('');

  const [acceptInvitation] = useAcceptInvitationMutation();

  useEffect(() => {
    const handleAcceptInvitation = async () => {
      // Check if required parameters are present
      if (!code || !projectId) {
        setStatus('error');
        setErrorMessage('Invalid invitation link. Missing required parameters.');
        return;
      }

      try {
        // Call the API to accept the invitation
        await acceptInvitation({
          id: projectId,
          payload: {
            code,
          },
        }).unwrap();

        setStatus('success');

        // Redirect to project details page after a short delay
        setTimeout(() => {
          router.replace(paths.project.details(projectId));
        }, 1000);
      } catch (error) {
        setStatus('error');
        setErrorMessage(
          'Failed to accept invitation. The invitation may have expired or been revoked.'
        );
        console.error('Error accepting invitation:', error);
      }
    };

    handleAcceptInvitation();
  }, [code, projectId, acceptInvitation, router]);

  if (status === 'loading') {
    return <LoadingScreen />;
  }

  return (
    <>
      <Helmet>
        <title>{metadata.title}</title>
      </Helmet>

      <Container maxWidth="sm" sx={{ py: 10 }}>
        <Card sx={{ p: 5 }}>
          {status === 'success' && (
            <Box>
              <Stack spacing={1} alignItems="center">
                <Typography variant="h4">
                  Success - Invite accepted
                </Typography>
                <Card
                  sx={(theme) => ({
                    width: '100%',
                    p: 3,
                    backgroundColor: theme.vars.palette.background.neutral,
                    border: `1px solid ${theme.vars.palette.divider}`,
                  })}
                >
                  <Typography variant="body2">You&apos;re in! Redirecting to the project.</Typography>
                </Card>
              </Stack>
            </Box>
          )}

          {status === 'error' && (
            <Box>
               <Stack spacing={1} alignItems="center">
                <Typography variant="h4" sx={{ textAlign: 'center' }}>
                  Can&apos;t open this link
                </Typography>
                <Typography variant="body1" sx={{ textAlign: 'center', mb: 3 }}>
                  It may be expired or no longer active.
                  Try asking the owner to resend it.
                </Typography>
                <Button
                  variant="outlined"
                  color="secondary"
                  onClick={() => router.push(paths.project.root)}
                  sx={{
                    minWidth: 120,
                  }}
                >
                  Back to latest project
                </Button>
              </Stack>
             
            </Box>
          )}
        </Card>
      </Container>
    </>
  );
}
