import type { Resource } from 'src/types/resource';

import { toast } from 'sonner';
import { useParams } from 'react-router';
import { Helmet } from 'react-helmet-async';
import { useRef, useState, useEffect, useCallback } from 'react';

import {
  Box,
  Card,
  Fade,
  Stack,
  Button,
  Container,
  Typography,
  CardContent,
  LinearProgress,
  CircularProgress,
} from '@mui/material';

import useAnalytics from 'src/hooks/analytics';

import { CONFIG } from 'src/global-config';
import { SimpleLayout } from 'src/layouts/simple';
import { useGetResourceQuery } from 'src/store/api/resources/hooks';

import { Iconify } from 'src/components/iconify';
import { LoadingScreen } from 'src/components/loading-screen';

import useDownloadTranscription from 'src/sections/resources/hooks/download-transcription';

import { useAuthContext } from 'src/auth/hooks';

// ----------------------------------------------------------------------

const metadata = { title: `${CONFIG.appName} - Download` };

// ----------------------------------------------------------------------

type DownloadState = 'idle' | 'checking' | 'downloading' | 'cancelled' | 'error' | 'success';

interface DownloadPageContentProps {
  resourceId: string;
}

// Custom hook for download management
const useDownloadManager = (resource: Resource | undefined) => {
  const { trackEvent } = useAnalytics();
  const downloadTranscription = useDownloadTranscription();
  const [state, setState] = useState<DownloadState>('idle');
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string>('');

  const timeoutsRef = useRef<NodeJS.Timeout[]>([]);
  const isMountedRef = useRef(true);

  // Cleanup function
  const cleanup = useCallback(() => {
    timeoutsRef.current.forEach((timeout) => clearTimeout(timeout));
    timeoutsRef.current = [];
  }, []);

  // Helper for managed timeouts
  const createTimeout = useCallback((callback: () => void, delay: number): NodeJS.Timeout => {
    const timeout = setTimeout(() => {
      timeoutsRef.current = timeoutsRef.current.filter((t) => t !== timeout);
      callback();
    }, delay);

    timeoutsRef.current.push(timeout);
    return timeout;
  }, []);

  // Start download process
  const startDownload = useCallback(async () => {
    console.log('startDownload', resource);
    if (!resource) {
      setState('error');
      setError('Resource not available');
      return;
    }

    // Check if duration is invalid
    if ((resource.duration ?? -1) < 0) {
      setState('error');
      setError('This resource has invalid duration and cannot be downloaded as transcript');
      return;
    }

    // Check if transcriptions are available
    if (
      !resource.transcription ||
      !Array.isArray(resource.transcription) ||
      resource.transcription.length === 0
    ) {
      setState('error');
      setError('No transcript available for this resource');
      return;
    }

    try {
      setState('downloading');
      setProgress(0);
      setError('');

      // Track download event
      trackEvent({
        eventCategory: 'Resource',
        eventAction: 'Download transcript',
        properties: {
          resourceId: resource.id,
          resourceName: resource.name,
        },
      });

      // Show download started toast
      toast.success(`Download started for ${resource.name} transcript`, {
        description: 'Transcript download is in progress...',
        duration: 5000,
      });

      // Simulate progress for user feedback
      const progressInterval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      // Download transcription
      const fileName = `${resource.name}_transcript.docx`;
      downloadTranscription(resource.transcription, fileName);

      // Complete progress
      clearInterval(progressInterval);
      setProgress(100);

      if (isMountedRef.current) {
        setState('success');

        createTimeout(() => {
          // Try to close the window (works if opened via window.open)
          if (window.opener || window.history.length <= 1) {
            window.close();
          } else {
            // If window.close() doesn't work, show success message and let user close manually
            toast.success('Download completed! You can close this tab now.', {
              duration: 5000,
              description: 'The transcript file has been saved to your downloads folder.',
            });
          }
        }, 200);
      }
    } catch (err) {
      console.error('Download failed:', err);
      if (isMountedRef.current) {
        setState('error');
        setError(err instanceof Error ? err.message : 'Download failed');
      }
    }
  }, [resource, trackEvent, downloadTranscription, createTimeout]);

  // Cancel download
  const cancelDownload = useCallback(() => {
    cleanup();
    setState('cancelled');
    setProgress(0);
  }, [cleanup]);

  // Reset and retry
  const retryDownload = useCallback(() => {
    cleanup();
    setState('idle');
    setProgress(0);
    setError('');

    // Start download after a brief delay
    createTimeout(startDownload, 100);
  }, [cleanup, startDownload, createTimeout]);

  // Cleanup on unmount
  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
      cleanup();
    };
  }, [cleanup]);

  return {
    state,
    progress,
    error,
    startDownload,
    cancelDownload,
    retryDownload,
  };
};

const DownloadIcon = ({ state }: { state: DownloadState }) => {
  const getIcon = () => {
    switch (state) {
      case 'checking':
        return <CircularProgress size={48} sx={{ color: 'primary.main' }} />;
      case 'downloading':
        return 'material-symbols:download';
      case 'success':
        return 'material-symbols:check-circle';
      case 'error':
        return 'material-symbols:error';
      case 'cancelled':
        return 'material-symbols:cancel';
      default:
        return 'material-symbols:download';
    }
  };

  const icon = getIcon();

  if (state === 'checking') return icon;

  return (
    <Iconify
      icon={icon as string}
      sx={{
        fontSize: 64,
        color:
          state === 'success'
            ? 'success.main'
            : state === 'error'
              ? 'error.main'
              : state === 'cancelled'
                ? 'warning.main'
                : 'primary.main',
        mb: 2,
      }}
    />
  );
};

const ProgressDisplay = ({ progress, state }: { progress: number; state: DownloadState }) => {
  if (state !== 'downloading') return null;

  return (
    <Box sx={{ width: '100%', mb: 3 }}>
      <LinearProgress
        variant="determinate"
        value={progress}
        sx={{
          height: 10,
          borderRadius: 5,
          mb: 2,
          '& .MuiLinearProgress-bar': {
            borderRadius: 5,
          },
        }}
      />
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
          {Math.round(progress)}% downloaded
        </Typography>
        <Typography variant="caption" color="text.disabled">
          {progress < 100 ? 'Downloading...' : 'Finalizing...'}
        </Typography>
      </Box>
    </Box>
  );
};

const ActionButtons = ({
  state,
  onCancel,
  onRetry,
  onClose,
  onStartDownload,
  resource,
}: {
  state: DownloadState;
  onCancel: () => void;
  onRetry: () => void;
  onClose: () => void;
  onStartDownload: () => void;
  resource: Resource | undefined;
}) => {
  const isDownloadDisabled =
    !resource ||
    (resource.duration ?? -1) < 0 ||
    !resource.transcription ||
    !Array.isArray(resource.transcription) ||
    resource.transcription.length === 0;

  switch (state) {
    case 'idle':
      return (
        <Button
          variant="contained"
          color="primary"
          onClick={onStartDownload}
          startIcon={<Iconify icon="material-symbols:download" />}
          sx={{ borderRadius: 2 }}
          fullWidth
          size="large"
          disabled={isDownloadDisabled}
        >
          Download Transcript
        </Button>
      );

    case 'downloading':
      return (
        <Button
          variant="outlined"
          color="secondary"
          onClick={onCancel}
          startIcon={<Iconify icon="material-symbols:cancel" />}
          sx={{ borderRadius: 2 }}
        >
          Cancel Download
        </Button>
      );

    case 'success':
      return (
        <Button
          variant="contained"
          color="primary"
          onClick={onClose}
          startIcon={<Iconify icon="material-symbols:close" />}
          sx={{ borderRadius: 2 }}
          fullWidth
        >
          Close Window
        </Button>
      );

    case 'error':
      return (
        <Stack direction="row" spacing={2} sx={{ width: '100%' }}>
          <Button
            variant="contained"
            color="primary"
            onClick={onRetry}
            startIcon={<Iconify icon="material-symbols:refresh" />}
            sx={{ borderRadius: 2 }}
            fullWidth
          >
            Try Again
          </Button>
          <Button
            variant="outlined"
            color="secondary"
            onClick={onClose}
            sx={{ borderRadius: 2 }}
            fullWidth
          >
            Close
          </Button>
        </Stack>
      );

    case 'cancelled':
      return (
        <Stack direction="row" spacing={2} sx={{ width: '100%' }}>
          <Button
            variant="contained"
            color="primary"
            onClick={onRetry}
            startIcon={<Iconify icon="material-symbols:download" />}
            sx={{ borderRadius: 2 }}
            fullWidth
          >
            Download Again
          </Button>
          <Button
            variant="outlined"
            color="secondary"
            onClick={onClose}
            sx={{ borderRadius: 2 }}
            fullWidth
          >
            Close
          </Button>
        </Stack>
      );

    default:
      return null;
  }
};

// Main content component
function DownloadPageContent({ resourceId }: DownloadPageContentProps) {
  const { authenticated } = useAuthContext();

  const {
    data: resource,
    isLoading,
    isError,
    error: apiError,
  } = useGetResourceQuery({ id: resourceId }, { skip: !resourceId || !authenticated });

  const { state, progress, error, startDownload, cancelDownload, retryDownload } =
    useDownloadManager(resource);

  const handleClose = useCallback(() => {
    // Try to close the window (works if opened via window.open)
    if (window.opener || window.history.length <= 1) {
      window.close();
    } else {
      // If window.close() doesn't work, navigate back in history
      window.history.back();
    }
  }, []);

  const getStateMessage = () => {
    const isDownloadDisabled =
      !resource ||
      (resource.duration ?? -1) < 0 ||
      !resource.transcription ||
      !Array.isArray(resource.transcription) ||
      resource.transcription.length === 0;

    switch (state) {
      case 'checking':
        return 'Preparing download...';
      case 'downloading':
        return 'Please wait until the transcript download is completed.';
      case 'success':
        return `Your transcript for "${resource?.name}" has been downloaded successfully.`;
      case 'error':
        return error || 'An error occurred while downloading the transcript.';
      case 'cancelled':
        return 'Transcript download was cancelled. You can try downloading again.';
      default:
        if (isDownloadDisabled) {
          if (!resource) {
            return 'Resource not available.';
          }
          if ((resource.duration ?? -1) < 0) {
            return 'This resource has invalid duration and cannot be downloaded as transcript.';
          }
          if (
            !resource.transcription ||
            !Array.isArray(resource.transcription) ||
            resource.transcription.length === 0
          ) {
            return 'No transcript available for this resource.';
          }
        }
        return 'Ready to download transcript. Click the button below to start.';
    }
  };

  const getStateTitle = () => {
    switch (state) {
      case 'checking':
        return 'Preparing Download';
      case 'downloading':
        return `Downloading "${resource?.name || 'Unknown File'}" Transcript`;
      case 'success':
        return 'Transcript Download Complete';
      case 'error':
        return 'Transcript Download Failed';
      case 'cancelled':
        return 'Transcript Download Cancelled';
      default:
        return 'Download Transcript';
    }
  };

  if (isLoading || !authenticated) {
    return <LoadingScreen />;
  }

  if (isError) {
    return (
      <Container maxWidth="sm">
        <Box
          sx={{
            minHeight: '100vh',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            p: 3,
          }}
        >
          <Card
            sx={{
              width: '100%',
              maxWidth: 480,
              boxShadow: (theme) => theme.customShadows?.z24,
            }}
          >
            <CardContent sx={{ p: 4, textAlign: 'center' }}>
              <DownloadIcon state="error" />

              <Typography variant="h4" sx={{ fontWeight: 600, color: 'error.main', mb: 1 }}>
                Resource Not Found
              </Typography>

              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                {apiError
                  ? 'error' in apiError
                    ? apiError.error
                    : 'message' in apiError
                      ? apiError.message
                      : 'The requested resource could not be found.'
                  : 'The requested resource could not be found.'}
              </Typography>

              <Button variant="contained" onClick={handleClose} sx={{ borderRadius: 2 }} fullWidth>
                Close Window
              </Button>
            </CardContent>
          </Card>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="md">
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          p: 3,
        }}
      >
        <Fade in timeout={500}>
          <Card
            sx={{
              width: '100%',
              maxWidth: 800,
              boxShadow: (theme) => theme.customShadows?.z24,
              overflow: 'hidden',
            }}
          >
            <CardContent sx={{ p: 5 }}>
              <Box sx={{ textAlign: 'center', mb: 4 }}>
                <DownloadIcon state={state} />

                <Typography
                  variant="h4"
                  sx={{
                    fontWeight: 600,
                    mb: 1,
                    color:
                      state === 'success'
                        ? 'success.main'
                        : state === 'error'
                          ? 'error.main'
                          : state === 'cancelled'
                            ? 'warning.main'
                            : 'text.primary',
                  }}
                >
                  {getStateTitle()}
                </Typography>

                <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                  {getStateMessage()}
                </Typography>
              </Box>

              <ProgressDisplay progress={progress} state={state} />

              <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                <ActionButtons
                  state={state}
                  onCancel={cancelDownload}
                  onRetry={retryDownload}
                  onClose={handleClose}
                  onStartDownload={startDownload}
                  resource={resource}
                />
              </Box>
            </CardContent>
          </Card>
        </Fade>
      </Box>
    </Container>
  );
}

// ----------------------------------------------------------------------

export default function DownloadPage() {
  const { resourceId = '' } = useParams();

  return (
    <>
      <Helmet>
        <title>{metadata.title}</title>
      </Helmet>

      <SimpleLayout>
        <DownloadPageContent resourceId={resourceId} />
      </SimpleLayout>
    </>
  );
}
