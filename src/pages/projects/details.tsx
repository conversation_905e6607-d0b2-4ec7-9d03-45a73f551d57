import { useEffect } from 'react';
import { useParams } from 'react-router';
import { useDispatch } from 'react-redux';
import { Helmet } from 'react-helmet-async';

import { paths } from 'src/routes/paths';
import { useRouter } from 'src/routes/hooks';

import { CONFIG } from 'src/global-config';
import { useUploadWarning } from 'src/hooks';
import { useGetProjectMembershipQuery } from 'src/store/api/projects';
import { setLastViewedProjectId } from 'src/store/slices/settings/slice';

import ProjectDetailsView from 'src/sections/projects/view/details';

// ----------------------------------------------------------------------

const metadata = { title: `${CONFIG.appName} - Project Details` };

export default function ProjectDetailsPage() {
  const router = useRouter();
  const dispatch = useDispatch();
  const { id: projectId = '' } = useParams();

  // Add upload warning functionality
  useUploadWarning();

  const { refetch, error: cannotGetProjectMembershipError } = useGetProjectMembershipQuery(
    { id: projectId },
    {
      skip: !projectId,
    }
  );

  useEffect(() => {
    if (!projectId) return undefined;
    
    const timer = setTimeout(() => {
      // Only refetch if the query was actually initiated (not skipped)
      if (refetch) {
        refetch();
      }
    }, 3_000);

    return () => clearTimeout(timer);
  }, [refetch, projectId]);

  useEffect(() => {
    if (cannotGetProjectMembershipError) {
      router.push(paths.project.root + '/403');
    }
  }, [cannotGetProjectMembershipError]);

  useEffect(() => {
    dispatch(setLastViewedProjectId(projectId));
  }, [projectId]);

  return (
    <>
      <Helmet>
        <title> {metadata.title}</title>
      </Helmet>

      <ProjectDetailsView projectId={projectId} />
    </>
  );
}
