import type { FC } from 'react';

import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router';

import { Box, Alert, Typography, CircularProgress } from '@mui/material';

import { useLazyResolveUrlQuery } from 'src/store/api/url-resolver';

// ----------------------------------------------------------------------

const RedirectHandler: FC = () => {
  const { shortCode } = useParams<{ shortCode: string }>();
  const navigate = useNavigate();
  const [error, setError] = useState<string | null>(null);
  
  const [resolveUrl, { isLoading }] = useLazyResolveUrlQuery();

  useEffect(() => {
    const resolveAndRedirect = async () => {
      if (!shortCode) {
        setError('Invalid short code');
        return;
      }

      try {
        const response = await resolveUrl({ shortCode });
        
        if ('data' in response && response.data?.success && response.data?.originalUrl) {
          const originalUrl = response.data.originalUrl;
          
          // Check if it's an internal URL that should be handled by the frontend router
          if (originalUrl.includes(window.location.origin)) {
            // Extract the path from the full URL and navigate internally
            const url = new URL(originalUrl);
            const internalPath = url.pathname + url.search + url.hash;
            navigate(internalPath, { replace: true });
          } else {
            // External URL - redirect to it
            window.location.replace(originalUrl);
          }
        } else if ('data' in response && response.data) {
          setError(response.data.error || 'Failed to resolve URL');
        } else if ('error' in response) {
          console.error('Error resolving short URL:', response.error);
          setError((response.error as any)?.data?.message || 'Failed to resolve URL');
        }
      } catch (err: any) {
        console.error('Error resolving short URL:', err);
        setError(err.response?.data?.message || 'Failed to resolve URL');
      }
    };

    resolveAndRedirect();
  }, [shortCode, navigate, resolveUrl]);

  if (isLoading) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          gap: 2,
        }}
      >
        <CircularProgress size={48} />
        <Typography variant="h6" color="text.secondary">
          Redirecting...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          gap: 2,
          px: 2,
        }}
      >
        <Alert severity="error" sx={{ maxWidth: 500 }}>
          <Typography variant="h6" gutterBottom>
            Link Error
          </Typography>
          <Typography variant="body2">
            {error}
          </Typography>
        </Alert>
        <Typography 
          variant="body2" 
          color="primary" 
          sx={{ cursor: 'pointer', textDecoration: 'underline' }}
          onClick={() => navigate('/', { replace: true })}
        >
          Go to Home
        </Typography>
      </Box>
    );
  }

  return null;
};

export default RedirectHandler;
