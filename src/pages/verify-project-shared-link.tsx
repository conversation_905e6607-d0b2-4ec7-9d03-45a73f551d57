
import { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import { Button } from '@mui/material';
import Stack from '@mui/material/Stack';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';

import { paths } from 'src/routes/paths';
import { useRouter, useParams, useSearchParams } from 'src/routes/hooks';

import useUserInitialContext from 'src/hooks/user-initial-context';

import { CONFIG } from 'src/global-config';
import {
  useGetProjectMembershipQuery,
  useSharedLinkVerificationMutation,
} from 'src/store/api/projects';

import { LoadingScreen } from 'src/components/loading-screen';

// ----------------------------------------------------------------------

const metadata = { title: `${CONFIG.appName} - Join Project` };

export default function VerifyProjectSharedLinkPage() {
  useUserInitialContext();

  const router = useRouter();
  const { projectId } = useParams();
  const searchParams = useSearchParams();

  // Get token from URL parameters
  const token = searchParams.get('token');

  const [status, setStatus] = useState<'loading' | 'error' | 'success'>('loading');
  const [isMember, setIsMember] = useState(false);

  // Check if user is already a member of the project
  const {
    data: projectMembership,
    isLoading: isMembershipLoading,
    error: membershipError,
    refetch: refetchProjectMembership,
  } = useGetProjectMembershipQuery(
    { id: projectId || '' },
    {
      skip: !projectId,
    }
  );

  const [sharedLinkVerification] =
    useSharedLinkVerificationMutation();

  useEffect(() => {
    const handleVerification = async () => {
      // Check if required parameters are present
      if (!token || !projectId) {
        setStatus('error');
        return;
      }

      // Wait for membership check to complete
      if (isMembershipLoading) {
        return;
      }

      // If user is already a member, redirect to project
      if (projectMembership && !membershipError) {
        router.push(paths.project.details(projectId));
        return;
      }

      // If user is not a member (membership error indicates they don't have access), proceed with verification
      if (membershipError) {
        try {
          // Call the API to verify the shared link
          const response = await sharedLinkVerification({
            id: projectId,
            payload: {
              token,
            },
          }).unwrap();

          if (response.isMember) {

            setIsMember(true);
            await refetchProjectMembership();

            setTimeout(() => {
              console.log("redirecting to project details");
              router.replace(paths.project.details(projectId));
            }, 1000);
          }

          setStatus('success');
        } catch (error) {
          setStatus('error');
          console.error('Error verifying shared link:', error);
        }
      }
    };

    handleVerification();
  }, [
    token,
    projectId,
    projectMembership,
    membershipError,
    isMembershipLoading,
    sharedLinkVerification,
    router,
    refetchProjectMembership,
  ]);

  // Show loading screen while checking membership or verifying
  if (status === 'loading' || isMembershipLoading) {
    return <LoadingScreen />;
  }

  const renderCardRequestAccess = () => <Card sx={{ p: 5 }}>
    {status === 'success' && (
      <Box>
        <Stack spacing={1} alignItems="center">
          <Typography variant="h4">You need access</Typography>

          <Typography variant="body1" sx={{ textAlign: 'center', mb: 1 }}>
            Ask the owner to give you access.
          </Typography>

          <Button
            variant="outlined"
            color="inherit"
            onClick={() => router.push(paths.project.root)}
            sx={(theme) => ({
              backgroundColor: 'transparent',
              color: theme.vars.palette.text.primary,
              borderColor: theme.vars.palette.divider,
              ...theme.applyStyles('dark', {
                backgroundColor: 'transparent',
                color: theme.vars.palette.text.primary,
                borderColor: theme.vars.palette.divider,
              }),
            })}
          >
            Go to projects
          </Button>
        </Stack>
      </Box>
    )}

    {status === 'error' && (
      <Box>
        <Stack spacing={1} alignItems="center">
          <Typography variant="h4">Can&apos;t open this link</Typography>

          <Typography variant="body1" sx={{ textAlign: 'center', mb: 3 }}>
            It may be expired or no longer active.
            <br />
            Try asking the owner to resend it.
          </Typography>

          <Button
            variant="outlined"
            color="secondary"
            onClick={() => router.push(paths.project.root)}
            sx={{
              minWidth: 120,
            }}
          >
            Back to latest project
          </Button>
        </Stack>
      </Box>
    )}
  </Card>


  const renderCardAlreadyMember = () => <Card sx={{ p: 5 }}>
    {status === 'success' && (
      <Box>
        <Stack spacing={1} alignItems="center">
          <Typography variant="h4">
            Success - Invite accepted
          </Typography>
          <Card
            sx={(theme) => ({
              width: '100%',
              p: 3,
              backgroundColor: theme.vars.palette.background.neutral,
              border: `1px solid ${theme.vars.palette.divider}`,
            })}
          >
            <Typography variant="body2">You&apos;re in! Redirecting to the project.</Typography>
          </Card>
        </Stack>
      </Box>
    )}

    {status === 'error' && (
      <Box>
        <Stack spacing={1} alignItems="center">
          <Typography variant="h4" sx={{ textAlign: 'center' }}>
            Can&apos;t open this link
          </Typography>
          <Typography variant="body1" sx={{ textAlign: 'center', mb: 3 }}>
            It may be expired or no longer active.
            Try asking the owner to resend it.
          </Typography>
          <Button
            variant="outlined"
            color="secondary"
            onClick={() => router.push(paths.project.root)}
            sx={{
              minWidth: 120,
            }}
          >
            Back to latest project
          </Button>
        </Stack>

      </Box>
    )}
  </Card>

  return (
    <>
      <Helmet>
        <title>{metadata.title}</title>
      </Helmet>

      <Container maxWidth="sm" sx={{ py: 10 }}>
        {
          isMember ? renderCardAlreadyMember() : renderCardRequestAccess()
        }
      </Container>
    </>
  );
}
