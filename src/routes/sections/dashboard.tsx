import type { RouteObject } from 'react-router';

import { useDispatch } from 'react-redux';
import { lazy, Suspense, useEffect } from 'react';
import { Outlet, useLocation } from 'react-router';

import { paths } from 'src/routes/paths';

import { DashboardLayout } from 'src/layouts/dashboard';
import { viewResource } from 'src/store/slices/resources/slice';
import { CitationProvider } from 'src/contexts/citation-context';

import { LoadingScreen } from 'src/components/loading-screen';

import { AuthGuard } from 'src/auth/guard';

// ----------------------------------------------------------------------

const FilesPage = lazy(() => import('src/pages/files'));
const ProjectsPage = lazy(() => import('src/pages/projects'));
const ProjectDetailsPage = lazy(() => import('src/pages/projects/details'));
const ProjectFolderDetailsPage = lazy(() => import('src/pages/projects/folder-details'));
// ----------------------------------------------------------------------

const DashboardLayoutWrapper: React.FC = () => {
  const dispatch = useDispatch();
  const location = useLocation();

  // Clear focused resource when location changes
  useEffect(() => {
    dispatch(viewResource(null));
  }, [location]);

  return (
    <CitationProvider>
      <DashboardLayout>
        <Suspense fallback={<LoadingScreen />}>
          <Outlet />
        </Suspense>
      </DashboardLayout>
    </CitationProvider>
  );
};

export const dashboardRoutes: RouteObject[] = [
  {
    path: paths.files.root,
    element: (
      <AuthGuard>
        <DashboardLayoutWrapper />
      </AuthGuard>
    ),
    children: [{ element: <FilesPage />, index: true }],
  },
  {
    path: paths.project.root,
    element: (
      <AuthGuard>
        <DashboardLayoutWrapper />
      </AuthGuard>
    ),
    children: [
      { index: true, element: <ProjectsPage /> },
      {
        path: ':id',
        element: <ProjectDetailsPage />,
      },
      {
        path: ':id/folder/:folderId',
        element: <ProjectFolderDetailsPage />,
      },
    ],
  },
];
