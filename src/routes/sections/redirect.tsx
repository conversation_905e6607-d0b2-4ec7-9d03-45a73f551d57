import type { RouteObject } from 'react-router';

import { lazy, Suspense } from 'react';

import { LoadingScreen } from 'src/components/loading-screen';

// ----------------------------------------------------------------------

const RedirectHandler = lazy(() => import('src/pages/redirect-handler'));

export const redirectRoutes: RouteObject[] = [
  {
    path: '/s/:shortCode',
    element: (
            <Suspense fallback={<LoadingScreen />}>
              <RedirectHandler />
            </Suspense>
        ),
  },
];
