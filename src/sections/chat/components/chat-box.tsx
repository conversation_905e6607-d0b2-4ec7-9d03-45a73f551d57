import type { SxProps } from '@mui/material';

import { toast } from 'sonner';
import { useSelector } from 'react-redux';
import { memo, useMemo, useState, forwardRef, useCallback, useImperativeHandle } from 'react';

import { Box, Stack, Button, Tooltip, IconButton } from '@mui/material';

import useFeatureFlags from 'src/hooks/feature-flags';

import { AppFeatures } from 'src/types';
import { useConvertToResourceMutation } from 'src/store/api/chat/hooks';
import { selectLastViewedProjectId } from 'src/store/slices/settings/selectors';

import { Iconify } from 'src/components/iconify';

import { NewChatDialog } from './new-chat-dialog';
import { ChatMessageList } from './chat-message-list';
import { ChatMessageInput } from './chat-message-input';
import { useImageUpload } from '../hooks/use-image-upload';

import type { ChatMessage, ImageUpload, ChatProgressEvent } from '../types';

export interface ChatBoxRef {
  openNewChatDialog: () => void;
}

interface ChatBoxProps {
  messages: ChatMessage[];
  containerSx?: SxProps;
  messageListSx?: SxProps;
  inputSx?: SxProps;
  loading?: boolean;
  isReplying?: boolean;
  disabled?: boolean;
  sendMessage: (message: string, images?: ImageUpload[]) => void;
  stopConversation: () => void;
  quickReplyOptions?: string[];
  actions?: React.ReactNode;
  placeholder?: string;
  conversationId?: string | null;
  onConversionSuccess?: (resourceTitle: string) => void;
  onResetChat?: () => void;
  currentProgress?: ChatProgressEvent['data'] | null;
}

const QuickReplyOptions = memo<{
  options: string[];
  onSelect: (option: string) => void;
  disabled?: boolean;
}>(({ options, onSelect, disabled }) => (
  <Stack direction="row" alignItems="center" gap={1}>
    {options.map((option) => (
      <Button
        variant="outlined"
        size="small"
        key={option}
        onClick={() => onSelect(option)}
        disabled={disabled}
      >
        {option}
      </Button>
    ))}
  </Stack>
));

QuickReplyOptions.displayName = 'QuickReplyOptions';

const ChatBox = forwardRef<ChatBoxRef, ChatBoxProps>(
  (
    {
      messages,
      containerSx,
      messageListSx,
      inputSx,
      loading = false,
      isReplying = false,
      disabled = false,
      sendMessage,
      stopConversation,
      quickReplyOptions = [],
      actions,
      placeholder = 'Type your message...',
      conversationId,
      onConversionSuccess,
      onResetChat,
      currentProgress,
    },
    ref
  ) => {
    const [message, setMessage] = useState('');
    const [newChatDialogOpen, setNewChatDialogOpen] = useState(false);
    const currentProjectId = useSelector(selectLastViewedProjectId);
    const [convertToResource, { isLoading: isConverting }] = useConvertToResourceMutation();
    const { isFlagEnabled } = useFeatureFlags();

    const allowImageUpload = isFlagEnabled(AppFeatures.SUPPORT_CHAT_IMAGES);

    // Use the centralized image upload hook
    const {
      attachedImages,
      setAttachedImages,
      handleImagesAdd,
      handleImageRemove,
      handlePaste,
      getRootProps,
      getInputProps,
      isDragActive,
      isMaxReached,
      openFileDialog,
    } = useImageUpload({
      allowImageUpload,
      disabled,
    });

    const handleSendMessage = async (msg: string, images: ImageUpload[] = []) => {
      if ((!msg.trim() && images.length === 0) || disabled) return;

      try {
        // Send the message with images
        sendMessage(msg, images);
        // Clear the message text and images after successful send
        setMessage('');
        setAttachedImages([]);
      } catch (sendError) {
        console.error('Failed to send message:', sendError);
      }
    };

    const handleQuickReply = useCallback(
      async (option: string) => {
        if (disabled) return;

        try {
          // Send the quick reply with any attached images
          sendMessage(option, attachedImages);
          // Clear the message text and images after successful send
          setMessage('');
          setAttachedImages([]);
        } catch (sendError) {
          console.error('Failed to send quick reply:', sendError);
        }
      },
      [disabled, sendMessage, attachedImages, setAttachedImages]
    );

    const handleNewChat = useCallback(() => {
      // Always show the dialog - let the user choose what to do
      setNewChatDialogOpen(true);
    }, []);

    // Expose handleNewChat to parent components
    useImperativeHandle(
      ref,
      () => ({
        openNewChatDialog: handleNewChat,
      }),
      [handleNewChat]
    );

    const handleSaveAndNew = useCallback(async () => {
      if (!conversationId) {
        toast.error('No conversation to save');
        setNewChatDialogOpen(false);
        return;
      }

      if (!currentProjectId) {
        toast.error('No project selected. Please select a project first');
        setNewChatDialogOpen(false);
        return;
      }

      try {
        const response = await convertToResource({
          id: conversationId,
          payload: {
            format: 'markdown', // Default format that includes embedded images
          },
        }).unwrap();

        toast.success('Conversation saved as resource!', {
          description: `Created: ${response.data.title}`,
        });

        onConversionSuccess?.(response.data.title);

        // Reset chat after successful conversion
        onResetChat?.();
        setNewChatDialogOpen(false);
      } catch {
        const errorMessage = 'Failed to save conversation';
        const errorDescription = 'Please try again later';

        toast.error(errorMessage, {
          description: errorDescription,
        });
      }
    }, [conversationId, currentProjectId, convertToResource, onConversionSuccess, onResetChat]);

    const handleStartFresh = useCallback(() => {
      onResetChat?.();
      setNewChatDialogOpen(false);
      toast.success('Started new conversation');
    }, [onResetChat]);

    const handleCloseDialog = useCallback(() => {
      setNewChatDialogOpen(false);
    }, []);

    // Memoize send button disabled state
    const sendButtonDisabled = useMemo(
      () => disabled || (!message.trim() && attachedImages.length === 0),
      [disabled, message, attachedImages]
    );

    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
          minHeight: 0,
          ...containerSx,
        }}
      >
        <ChatMessageList
          messages={messages}
          loading={loading}
          isReplying={isReplying}
          currentProgress={currentProgress}
          sx={{
            flex: 1,
            minHeight: 0,
            ...messageListSx,
          }}
          data-testid="chat-box-message-list"
        />

        {!loading && (
          <Box
            {...(allowImageUpload ? getRootProps() : {})}
            data-testid="chat-box-input-container"
            sx={{
              flexShrink: 0,
              display: 'flex',
              flexDirection: 'column',
              gap: 1,
              position: 'relative',
              ...(allowImageUpload &&
                isDragActive && {
                  backgroundColor: (theme) => theme.palette.action.hover,
                  borderRadius: 1,
                  '&::after': {
                    content: '"Drop images here"',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: 'rgba(0, 0, 0, 0.05)',
                    backdropFilter: 'blur(2px)',
                    color: 'primary.main',
                    fontSize: '1rem',
                    fontWeight: 500,
                    zIndex: 10,
                    borderRadius: 1,
                    border: (theme) => `2px dashed ${theme.palette.primary.main}`,
                  },
                }),
            }}
          >
            {allowImageUpload && <input {...getInputProps()} />}

            <ChatMessageInput
              placeholder={placeholder}
              onEnter={handleSendMessage}
              message={message}
              setMessage={setMessage}
              attachedImages={attachedImages}
              onImagesAdd={handleImagesAdd}
              onImageRemove={handleImageRemove}
              onPaste={handlePaste}
              projectId={currentProjectId ?? ''}
              disabled={disabled}
              disableDrop // Disable drop on input since we handle it at container level
              isMaxImagesReached={isMaxReached}
              onOpenFileDialog={openFileDialog}
              sx={{
                flexShrink: 0,
                ...inputSx,
              }}
            />

            <Stack
              direction="row"
              justifyContent="space-between"
              alignItems="center"
              sx={{
                flexShrink: 0,
                minHeight: 40,
                py: 0.5,
              }}
            >
              {actions}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {quickReplyOptions.length > 0 && (
                  <QuickReplyOptions
                    disabled={disabled}
                    options={quickReplyOptions}
                    onSelect={handleQuickReply}
                  />
                )}
                {isReplying ? (
                  <Tooltip title="Stop" placement="top" arrow>
                    <IconButton onClick={stopConversation}>
                      <Iconify icon="material-symbols:stop-rounded" />
                    </IconButton>
                  </Tooltip>
                ) : (
                  <Tooltip title="Send" placement="top" arrow>
                    <IconButton
                      disabled={sendButtonDisabled}
                      onClick={() => handleSendMessage(message, attachedImages)}
                    >
                      <Iconify
                        sx={{ color: sendButtonDisabled ? 'text.disabled' : 'text.primary' }}
                        icon="mynaui:send-solid"
                      />
                    </IconButton>
                  </Tooltip>
                )}
              </Box>
            </Stack>
          </Box>
        )}

        {/* New Chat Dialog */}
        <NewChatDialog
          open={newChatDialogOpen}
          onClose={handleCloseDialog}
          onSaveAndNew={handleSaveAndNew}
          onStartFresh={handleStartFresh}
          isConverting={isConverting}
        />
      </Box>
    );
  }
);

ChatBox.displayName = 'ChatBox';

export { ChatBox };
