import type { Resource } from 'src/types';
import type { TransitionProps } from '@mui/material/transitions';

import { useMemo, useEffect, forwardRef } from 'react';

import Slide from '@mui/material/Slide';
import AppBar from '@mui/material/AppBar';
import Dialog from '@mui/material/Dialog';
import Toolbar from '@mui/material/Toolbar';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import { Stack, Divider, useTheme, useMediaQuery } from '@mui/material';

import { DIALOG_APP_BAR_HEIGHT } from 'src/theme/constants';
import { useGetProjectDetailsQuery } from 'src/store/api/projects';

import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';

import { ChatBox } from './chat-box';
import useChat from '../hooks/use-chat';
import ProjectCard from '../../projects/components/project-card';
import ResourceCard from '../../resources/components/resource-card';

// ----------------------------------------------------------------------

const Transition = forwardRef(
  (props: TransitionProps & { children: React.ReactElement }, ref: React.Ref<unknown>) => (
    <Slide direction="up" ref={ref} {...props} />
  )
);

interface Props {
  open: boolean;
  onClose: () => void;
  resources?: Resource[];
  initialMessages?: string[];
  projectId?: string;
}

const ChatDialog: React.FC<Props> = ({
  open,
  onClose,
  resources,
  projectId,
  initialMessages = [],
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const { data: project, isLoading: isLoadingProjectDetails } = useGetProjectDetailsQuery(
    { id: projectId as string, filter: { includeAllResources: true } },
    {
      skip: !projectId,
      selectFromResult(state) {
        return {
          data: state.data,
          isLoading: state.isLoading,
        };
      },
      refetchOnFocus: true,
      refetchOnMountOrArgChange: true,
    }
  );

  // Get resource IDs for the new backend API
  const resourceIds = useMemo(() => {
    if (project?.resources) {
      return project.resources.map((resource) => resource.id);
    }
    if (resources) {
      return resources.map((resource) => resource.id);
    }
    return [];
  }, [project, resources]);

  // Use the new chat hook
  const {
    messages,
    isLoading: isCreatingConversation,
    isStreaming,
    sendMessage,
    stopStreaming,
    conversationId,
    resetChat,
    allowSendMessage,
  } = useChat({
    resources: resourceIds,
    projectId,
  });

  // Reset chat when dialog opens
  useEffect(() => {
    if (open) {
      resetChat();
    }
  }, [open, resetChat]);

  const loading = isLoadingProjectDetails || isCreatingConversation;
  const hasActiveSession = !!conversationId;

  const title = useMemo(() => {
    if (project) {
      return `Aida Chat for project: ${project.name}`;
    } else if (resources) {
      return `Aida Chat for ${resources.length} selected resources`;
    }

    return 'Aida Chat';
  }, [project, resources]);

  const inputPreviews = useMemo(() => {
    if (project) {
      return <ProjectCard data={project} hideActions />;
    } else if (resources) {
      return resources.map((resource) => <ResourceCard data={resource} hideActions hidePreview />);
    }

    return null;
  }, [project, resources]);

  // Send initial messages when conversation is ready
  useEffect(() => {
    if (hasActiveSession && initialMessages.length && !isStreaming) {
      initialMessages.forEach(async (message) => {
        await sendMessage(message);
      });
    }
  }, [hasActiveSession, initialMessages, isStreaming, sendMessage]);

  return (
    <Dialog fullScreen open={open} onClose={onClose} TransitionComponent={Transition}>
      <AppBar position="relative" color="default">
        <Toolbar>
          <Typography variant="h6" sx={{ flex: 1 }}>
            {title}
          </Typography>

          <IconButton color="inherit" edge="start" onClick={onClose}>
            <Iconify icon="mingcute:close-line" />
          </IconButton>
        </Toolbar>
      </AppBar>
      <Stack direction="row" height={`calc(100vh - ${DIALOG_APP_BAR_HEIGHT}px)`} gap={1}>
        {!isMobile && inputPreviews && (
          <>
            <Stack direction="column" width="25%" sx={{ p: 2 }}>
              <Scrollbar
                sx={[{ height: '100%' }]}
                slotProps={{ contentSx: { display: 'flex', flexDirection: 'column', gap: 2 } }}
              >
                {inputPreviews}
              </Scrollbar>
            </Stack>
            <Divider orientation="vertical" flexItem />
          </>
        )}

        <Stack direction="column" width={isMobile ? '100%' : '75%'}>
          <ChatBox
            containerSx={{
              px: isMobile ? 1 : 3,
              py: 1,
              height: '100%',
              minHeight: 0,
            }}
            messageListSx={{
              px: 0,
              [theme.breakpoints.down('sm')]: {
                px: 0,
              },
            }}
            inputSx={{
              width: '100%',
              [theme.breakpoints.down('sm')]: {
                mx: -1,
              },
            }}
            messages={messages}
            loading={loading}
            isReplying={isStreaming}
            disabled={!allowSendMessage}
            sendMessage={sendMessage}
            stopConversation={stopStreaming}
            conversationId={conversationId}
            onResetChat={resetChat}
          />
        </Stack>
      </Stack>
    </Dialog>
  );
};

export default ChatDialog;
