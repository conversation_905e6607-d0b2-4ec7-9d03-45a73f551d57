import type { Theme, SxProps } from '@mui/material';

import { useState, useCallback } from 'react';

import InputBase from '@mui/material/InputBase';
import { Box, Tooltip, useTheme, IconButton } from '@mui/material';

import useFeatureFlags from 'src/hooks/feature-flags';

import { AppFeatures } from 'src/types';

import { Iconify } from 'src/components/iconify';

import { ImagePreviewGrid } from './image-preview-grid';
import { IMAGE_CONSTRAINTS } from '../utils/image-validation';
import { ChatPreferencesDialog } from './chat-preferences-dialog';

import type { ImageUpload } from '../types';

// ----------------------------------------------------------------------

type Props = {
  onEnter: (message: string, images?: ImageUpload[]) => Promise<void>;
  sx?: SxProps<Theme>;
  message: string;
  setMessage: (message: string) => void;
  attachedImages: ImageUpload[];
  onImagesAdd: (files: File[]) => Promise<void>;
  onImageRemove: (imageId: string) => void;
  onPaste: (event: React.ClipboardEvent) => Promise<void>;
  placeholder?: string;
  projectId?: string;
  disabled?: boolean;
  disableDrop?: boolean;
  isMaxImagesReached?: boolean;
  onOpenFileDialog?: () => void;
};

export function ChatMessageInput({
  onEnter,
  message,
  setMessage,
  attachedImages,
  onImagesAdd,
  onImageRemove,
  onPaste,
  placeholder,
  projectId,
  disabled = false,
  disableDrop = false,
  isMaxImagesReached = false,
  onOpenFileDialog,
}: Props) {
  const theme = useTheme();
  const { isFlagEnabled } = useFeatureFlags();
  const [preferencesDialogOpen, setPreferencesDialogOpen] = useState(false);

  const allowPreferencesConfiguration = isFlagEnabled(AppFeatures.CHAT_PREFERENCES);
  const allowImageUpload = isFlagEnabled(AppFeatures.SUPPORT_CHAT_IMAGES);

  const handleChangeMessage = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setMessage(event.target.value);
    },
    [setMessage]
  );

  const handleSendMessage = useCallback(async () => {
    if (disabled) return;
    if (!message.trim() && attachedImages.length === 0) return;

    try {
      await onEnter(message, attachedImages);
      // Note: Parent component handles clearing attachedImages
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  }, [message, attachedImages, onEnter, disabled]);

  const handleKeyDown = useCallback(
    async (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (event.key === 'Enter') {
        // Allow new line with Shift+Enter
        if (event.shiftKey) {
          return; // Let the default behavior handle new line
        }

        // Send message with Enter (without Shift)
        event.preventDefault();
        await handleSendMessage();
      }
    },
    [handleSendMessage]
  );

  const handlePaste = useCallback(
    async (event: React.ClipboardEvent<HTMLInputElement>) => {
      if (!allowImageUpload) return;
      // Delegate to parent's paste handler
      await onPaste(event);
    },
    [allowImageUpload, onPaste]
  );

  const handleOpenPreferences = useCallback(() => {
    setPreferencesDialogOpen(true);
  }, []);

  const handleClosePreferences = useCallback(() => {
    setPreferencesDialogOpen(false);
  }, []);

  const handleImageButtonClick = useCallback(() => {
    // Use parent's file dialog handler if provided, otherwise create our own
    if (onOpenFileDialog) {
      onOpenFileDialog();
    } else {
      const input = document.createElement('input');
      input.type = 'file';
      input.multiple = true;
      input.accept = IMAGE_CONSTRAINTS.SUPPORTED_FORMATS.join(',');
      input.onchange = (e) => {
        const files = Array.from((e.target as HTMLInputElement).files || []);
        onImagesAdd(files);
      };
      input.click();
    }
  }, [onOpenFileDialog, onImagesAdd]);

  const hasImages = attachedImages.length > 0;

  return (
    <>
      <Box sx={{ position: 'relative', width: '100%' }}>
        {/* Image previews - mini thumbnails on top */}
        {allowImageUpload && hasImages && (
          <Box sx={{ mb: 1, px: 1.5 }}>
            <ImagePreviewGrid
              images={attachedImages}
              onRemove={onImageRemove}
              disabled={false} // Always allow removing images from input
            />
          </Box>
        )}

        {/* Main input area - drag and drop is handled at container level */}
        <Box
          sx={{
            position: 'relative',
            borderTop: (muiTheme) => `solid 1px ${muiTheme.vars.palette.divider}`,
            bgcolor: 'transparent',
          }}
        >
          {/* Text input */}
          <InputBase
            name="chat-message"
            id="chat-message-input"
            value={message}
            onKeyDown={handleKeyDown}
            onChange={handleChangeMessage}
            onPaste={handlePaste}
            placeholder={placeholder || 'Start typing...'}
            multiline
            maxRows={4}
            disabled={disabled}
            sx={{
              px: 1.5,
              py: 1,
              pr: allowImageUpload ? 12 : 6, // More space for image + preferences icons
              minHeight: 48,
              flexShrink: 0,
              backgroundColor: 'transparent',
              width: '100%',
              [theme.breakpoints.down('sm')]: {
                px: 1,
                pr: allowImageUpload ? 10 : 5,
                minHeight: 44,
              },
              '& .MuiInputBase-input': {
                py: 1,
                fontSize: '0.875rem',
                lineHeight: 1.5,
                backgroundColor: 'transparent',
              },
              ...(disabled && {
                opacity: 0.6,
              }),
            }}
          />

          {/* Action buttons container */}
          <Box
            sx={{
              position: 'absolute',
              right: 8,
              top: '50%',
              transform: 'translateY(-50%)',
              display: 'flex',
              alignItems: 'center',
              gap: 0.5,
            }}
          >
            {/* Image upload button */}
            {allowImageUpload && (
              <Tooltip
                title={
                  isMaxImagesReached
                    ? `Maximum ${IMAGE_CONSTRAINTS.MAX_IMAGES_PER_MESSAGE} images`
                    : disabled
                      ? 'Image upload disabled'
                      : 'Attach images'
                }
                placement="top"
                arrow
              >
                <span>
                  <IconButton
                    size="small"
                    disabled={disabled || isMaxImagesReached}
                    onClick={handleImageButtonClick}
                    sx={(muiTheme) => ({
                      color: 'text.secondary',
                      '&:hover': {
                        color: muiTheme.palette.primary.main,
                      },
                      '&.Mui-disabled': {
                        color: 'text.disabled',
                      },
                    })}
                  >
                    <Iconify icon="material-symbols:image-outline" width={20} height={20} />
                  </IconButton>
                </span>
              </Tooltip>
            )}

            {/* Preferences Icon */}
            {allowPreferencesConfiguration && (
              <Tooltip title="Chat Preferences" placement="top" arrow>
                <IconButton
                  onClick={handleOpenPreferences}
                  color="default"
                  size="small"
                  disabled={disabled}
                  sx={{
                    color: 'text.secondary',
                    '&:hover': {
                      color: 'text.primary',
                    },
                  }}
                >
                  <Iconify icon="material-symbols:settings" width={20} height={20} />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        </Box>
      </Box>

      {/* Preferences Dialog */}
      {preferencesDialogOpen && (
        <ChatPreferencesDialog
          open={preferencesDialogOpen}
          onClose={handleClosePreferences}
          projectId={projectId}
        />
      )}
    </>
  );
}