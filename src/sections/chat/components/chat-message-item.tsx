import { toast } from 'sonner';
import { marked } from 'marked';
import { memo, useMemo, useCallback } from 'react';
import { useCopyToClipboard } from 'minimal-shared/hooks';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import { Tooltip, useTheme, CircularProgress } from '@mui/material';

import { AiAgentIcon } from 'src/assets/icons';

import { Iconify } from 'src/components/iconify';
import { MarkdownWithCitations } from 'src/components/markdown';

import { ImagePreviewGrid } from './image-preview-grid';
import useConvertToNote from '../hooks/use-convert-to-note';
import useConvertToResource from '../../resources/hooks/convert-to-resource';

import type { ChatMessage } from '../types';

// ----------------------------------------------------------------------

type Props = {
  message: ChatMessage;
  helperText?: string;
};

export const ChatMessageItem = memo<Props>(({ message, helperText }) => {
  const theme = useTheme();

  const { copy } = useCopyToClipboard();
  const { convertTextToResource, isConverting: isConvertingToResource } = useConvertToResource();
  const { convertTextToNote, isConverting: isConvertingToNote } = useConvertToNote();

  const messageData = useMemo(() => {
    const isUser = message.role === 'user';
    const isAssistant = message.role === 'assistant';
    const content =
      message.content ||
      (typeof (message as any).body === 'string' ? (message as any).body : '') ||
      '';
    const hasStringContent =
      typeof message.content === 'string' && message.content.trim().length > 0;
    const hasBodyComponent = !!(message as any).body && typeof (message as any).body !== 'string';

    return {
      isUser,
      isAssistant,
      content,
      hasContent: hasStringContent || hasBodyComponent,
      hasStringContent,
      hasBodyComponent,
    };
  }, [message]);

  // Memoize assistant info
  const assistantInfo = useMemo(
    () => ({
      name: 'Aida',
      avatar: <AiAgentIcon sx={{ width: 32, height: 32 }} />,
    }),
    []
  );

  const onCopy = useCallback(
    async (text: string) => {
      if (!text) return;

      try {
        // Convert markdown to HTML
        const htmlContent = await marked(text);

        // Try to copy as formatted HTML
        await navigator.clipboard.write([
          new ClipboardItem({
            'text/html': new Blob([htmlContent], { type: 'text/html' }),
            'text/plain': new Blob([text], { type: 'text/plain' }),
          }),
        ]);

        toast.success('Copied!');
      } catch {
        // Fallback to plain text copy
        try {
          await copy(text);
          toast.success('Copied!');
        } catch (fallbackError) {
          console.error('Failed to copy text:', fallbackError);
          toast.error('Failed to copy text');
        }
      }
    },
    [copy]
  );

  const handleConvertToResource = useCallback(() => {
    if (messageData.hasStringContent) {
      convertTextToResource(messageData.content as string);
    }
  }, [convertTextToResource, messageData.content, messageData.hasStringContent]);

  const handleConvertToNote = useCallback(() => {
    if (messageData.hasStringContent) {
      convertTextToNote(messageData.content as string);
    }
  }, [convertTextToNote, messageData.content, messageData.hasStringContent]);

  const handleCopy = useCallback(() => {
    if (messageData.hasStringContent) {
      onCopy(messageData.content as string);
    }
  }, [onCopy, messageData.content, messageData.hasStringContent]);

  const renderInfo = useMemo(() => {
    if (messageData.isUser) return null;

    return (
      <Typography noWrap variant="caption" sx={{ mb: 1, color: 'text.disabled', mr: 'auto' }}>
        {assistantInfo.name}
      </Typography>
    );
  }, [messageData.isUser, assistantInfo.name]);

  // Render compact image thumbnails using ImagePreviewGrid
  const renderImages = useMemo(() => {
    if (!message.images || message.images.length === 0) return null;

    return (
      <ImagePreviewGrid
        images={message.images}
        disabled
        sx={{
          justifyContent: messageData.isUser ? 'flex-end' : 'flex-start',
          mb: 1, // Small margin below images
        }}
      />
    );
  }, [message.images, messageData.isUser]);

  const renderBody = useMemo(
    () => (
      <Stack direction="column" gap={0.5}>
        <Stack
          sx={{
            p: 1.5,
            width: 'fit-content',
            minWidth: 48,
            maxWidth: 600,
            borderRadius: 1,
            typography: 'body2',
            bgcolor: 'background.neutral',
            ...(messageData.isUser && {
              color: theme.palette.common.white,
              bgcolor: 'primary.lighter',
            }),
          }}
        >
          {renderImages}

          {messageData.hasBodyComponent ? (
            (message as any).body
          ) : typeof messageData.content === 'string' ? (
            <MarkdownWithCitations
              sx={{ '& p': { typography: 'body2', margin: 0 } }}
              citations={message.citations || []}
            >
              {messageData.content || ''}
            </MarkdownWithCitations>
          ) : (
            messageData.content
          )}
        </Stack>
        {helperText && (
          <Typography variant="caption" sx={{ color: 'text.disabled', textAlign: 'right' }}>
            {helperText}
          </Typography>
        )}
      </Stack>
    ),
    [
      messageData.content,
      messageData.isUser,
      messageData.hasBodyComponent,
      message,
      helperText,
      theme.palette.common.white,
    ]
  );

  const renderActions = useMemo(() => {
    if (!messageData.hasStringContent) {
      return null;
    }

    return (
      <Box
        className="message-actions"
        sx={{
          pt: 0.5,
          opacity: 0,
          top: '100%',
          display: 'flex',
          position: 'absolute',
          transition: theme.transitions.create(['opacity'], {
            duration: theme.transitions.duration.shorter,
          }),
          right: 0,
          left: 'unset',
          gap: 0.5,
        }}
      >
        <Tooltip title="Convert to source" arrow>
          <IconButton
            disabled={isConvertingToResource}
            size="small"
            onClick={handleConvertToResource}
          >
            {isConvertingToResource ? (
              <CircularProgress size={16} />
            ) : (
              <Iconify
                color={theme.palette.grey[700]}
                icon="material-symbols:convert-to-text-rounded"
                width={16}
              />
            )}
          </IconButton>
        </Tooltip>
        <Tooltip title="Convert to note" arrow>
          <IconButton disabled={isConvertingToNote} size="small" onClick={handleConvertToNote}>
            {isConvertingToNote ? (
              <CircularProgress size={16} />
            ) : (
              <Iconify color={theme.palette.grey[700]} icon="material-symbols:note" width={16} />
            )}
          </IconButton>
        </Tooltip>
        <Tooltip title="Copy" arrow>
          <IconButton size="small" onClick={handleCopy}>
            <Iconify
              color={theme.palette.grey[700]}
              icon="material-symbols:content-copy"
              width={16}
            />
          </IconButton>
        </Tooltip>
      </Box>
    );
  }, [
    messageData.hasStringContent,
    theme.transitions,
    theme.palette.grey,
    isConvertingToResource,
    isConvertingToNote,
    handleConvertToResource,
    handleConvertToNote,
    handleCopy,
  ]);

  // Always render the message, even if content is empty (for streaming placeholders)
  return (
    <Box sx={{ mb: 5, display: 'flex', justifyContent: messageData.isUser ? 'flex-end' : 'unset' }}>
      {messageData.isAssistant && <Box sx={{ mr: 2 }}>{assistantInfo.avatar}</Box>}

      <Stack alignItems={messageData.isUser ? 'flex-end' : 'flex-start'}>
        {renderInfo}

        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            position: 'relative',
            '&:hover': { '& .message-actions': { opacity: 1 } },
          }}
        >
          {renderBody}
          {renderActions}
        </Box>
      </Stack>
    </Box>
  );
});

// Add display name for debugging
ChatMessageItem.displayName = 'ChatMessageItem';
