import type { Theme, SxProps } from '@mui/material';

import { memo, useMemo } from 'react';

import { useTheme } from '@mui/material';

import { Scrollbar } from 'src/components/scrollbar';
import AnimateDots from 'src/components/animate/animate-dots';
import { LoadingScreen } from 'src/components/loading-screen';

import { ChatMessageItem } from './chat-message-item';
import { useMessagesScroll } from '../hooks/use-messages-scroll';

import type { ChatMessage, ChatProgressEvent } from '../types';

// ----------------------------------------------------------------------

type Props = {
  loading: boolean;
  messages: ChatMessage[];
  isReplying: boolean;
  currentProgress?: ChatProgressEvent['data'] | null;
  sx?: SxProps<Theme>;
};

export const ChatMessageList = memo<Props>(({ messages = [], loading, isReplying, currentProgress, sx = {} }) => {
  const theme = useTheme();
  const { messagesEndRef } = useMessagesScroll(messages);

  // Memoize the placeholder message for typing indicator (only when no content yet)
  const placeholderMessage = useMemo(
    (): ChatMessage =>
      ({
        id: 'replying-placeholder',
        conversationId: 'placeholder',
        role: 'assistant',
        content: '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        // Add the body field for the React component (AnimateDots)
        body: <AnimateDots size={6} sx={{ mb: 1 }} />,
      }) as ChatMessage & { body: React.ReactNode },
    []
  );

  // Memoize the scrollbar styles
  const scrollbarStyles = useMemo(
    () => ({
      px: 3,
      pt: 5,
      pb: 3,
      flex: '1 1 auto',
      minHeight: 0,
      height: '100%',
      [theme.breakpoints.down('sm')]: {
        px: 1,
        pt: 2,
        pb: 2,
      },
      '& .simplebar-content': {
        transition: 'all 0.3s ease-in-out',
        minHeight: '100%',
        display: 'flex',
        flexDirection: 'column',
      },
      ...sx,
    }),
    [theme.breakpoints, sx]
  );

  // Memoize the message items
  const messageItems = useMemo(
    () => messages.map((message) => <ChatMessageItem key={message.id} message={message} />),
    [messages]
  );

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <Scrollbar ref={messagesEndRef} sx={scrollbarStyles}>
      {messageItems}
      {isReplying && (
        <ChatMessageItem 
          message={placeholderMessage} 
          helperText={currentProgress?.message}
        />
      )}
    </Scrollbar>
  );
});

// Add display name for debugging
ChatMessageList.displayName = 'ChatMessageList';
