import { useState, useCallback } from 'react';

import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>witch,
  TextField,
  Typography,
  DialogTitle,
  DialogActions,
  DialogContent,
  FormControlLabel,
} from '@mui/material';

import { useChatPreferences } from '../hooks/use-chat-preferences';

import type { ChatPreferences } from '../types';

// ----------------------------------------------------------------------

const PROMPT_TEMPLATES = {
  basic: `Focus your analysis on statements made by speakers labeled 'Participant'. Unless specified, you can ignore statements from the 'Interviewer'.`,

  collaborative: `## Core Instructions
**Persona:** You are my collaborative research partner. Your primary goal is to help me think critically and deeply about the provided source data.

## Interaction Model
For every prompt I give you, follow this two-step process:

**Step 1: The Direct Answer**
First, provide a direct, concise answer to my question. This answer must be based exclusively on the provided source documents.

**Step 2: Points for Deeper Consideration**
After the direct answer, add a section titled "Points for Deeper Consideration". In this section, you will act as a critical thinking partner. Without offering your own opinions, you MUST:
    * Highlight any subtle tensions, ambiguities, or contradictions you find in the data related to my question.
    * Surface underlying assumptions that may be present in the participants' statements.
    * Propose one or two powerful, open-ended follow-up questions that would challenge me to explore the topic from a new angle.`,

  thematic: `## Core Instructions
**Persona:** You are an expert qualitative research assistant. Your audience is a senior researcher preparing a client-facing report. Your work must be rigorous, objective, and precise.

**Guiding Principles:**
1.  **Source-Grounded:** Base 100% of your analysis on the provided source documents. Never infer, editorialize, or add outside information.
2.  **Prioritize Participants:** Unless specified otherwise, your analysis should focus on statements from speakers labeled 'Participant'.
3.  **Neutral Tone:** Maintain a neutral, analytical tone throughout your response.

## Output Format
When asked to identify and summarize themes, you MUST structure your entire response using the following Markdown format:

### Key Insights Summary
(A dense, one-paragraph synthesis of the most critical findings and themes.)

### Detailed Thematic Analysis
**Theme 1: [Insert Descriptive Theme Name]**
**Definition:** (A 1-2 sentence definition of the theme in your own words, based on the data.)
**Supporting Evidence:**
    * (A direct quote from a participant that exemplifies the theme.)
    * (A second, different quote that adds nuance or weight to the theme.)

**Theme 2: [Insert Descriptive Theme Name]**
**Definition:** (A 1-2 sentence definition of the theme.)
**Supporting Evidence:**
    * (A direct quote illustrating this second theme.)`,
} as const;

type PromptType = keyof typeof PROMPT_TEMPLATES | null;

interface ChatPreferencesDialogProps {
  open: boolean;
  onClose: () => void;
  projectId?: string;
}

export function ChatPreferencesDialog({ open, onClose, projectId }: ChatPreferencesDialogProps) {
  const { preferences, updatePreferences } = useChatPreferences(projectId);

  const [localPreferences, setLocalPreferences] = useState<ChatPreferences>(preferences);
  const [selectedPromptType, setSelectedPromptType] = useState<PromptType>(null);

  const handleSwitchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setLocalPreferences((prev) => ({
      ...prev,
      enableSearchQuery: event.target.checked,
    }));
  }, []);

  const handleInstructionsChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setLocalPreferences((prev) => ({
      ...prev,
      userInstructions: event.target.value,
    }));
    // Clear selected prompt type when user manually edits
    setSelectedPromptType(null);
  }, []);

  const handlePromptChipClick = useCallback(
    (promptType: PromptType) => {
      if (promptType === selectedPromptType) {
        // If clicking the same chip, deselect it and clear instructions
        setSelectedPromptType(null);
        setLocalPreferences((prev) => ({
          ...prev,
          userInstructions: '',
        }));
      } else {
        // Select new prompt type and update instructions
        setSelectedPromptType(promptType);
        setLocalPreferences((prev) => ({
          ...prev,
          userInstructions: promptType ? PROMPT_TEMPLATES[promptType] : '',
        }));
      }
    },
    [selectedPromptType]
  );

  const handleSave = useCallback(() => {
    updatePreferences(localPreferences);
    onClose();
  }, [localPreferences, updatePreferences, onClose]);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: { minHeight: 400 },
      }}
    >
      <DialogTitle>Chat Preferences</DialogTitle>

      <DialogContent>
        <Stack spacing={3} sx={{ pt: 1 }}>
          <FormControlLabel
            control={
              <Switch
                checked={localPreferences.enableSearchQuery}
                onChange={handleSwitchChange}
                name="enableSearchQuery"
              />
            }
            label={
              <Stack>
                <Typography variant="body1">Enable Source-Grounded Analysis</Typography>
                <Typography variant="body2" color="text.secondary">
                  Enable a more precise analysis. The AI will first retrieve specific evidence from
                  your documents to build its answer, which enables verifiable citations and
                  improves accuracy
                </Typography>
              </Stack>
            }
            sx={{
              alignItems: 'flex-start',
              '& .MuiFormControlLabel-label': { mt: -0.5 },
            }}
          />

          <Box>
            <Typography variant="subtitle2" sx={{ mb: 1.5 }}>
              Default Prompt Suggestions
            </Typography>
            <Stack direction="row" spacing={1}>
              <Chip
                label="Basic"
                variant={selectedPromptType === 'basic' ? 'filled' : 'outlined'}
                color={selectedPromptType === 'basic' ? 'primary' : 'default'}
                onClick={() => handlePromptChipClick('basic')}
                sx={{ cursor: 'pointer' }}
              />
              <Chip
                label="Collaborative"
                variant={selectedPromptType === 'collaborative' ? 'filled' : 'outlined'}
                color={selectedPromptType === 'collaborative' ? 'primary' : 'default'}
                onClick={() => handlePromptChipClick('collaborative')}
                sx={{ cursor: 'pointer' }}
              />
              <Chip
                label="Thematic"
                variant={selectedPromptType === 'thematic' ? 'filled' : 'outlined'}
                color={selectedPromptType === 'thematic' ? 'primary' : 'default'}
                onClick={() => handlePromptChipClick('thematic')}
                sx={{ cursor: 'pointer' }}
              />
            </Stack>
          </Box>

          <TextField
            label="Custom Instructions"
            multiline
            rows={6}
            value={localPreferences.userInstructions}
            onChange={handleInstructionsChange}
            fullWidth
            helperText={`Guide the AI's analysis with instructions that are applied to every message. For example: "Focus only on statements made by Participants.`}
            slotProps={{
              htmlInput: {
                className: 'scrollbar',
              },
            }}
          />
        </Stack>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} color="inherit">
          Cancel
        </Button>
        <Button onClick={handleSave} variant="contained">
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
}
