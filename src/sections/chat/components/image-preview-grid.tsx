import type { Theme, SxProps } from '@mui/material';

import React, { memo, useMemo } from 'react';

import { Box, Paper, Tooltip, IconButton } from '@mui/material';

import { Iconify } from 'src/components/iconify';

import { formatFileSize } from '../utils/image-validation';

import type { ImageUpload, ImageAttachment } from '../types';

// ----------------------------------------------------------------------

interface ImagePreviewGridProps {
  images: ImageUpload[] | ImageAttachment[];
  onRemove?: (imageId: string) => void;
  disabled?: boolean;
  sx?: SxProps<Theme>;
}

export const ImagePreviewGrid = memo<ImagePreviewGridProps>(
  ({ images = [], onRemove, disabled = false, sx }) => {
    if (images.length === 0) return null;

    return (
      <Box
        sx={[
          {
            display: 'flex',
            gap: 0.5,
            overflow: 'hidden',
            maxWidth: '100%',
          },
          ...(Array.isArray(sx) ? sx : [sx]),
        ]}
      >
        {images.map((image) => (
          <ImagePreviewItem key={image.id} image={image} onRemove={onRemove} disabled={disabled} />
        ))}
      </Box>
    );
  }
);

// ----------------------------------------------------------------------

interface ImagePreviewItemProps {
  image: ImageUpload | ImageAttachment;
  onRemove?: (imageId: string) => void;
  disabled?: boolean;
}

const ImagePreviewItem = memo<ImagePreviewItemProps>(({ image, onRemove, disabled }) => {
  const imageData = useMemo(() => {
    const imageUrl = 'file' in image ? URL.createObjectURL(image.file) : image.url;
    const filename = 'file' in image ? image.file.name : image.filename;
    const fileSize = 'file' in image ? image.file.size : image.size;
    const status = 'status' in image ? image.status : 'uploaded';

    return {
      imageUrl,
      filename,
      fileSize,
      status,
      isUploading: status === 'uploading',
      hasFailed: status === 'failed',
    };
  }, [image]);

  const handleRemoveClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    event.preventDefault();
    if (onRemove && !disabled) {
      onRemove(image.id);
    }
  };

  // Create tooltip content with image preview
  const tooltipContent = (
    <Box sx={{ p: 1, maxWidth: 250 }}>
      <Box
        component="img"
        src={imageData.imageUrl}
        alt={imageData.filename}
        sx={{
          width: '100%',
          height: 'auto',
          maxHeight: 200,
          objectFit: 'contain',
          borderRadius: 1,
          display: 'block',
          mb: 1,
        }}
      />
      <Box sx={{ textAlign: 'center' }}>
        <Box
          sx={{
            fontSize: '0.75rem',
            fontWeight: 500,
            color: 'white',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            mb: 0.5,
          }}
        >
          {imageData.filename}
        </Box>
        <Box
          sx={{
            fontSize: '0.7rem',
            color: 'rgba(255, 255, 255, 0.7)',
          }}
        >
          {formatFileSize(imageData.fileSize)}
        </Box>
      </Box>
    </Box>
  );

  return (
    <Tooltip
      title={tooltipContent}
      placement="top"
      arrow
      enterDelay={300}
      leaveDelay={0}
      slotProps={{
        tooltip: {
          sx: {
            bgcolor: 'rgba(0, 0, 0, 0.9)',
            maxWidth: 300,
            '& .MuiTooltip-arrow': {
              color: 'rgba(0, 0, 0, 0.9)',
            },
          },
        },
      }}
    >
      <Paper
        elevation={1}
        sx={(theme) => ({
          position: 'relative',
          borderRadius: 0.75,
          overflow: 'visible',
          width: 32,
          height: 32,
          flexShrink: 0,
          cursor: 'pointer',
          border: `1px solid ${theme.vars.palette.divider}`,
          transition: theme.transitions.create(['transform', 'box-shadow'], {
            duration: theme.transitions.duration.shorter,
          }),
          '&:hover': {
            transform: 'scale(1.05)',
            boxShadow: theme.shadows[4],
          },
          ...(imageData.hasFailed && {
            borderColor: 'error.main',
            bgcolor: theme.vars.palette.error.lighter,
          }),
        })}
      >
        {/* Image container */}
        <Box
          sx={{
            width: '100%',
            height: '100%',
            borderRadius: 0.75,
            overflow: 'hidden',
          }}
        >
          <Box
            component="img"
            src={imageData.imageUrl}
            alt={imageData.filename}
            sx={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              display: 'block',
              filter: imageData.isUploading || imageData.hasFailed ? 'grayscale(50%)' : 'none',
            }}
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
            }}
          />

          {/* Status overlays */}
          {(imageData.isUploading || imageData.hasFailed) && (
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                bgcolor: 'rgba(0, 0, 0, 0.4)',
              }}
            >
              {imageData.isUploading ? (
                <Box
                  sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    bgcolor: 'white',
                    animation: 'pulse 1.5s ease-in-out infinite',
                    '@keyframes pulse': {
                      '0%': { opacity: 1 },
                      '50%': { opacity: 0.5 },
                      '100%': { opacity: 1 },
                    },
                  }}
                />
              ) : (
                <Iconify
                  icon="material-symbols:error-outline"
                  sx={{ color: 'error.main', fontSize: 12 }}
                />
              )}
            </Box>
          )}
        </Box>

        {/* Remove button */}
        {onRemove && !disabled && (
          <IconButton
            size="small"
            onClick={handleRemoveClick}
            sx={(theme) => ({
              position: 'absolute',
              top: 0,
              right: -6,
              bgcolor: theme.vars.palette.error.main,
              color: 'white',
              width: 12,
              height: 12,
              minWidth: 12,
              boxShadow: theme.shadows[2],
              zIndex: 10,
              '&:hover': {
                bgcolor: theme.vars.palette.error.dark,
                transform: 'scale(1.1)',
                boxShadow: theme.shadows[4],
              },
              '& .MuiSvgIcon-root': {
                fontSize: 12,
              },
            })}
          >
            <Iconify icon="material-symbols:close" width={10} height={10} />
          </IconButton>
        )}
      </Paper>
    </Tooltip>
  );
});

ImagePreviewGrid.displayName = 'ImagePreviewGrid';
ImagePreviewItem.displayName = 'ImagePreviewItem';
