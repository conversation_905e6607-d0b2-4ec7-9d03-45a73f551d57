import type { Theme, SxProps } from '@mui/material';

import { memo } from 'react';

import {
  Box,
  List,
  Alert,
  ListItem,
  Collapse,
  IconButton,
  AlertTitle,
  ListItemText,
  ListItemIcon,
} from '@mui/material';

import { Iconify } from 'src/components/iconify';

import type { ImageValidationError } from '../utils/image-validation';

// ----------------------------------------------------------------------

interface ImageUploadErrorsProps {
  errors: ImageValidationError[];
  onDismiss?: (errorId: string) => void;
  onDismissAll?: () => void;
  sx?: SxProps<Theme>;
}

export const ImageUploadErrors = memo<ImageUploadErrorsProps>(
  ({ errors = [], onDismiss, onDismissAll, sx }) => {
    if (errors.length === 0) return null;

    const getErrorIcon = (code: ImageValidationError['code']) => {
      switch (code) {
        case 'SIZE':
          return 'material-symbols:folder-zip-outline';
        case 'FORMAT':
          return 'material-symbols:image-not-supported-outline';
        case 'COUNT':
          return 'material-symbols:format-list-numbered';
        case 'TOTAL_SIZE':
          return 'material-symbols:storage';
        default:
          return 'material-symbols:error-outline';
      }
    };

    return (
      <Collapse in={errors.length > 0}>
        <Box
          sx={[
            {
              mb: 1,
              width: '100%',
            },
            ...(Array.isArray(sx) ? sx : [sx]),
          ]}
        >
          <Alert
            severity="warning"
            sx={{
              '& .MuiAlert-message': {
                width: '100%',
                overflow: 'hidden',
              },
            }}
            action={
              onDismissAll && errors.length > 1 ? (
                <IconButton color="inherit" size="small" onClick={onDismissAll} sx={{ mt: -0.5 }}>
                  <Iconify icon="material-symbols:close" width={18} height={18} />
                </IconButton>
              ) : undefined
            }
          >
            <AlertTitle sx={{ mb: errors.length > 1 ? 1 : 0 }}>
              {errors.length === 1 ? 'Image Upload Issue' : `${errors.length} Image Upload Issues`}
            </AlertTitle>

            {errors.length === 1 ? (
              // Single error - simplified display
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Iconify
                  icon={getErrorIcon(errors[0].code)}
                  sx={{ color: 'warning.main', fontSize: 16, flexShrink: 0 }}
                />
                <Box sx={{ flex: 1, minWidth: 0 }}>
                  <Box sx={{ fontWeight: 500, fontSize: '0.875rem' }}>{errors[0].filename}</Box>
                  <Box sx={{ color: 'text.secondary', fontSize: '0.75rem' }}>{errors[0].error}</Box>
                </Box>
                {onDismiss && (
                  <IconButton
                    size="small"
                    onClick={() => onDismiss(errors[0].imageId)}
                    sx={{ ml: 1, color: 'text.secondary' }}
                  >
                    <Iconify icon="material-symbols:close" width={16} height={16} />
                  </IconButton>
                )}
              </Box>
            ) : (
              // Multiple errors - list display
              <List dense disablePadding>
                {errors.map((error, index) => (
                  <ListItem
                    key={`${error.imageId}-${index}`}
                    disablePadding
                    sx={{
                      py: 0.5,
                      ...(index < errors.length - 1 && {
                        borderBottom: (theme) => `1px solid ${theme.vars.palette.divider}`,
                      }),
                    }}
                  >
                    <ListItemIcon sx={{ minWidth: 24 }}>
                      <Iconify
                        icon={getErrorIcon(error.code)}
                        sx={{ color: 'warning.main', fontSize: 16 }}
                      />
                    </ListItemIcon>

                    <ListItemText
                      primary={error.filename}
                      secondary={error.error}
                      primaryTypographyProps={{
                        variant: 'body2',
                        sx: { fontWeight: 500 },
                      }}
                      secondaryTypographyProps={{
                        variant: 'caption',
                      }}
                      sx={{ mr: 1 }}
                    />

                    {onDismiss && (
                      <IconButton
                        size="small"
                        onClick={() => onDismiss(error.imageId)}
                        sx={{
                          color: 'text.secondary',
                          '&:hover': { color: 'text.primary' },
                        }}
                      >
                        <Iconify icon="material-symbols:close" width={16} height={16} />
                      </IconButton>
                    )}
                  </ListItem>
                ))}
              </List>
            )}
          </Alert>
        </Box>
      </Collapse>
    );
  }
);

ImageUploadErrors.displayName = 'ImageUploadErrors';
