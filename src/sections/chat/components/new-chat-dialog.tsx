import { memo } from 'react';

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alog,
  Typography,
  IconButton,
  DialogTitle,
  DialogContent,
  CircularProgress,
} from '@mui/material';

import { Iconify } from 'src/components/iconify';

interface NewChatDialogProps {
  open: boolean;
  onClose: () => void;
  onSaveAndNew: () => void;
  onStartFresh: () => void;
  isConverting: boolean;
}

export const NewChatDialog = memo<NewChatDialogProps>(
  ({ open, onClose, onSaveAndNew, onStartFresh, isConverting }) => (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          p: 1,
        },
      }}
    >
      <DialogTitle sx={{ py: 2, pr: 1 }}>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Stack direction="row" alignItems="center" gap={1.5}>
            <Iconify
              icon="material-symbols:chat-bubble-outline"
              sx={{ fontSize: 24, color: 'primary.main' }}
            />
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              Start a New Chat?
            </Typography>
          </Stack>

          <IconButton
            onClick={onClose}
            disabled={isConverting}
            size="small"
            sx={{
              color: 'text.secondary',
              '&:hover': {
                color: 'text.primary',
                bgcolor: 'action.hover',
              },
            }}
          >
            <Iconify icon="material-symbols:close-rounded" sx={{ fontSize: 20 }} />
          </IconButton>
        </Stack>
      </DialogTitle>

      <DialogContent sx={{ pt: 0, pb: 3, px: 3 }}>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2.5 }}>
          You have an ongoing conversation. What would you like to do?
        </Typography>

        <Stack spacing={1}>
          <Button
            variant="contained"
            fullWidth
            startIcon={
              isConverting ? (
                <CircularProgress size={18} color="inherit" />
              ) : (
                <Iconify icon="material-symbols:save-rounded" sx={{ fontSize: 18 }} />
              )
            }
            onClick={onSaveAndNew}
            disabled={isConverting}
            sx={{
              justifyContent: 'flex-start',
              py: 1.25,
              px: 2,
              textAlign: 'left',
              '&:hover': {
                transform: 'translateY(-1px)',
                boxShadow: 2,
              },
              transition: 'all 0.2s ease-in-out',
            }}
          >
            <Stack spacing={0.25} sx={{ textAlign: 'left' }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, fontSize: '0.875rem' }}>
                Save and Start New
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.85, fontSize: '0.75rem' }}>
                This chat will be saved as a new source file in your project
              </Typography>
            </Stack>
          </Button>

          <Button
            variant="outlined"
            fullWidth
            startIcon={<Iconify icon="material-symbols:refresh-rounded" sx={{ fontSize: 18 }} />}
            onClick={onStartFresh}
            disabled={isConverting}
            sx={{
              justifyContent: 'flex-start',
              py: 1.25,
              px: 2,
              textAlign: 'left',
              borderColor: 'divider',
              '&:hover': {
                transform: 'translateY(-1px)',
                borderColor: 'primary.main',
                bgcolor: 'action.hover',
              },
              transition: 'all 0.2s ease-in-out',
            }}
          >
            <Stack spacing={0.25} sx={{ textAlign: 'left' }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, fontSize: '0.875rem' }}>
                Start fresh
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                The current chat will be discarded
              </Typography>
            </Stack>
          </Button>
        </Stack>
      </DialogContent>
    </Dialog>
  )
);

NewChatDialog.displayName = 'NewChatDialog';
