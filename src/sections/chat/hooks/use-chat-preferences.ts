import { useCallback } from 'react';
import { useDispatch } from 'react-redux';

import useAnalytics from 'src/hooks/analytics';

import { useAppSelector } from 'src/store';
import { setChatPreferences } from 'src/store/slices/agent-settings';
import { selectChatPreferencesForProject } from 'src/store/slices/agent-settings/selectors';

import type { ChatPreferences } from '../types';

export const useChatPreferences = (projectId?: string) => {
  const dispatch = useDispatch();
  const { trackEvent } = useAnalytics();

  const preferences = useAppSelector((state) =>
    selectChatPreferencesForProject(state, projectId ?? '')
  );

  // Update preferences for the project
  const updatePreferences = useCallback(
    (newPreferences: ChatPreferences) => {
      if (!projectId) return;

      dispatch(setChatPreferences({ projectId, preferences: newPreferences }));

      trackEvent({
        eventCategory: 'Aida Chat',
        eventAction: 'Updated preferences',
        properties: newPreferences,
      });
    },
    [dispatch, projectId, trackEvent]
  );

  return {
    preferences,
    updatePreferences,
  };
};
