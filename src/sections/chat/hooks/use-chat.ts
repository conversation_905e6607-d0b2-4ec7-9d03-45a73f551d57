import type {
  <PERSON>rrorEvent,
  StartEvent,
  ChatMessage,
  StreamEvent,
  ContentEvent,
  UseChatReturn,
  UseChatOptions,
  CompletionEvent,
  ConnectionEvent,
  ChatProgressEvent,
} from 'src/types';

import { toast } from 'sonner';
import * as Sentry from '@sentry/react';
import { uuidv4 } from 'minimal-shared/utils';
import { useRef, useState, useEffect, useCallback } from 'react';

import { AUTH } from 'src/lib/firebase';
import { CONFIG } from 'src/global-config';

import useAnalytics from '../../../hooks/analytics';
import { parseSSEStream } from '../utils/sse-parser';
import { createMessage } from '../utils/message-factory';
import { fileToBase64 } from '../utils/image-validation';
import { useChatPreferences } from './use-chat-preferences';
import { useConversation } from '../../../hooks/use-conversation';

import type { AIModel, ImageUpload, ImageAttachment } from '../types';

const useChat = (options: UseChatOptions = {}): UseChatReturn => {
  const {
    conversationId: initialConversationId,
    model = 'gemini-2.5-flash',
    resources = [],
    projectId,
  } = options;
  const { trackEvent } = useAnalytics();

  // Use the preferences hook for project-specific preferences
  const { preferences } = useChatPreferences(projectId);

  // State management - simplified like legacy
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [currentModel, setCurrentModel] = useState<AIModel>(model);
  const [isStreaming, setIsStreaming] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentStreamingMessageId, setCurrentStreamingMessageId] = useState<string | null>(null);
  const [abortController, setAbortController] = useState<AbortController | null>(null);
  const [currentProgress, setCurrentProgress] = useState<ChatProgressEvent['data'] | null>(null);

  // Ref to track streaming message ID immediately (to avoid state batching issues)
  const streamingMessageIdRef = useRef<string | null>(null);

  // Custom hooks
  const { conversationId, isLoading, createNewConversation, resetConversation } = useConversation({
    initialConversationId,
    projectId,
    model: currentModel,
  });

  // Append message to conversation - following legacy pattern
  const appendMessage = useCallback(
    (content: string, role: 'user' | 'assistant', existingMessageId?: string) => {
      const messageId = existingMessageId || uuidv4();

      if (existingMessageId) {
        // Update existing message
        setMessages((prev) =>
          prev.map((msg) =>
            msg.id === existingMessageId ? { ...msg, content: msg.content + content } : msg
          )
        );
      } else {
        // Create new message using message factory
        const messageData = createMessage({
          id: messageId,
          conversationId: conversationId || 'temp',
          role,
          content,
          model: currentModel,
        });
        setMessages((prev) => [...prev, messageData]);
      }

      return messageId;
    },
    [conversationId, currentModel]
  );

  // Handle streaming events - simplified like legacy
  const handleStreamEvent = useCallback(
    (event: StreamEvent) => {
      switch (event.type) {
        case 'connection': {
          const connectionEvent = event as ConnectionEvent;
          if (connectionEvent.data.status === 'connected') {
            setError(null);
          }
          break;
        }

        case 'start': {
          const startEvent = event as StartEvent;
          if (startEvent.data.userMessage) {
            setMessages((prev) => [...prev, startEvent.data.userMessage!]);
          }
          break;
        }

        case 'progress': {
          const progressEvent = event as ChatProgressEvent;
          setCurrentProgress(progressEvent.data);
          break;
        }

        case 'content': {
          const contentEvent = event as ContentEvent;

          // Create bot message if it doesn't exist yet (using ref for immediate tracking)
          let messageId = streamingMessageIdRef.current;
          if (!messageId) {
            messageId = appendMessage('', 'assistant');
            streamingMessageIdRef.current = messageId;
            setCurrentStreamingMessageId(messageId);
          }

          // Update the message directly with the messageId
          setMessages((prevMessages) =>
            prevMessages.map((msg) =>
              msg.id === messageId ? { ...msg, content: msg.content + contentEvent.data.text } : msg
            )
          );

          break;
        }

        case 'completion': {
          const completionEvent = event as CompletionEvent;
          const messageId = streamingMessageIdRef.current;

          if (messageId) {
            // Final update - ensure content is preserved and add metadata and citations
            setMessages((prev) =>
              prev.map((msg) =>
                msg.id === messageId
                  ? {
                      ...msg,
                      // Preserve existing content and only add metadata
                      metadata: {
                        finishReason: completionEvent.data.finishReason,
                        duration: completionEvent.data.duration,
                        chunks: completionEvent.data.textChunks,
                        responseLength: completionEvent.data.responseLength,
                      },
                      // Add citations if available
                      citations: completionEvent.data.citations || [],
                    }
                  : msg
              )
            );
          }

          // Clean up streaming state
          setIsStreaming(false);
          setCurrentStreamingMessageId(null);
          streamingMessageIdRef.current = null;
          setCurrentProgress(null);

          break;
        }

        case 'error': {
          const errorEvent = event as ErrorEvent;
          const messageId = currentStreamingMessageId;

          if (messageId) {
            setMessages((prev) => prev.filter((msg) => msg.id !== messageId));
          }

          const errorMessage = errorEvent.data.error || errorEvent.data.message || 'Unknown error';
          setError(errorMessage);
          setIsStreaming(false);
          setCurrentStreamingMessageId(null);
          streamingMessageIdRef.current = null;
          setCurrentProgress(null);

          toast.error('Chat error', {
            description: errorMessage,
          });

          Sentry.captureException(new Error(errorMessage), {
            extra: errorEvent.data,
          });
          break;
        }

        default:
          break;
      }
    },
    [currentStreamingMessageId, appendMessage]
  );

  // Clean up streaming state
  const cleanupStreaming = useCallback(() => {
    if (currentStreamingMessageId) {
      setMessages((prev) => prev.filter((msg) => msg.id !== currentStreamingMessageId));
    }
    setIsStreaming(false);
    setCurrentStreamingMessageId(null);
    streamingMessageIdRef.current = null;
    setError(null);
    setCurrentProgress(null);
  }, [currentStreamingMessageId]);

  // Process streaming response
  const processStreamingResponse = useCallback(
    async (
      reader: ReadableStreamDefaultReader<Uint8Array>,
      decoder: TextDecoder,
      controller: AbortController
    ) => {
      let buffer = '';
      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          if (controller.signal.aborted) {
            break;
          }

          const chunk = decoder.decode(value, { stream: true });
          buffer += chunk;
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          if (lines.length > 0) {
            const completeChunk = lines.join('\n');
            const events = parseSSEStream(completeChunk);

            for (const event of events) {
              handleStreamEvent(event);
            }
          }
        }

        if (buffer.trim()) {
          const events = parseSSEStream(buffer);
          for (const event of events) {
            handleStreamEvent(event);
          }
        }

        if (currentStreamingMessageId) {
          setIsStreaming(false);
          setCurrentStreamingMessageId(null);
          streamingMessageIdRef.current = null;
        }
      } finally {
        reader.releaseLock();
      }
    },
    [handleStreamEvent, currentStreamingMessageId]
  );

  // Send message with streaming - following legacy pattern
  const sendMessage = useCallback(
    async (
      message: string,
      images: ImageUpload[] = [],
      messageOptions: { model?: AIModel; resources?: string[] } = {}
    ) => {
      if ((!message.trim() && images.length === 0) || isStreaming) return;

      const messageModel = messageOptions.model || currentModel;
      const messageResources = messageOptions.resources || resources;

      const controller = new AbortController();
      setAbortController(controller);

      try {
        const currentConversationId = conversationId || (await createNewConversation());

        setError(null);

        // Create user message with images for UI
        const imageAttachments: ImageAttachment[] = images.map((img) => ({
          id: img.id,
          url: URL.createObjectURL(img.file),
          filename: img.file.name,
          size: img.file.size,
          mimeType: img.file.type,
          status: 'uploaded' as const,
        }));

        const userMessage = createMessage({
          id: uuidv4(),
          conversationId: currentConversationId,
          role: 'user' as const,
          content: message,
          model: messageModel,
          images: imageAttachments,
        });

        // Add user message to UI
        setMessages((prev) => [...prev, userMessage]);

        // Start streaming state - don't create empty bot message yet
        setIsStreaming(true);
        setCurrentStreamingMessageId(null);
        streamingMessageIdRef.current = null;
        setCurrentProgress(null);

        const firebaseToken = await AUTH.currentUser?.getIdToken();
        if (!firebaseToken) {
          throw new Error('No authentication token available');
        }

        // Convert images to base64 for API
        const imageData =
          images.length > 0
            ? await Promise.all(
                images.map(async (img) => ({
                  id: img.id,
                  data: await fileToBase64(img.file),
                  filename: img.file.name,
                  mimeType: img.file.type,
                  size: img.file.size,
                }))
              )
            : undefined;

        const requestBody: any = {
          model: messageModel,
          message,
          resources: messageResources,
          preferences: {
            instructions: preferences.userInstructions,
            ragSearchEnabled: preferences.enableSearchQuery,
          },
        };

        // Only add images if there are any
        if (imageData && imageData.length > 0) {
          requestBody.images = imageData;
        }

        const response = await fetch(
          `${CONFIG.aidaApiUrl}/agent/conversations/${currentConversationId}/message/stream`,
          {
            method: 'POST',
            headers: {
              Authorization: `Bearer ${firebaseToken}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody),
            signal: controller.signal,
          }
        );

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const reader = response.body?.getReader();
        const decoder = new TextDecoder();

        if (!reader) {
          throw new Error('No response body');
        }

        // Process streaming response
        await processStreamingResponse(reader, decoder, controller);
      } catch (requestError) {
        if (requestError instanceof Error && requestError.name === 'AbortError') {
          return;
        }

        const errorMessage =
          requestError instanceof Error ? requestError.message : 'Failed to send message';
        setError(errorMessage);
        toast.error('Failed to send message', {
          description: errorMessage,
        });

        Sentry.captureException(requestError);
        cleanupStreaming();
      } finally {
        if (abortController === controller) {
          setAbortController(null);
        }
      }
    },
    [
      conversationId,
      currentModel,
      resources,
      preferences,
      isStreaming,
      createNewConversation,
      processStreamingResponse,
      cleanupStreaming,
      abortController,
    ]
  );

  // Stop streaming
  const handleStopStreaming = useCallback(() => {
    if (abortController) {
      abortController.abort();
      setAbortController(null);
    }

    cleanupStreaming();

    trackEvent({
      eventCategory: 'Aida Chat',
      eventAction: 'Stopped streaming',
    });
  }, [abortController, cleanupStreaming, trackEvent]);

  // Switch model
  const switchModel = useCallback(
    (newModel: AIModel) => {
      setCurrentModel(newModel);

      trackEvent({
        eventCategory: 'Aida Chat',
        eventAction: 'Switched model',
        properties: {
          fromModel: currentModel,
          toModel: newModel,
          messageCount: messages.length,
        },
      });
    },
    [currentModel, messages.length, trackEvent]
  );

  // Reset chat
  const resetChat = useCallback(() => {
    handleStopStreaming();
    setMessages([]);
    resetConversation();
    setIsStreaming(false);
    setCurrentStreamingMessageId(null);
    streamingMessageIdRef.current = null;
    setError(null);
    setCurrentProgress(null);
  }, [handleStopStreaming, resetConversation]);

  // Cleanup on unmount
  useEffect(
    () => () => {
      if (abortController) {
        abortController.abort();
      }
    },
    [abortController]
  );

  const allowSendMessage = resources.length > 0 && !isStreaming && !isLoading;

  return {
    // State
    messages,
    isLoading,
    isStreaming,
    conversationId,
    currentModel,
    allowSendMessage,

    // Streaming State
    error,
    currentProgress,

    // Actions
    createConversation: createNewConversation,
    sendMessage,
    switchModel,
    resetChat,

    // Streaming controls
    stopStreaming: handleStopStreaming,
  };
};

export default useChat;
