import { toast } from 'sonner';
import { marked } from 'marked';
import { useDispatch } from 'react-redux';
import { useState, useCallback } from 'react';

import { useAppSelector } from 'src/store';
import { geminiFlashModel } from 'src/lib/firebase';
import { addNote } from 'src/store/slices/notes/slice';
import { useCreateNoteMutation } from 'src/store/api/notes';
import { selectLastViewedProjectId } from 'src/store/slices/settings/selectors';

/**
 * Fallback utility function to generate a title from content
 * Used when AI generation fails or is unavailable
 */
const generateFallbackTitle = (content: string): string => {
  if (!content) return 'New Note';

  // Remove markdown formatting for title generation
  const cleanContent = content
    .replace(/[#*_`~[\]]/g, '') // Remove common markdown chars
    .replace(/\n/g, ' ') // Replace newlines with spaces
    .trim();

  // Try to get first sentence (ending with . ! or ?)
  const firstSentence = cleanContent.match(/^[^.!?]*[.!?]/);
  if (firstSentence && firstSentence[0].length <= 50) {
    return firstSentence[0].trim();
  }

  // If no sentence or too long, take first 50 characters
  if (cleanContent.length <= 50) {
    return cleanContent;
  }

  return cleanContent.substring(0, 47) + '...';
};

/**
 * AI-powered title generation using Gemini
 * Generates concise, contextual titles based on content analysis
 */
const generateAiTitle = async (content: string): Promise<string> => {
  if (!geminiFlashModel || !content?.trim()) {
    return generateFallbackTitle(content);
  }

  try {
    const prompt = `Analyze the following content and generate a concise, descriptive title (maximum 60 characters) that captures the main topic or key insight. The title should be clear, engaging, and suitable for a note-taking application.

Content to analyze:
${content.trim()}

Requirements:
- Maximum 60 characters
- No quotes or special formatting
- Focus on the main topic or key insight
- Be specific and descriptive
- Use title case

Generate only the title, nothing else.`;

    const result = await geminiFlashModel.generateContent(prompt);
    const generatedTitle = result.response.text()?.trim();

    if (generatedTitle && generatedTitle.length > 0 && generatedTitle.length <= 60) {
      return generatedTitle;
    }

    // If generated title is too long or empty, fallback
    return generateFallbackTitle(content);
  } catch (error) {
    console.warn('AI title generation failed, using fallback:', error);
    return generateFallbackTitle(content);
  }
};

const useConvertToNote = () => {
  const dispatch = useDispatch();
  const [isConverting, setIsConverting] = useState(false);
  const [createNote] = useCreateNoteMutation();
  const projectId = useAppSelector(selectLastViewedProjectId);

  const convertTextToNote = useCallback(
    async (content: string) => {
      if (!content || !content.trim()) {
        toast.error('No content to convert to note');
        return;
      }

      if (!projectId) {
        toast.error('No project selected. Please select a project first');
        return;
      }

      setIsConverting(true);

      try {
        // Use AI to generate a smart title
        const title = await generateAiTitle(content);

        // Convert markdown content to HTML
        const htmlContent = await marked(content.trim());

        const response = await createNote({
          payload: {
            title,
            content: htmlContent,
            projectId,
          },
        });

        if (response.data) {
          dispatch(addNote(response.data));
          toast.success(`Successfully converted to note: "${title}"`);
        } else {
          throw new Error('Failed to create note');
        }
      } catch (error) {
        console.error('Error converting text to note:', error);
        toast.error('Failed to convert to note');
      } finally {
        setIsConverting(false);
      }
    },
    [createNote, dispatch, projectId]
  );

  return {
    convertTextToNote,
    isConverting,
  };
};

export default useConvertToNote;
