import { useDropzone } from 'react-dropzone';
import { useState, useCallback } from 'react';

import { ImageHandler } from '../utils/image-handler';
import { IMAGE_CONSTRAINTS } from '../utils/image-validation';

import type { ImageUpload } from '../types';

interface UseImageUploadOptions {
  allowImageUpload: boolean;
  disabled?: boolean;
  onImagesAdd?: (images: ImageUpload[]) => void;
}

interface UseImageUploadReturn {
  attachedImages: ImageUpload[];
  setAttachedImages: React.Dispatch<React.SetStateAction<ImageUpload[]>>;
  handleImagesAdd: (files: File[]) => Promise<void>;
  handleImageRemove: (imageId: string) => void;
  handlePaste: (event: React.ClipboardEvent) => Promise<void>;
  getRootProps: () => any;
  getInputProps: () => any;
  isDragActive: boolean;
  isMaxReached: boolean;
  remainingSlots: number;
  openFileDialog: () => void;
}

/**
 * Custom hook for handling image uploads with consistent validation
 */
export function useImageUpload({
  allowImageUpload,
  disabled = false,
  onImagesAdd,
}: UseImageUploadOptions): UseImageUploadReturn {
  const [attachedImages, setAttachedImages] = useState<ImageUpload[]>([]);

  const isMaxReached = ImageHandler.isMaxImagesReached(attachedImages.length);
  const remainingSlots = ImageHandler.getRemainingSlots(attachedImages.length);

  // Handle adding images with validation
  const handleImagesAdd = useCallback(
    async (files: File[]) => {
      if (!allowImageUpload || disabled) return;

      const validImages = await ImageHandler.processImages(files, attachedImages);

      if (validImages.length > 0) {
        setAttachedImages((prev) => [...prev, ...validImages]);
        onImagesAdd?.(validImages);
      }
    },
    [allowImageUpload, disabled, attachedImages, onImagesAdd]
  );

  // Handle removing an image
  const handleImageRemove = useCallback((imageId: string) => {
    setAttachedImages((prev) => prev.filter((img) => img.id !== imageId));
  }, []);

  // Handle paste event
  const handlePaste = useCallback(
    async (event: React.ClipboardEvent) => {
      if (!allowImageUpload || disabled) return;

      const imageFiles = await ImageHandler.extractImagesFromClipboard(event.clipboardData);

      if (imageFiles.length > 0) {
        // Don't prevent default if there's also text content
        const hasText = ImageHandler.clipboardHasText(event.clipboardData);
        if (!hasText) {
          event.preventDefault();
        }

        await handleImagesAdd(imageFiles);
      }
    },
    [allowImageUpload, disabled, handleImagesAdd]
  );

  // Dropzone configuration
  const { getRootProps, getInputProps, isDragActive, open } = useDropzone({
    accept: ImageHandler.getDropzoneAccept(),
    multiple: true,
    disabled: disabled || !allowImageUpload || isMaxReached,
    onDrop: handleImagesAdd,
    noClick: true,
    maxSize: IMAGE_CONSTRAINTS.MAX_FILE_SIZE,
    onDropRejected: (fileRejections) => ImageHandler.handleDropRejection(fileRejections as any),
  });

  // Open file dialog programmatically
  const openFileDialog = useCallback(() => {
    if (!disabled && !isMaxReached && allowImageUpload) {
      open();
    }
  }, [disabled, isMaxReached, allowImageUpload, open]);

  return {
    attachedImages,
    setAttachedImages,
    handleImagesAdd,
    handleImageRemove,
    handlePaste,
    getRootProps,
    getInputProps,
    isDragActive,
    isMaxReached,
    remainingSlots,
    openFileDialog,
  };
}
