import { useRef, useEffect, useCallback } from 'react';

import type { ChatMessage } from '../types';

// ----------------------------------------------------------------------

export type UseMessagesScrollReturn = {
  messagesEndRef: React.RefObject<HTMLDivElement>;
  scrollToBottom: () => void;
};

export function useMessagesScroll(messages: ChatMessage[]): UseMessagesScrollReturn {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const lastMessageCount = useRef(messages.length);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const scrollToBottom = useCallback(() => {
    if (!messagesEndRef.current) {
      return;
    }

    // Cancel any pending scroll
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // Use requestAnimationFrame for smooth scrolling
    scrollTimeoutRef.current = setTimeout(() => {
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollTop = messagesEndRef.current.scrollHeight;
      }
    }, 0);
  }, []);

  useEffect(() => {
    // Only scroll to bottom when a new message is added (not when content is appended)
    if (messages.length > lastMessageCount.current) {
      scrollToBottom();
    }
    lastMessageCount.current = messages.length;
  }, [messages.length, scrollToBottom]);

  // Cleanup timeout on unmount
  useEffect(
    () => () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    },
    []
  );

  return { messagesEndRef, scrollToBottom };
}
