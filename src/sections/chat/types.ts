import type { GeminiModelType } from 'src/lib/firebase';

// Chat message types
export type ChatRole = 'user' | 'assistant' | 'system';

// Image attachment types
export interface ImageAttachment {
  id: string;
  url: string;
  filename: string;
  size: number;
  mimeType: string;
  width?: number;
  height?: number;
  status: 'uploading' | 'uploaded' | 'failed';
  error?: string;
}

export interface ImageUpload {
  id: string;
  file: File;
}

// Citation types
export interface CitationReference {
  id: string;
  file: {
    id: string;
    name: string;
    displayName?: string;
    metadata: {
      audio_duration?: number;
      audioDuration?: number;
      category?: string;
      file_id?: string;
      filename?: string;
      is_transcript?: boolean;
      isTranscript?: boolean;
      original_filename?: string;
      originalFilename?: string;
      project_id?: string;
      speakers?: string[];
      transcript_source?: string;
      transcriptSource?: string;
      user_id?: string;
    };
  };
  pages: any[];
  text?: string; // Preview text with content snippet and metadata
  type?: string; // Content type (e.g., "transcript")
  speakers?: string[]; // Speakers array at reference level
  duration?: number; // Duration at reference level
  source?: string; // Source information (e.g., "revai")
  currentSpeaker?: string; // Current speaker context for some references
  timestamp?: string; // Formatted timestamp (e.g., "00:07:12")
  timestampFormatted?: string; // Same as timestamp
  timestampSeconds?: number; // Timestamp in seconds
  timestampConfidence?: 'low' | 'medium' | 'high'; // Confidence level of timestamp
}

export interface Citation {
  id: string;
  position: number;
  references: CitationReference[];
}

export interface ChatMessage {
  id: string;
  conversationId: string;
  role: ChatRole;
  content: string;
  model?: AIModel;
  createdAt: string;
  updatedAt: string;
  cost?: number;
  usage?: {
    inputTokens: number;
    outputTokens: number;
    totalTokens: number;
  };
  metadata?: {
    finishReason?: string;
    duration?: number;
    chunks?: number;
    responseLength?: number;
  };
  citations?: Citation[];
  images?: ImageAttachment[]; // Added images field
}

// New hybrid model support
export type AIModel = GeminiModelType | string; // Dynamic model IDs when multi-agents enabled

// 🎯 HYBRID APPROACH:
// • Flag OFF → Use existing 2 hardcoded Gemini models (backward compatibility)
// • Flag ON → 100% API-driven with all providers

// Legacy models (preserved for backward compatibility)
export const LEGACY_MODELS = {
  GEMINI_2_5_FLASH: 'gemini-2.5-flash',
  GEMINI_2_5_PRO: 'gemini-2.5-pro',
} as const;

export type LegacyModel = (typeof LEGACY_MODELS)[keyof typeof LEGACY_MODELS];

// Conversation types
export interface Conversation {
  id: string;
  projectId: string;
  model: AIModel;
  createdAt: Date;
  updatedAt: Date;
}

// Streaming event types
export interface StreamingProgress {
  textChunkCount: number;
  duration: number;
  current?: number;
  estimated?: number;
}

// Progress step metadata
export interface ProgressMetadata {
  complexity?: 'simple' | 'medium' | 'complex';
  estimatedTime?: number;
  fileCount?: number;
  [key: string]: any; // Allow for additional metadata fields
}

// Base event interface
export interface BaseEvent<T = any> {
  type: string;
  data: T;
}

// Connection event
export interface ConnectionEvent
  extends BaseEvent<{
    status: 'connected' | 'disconnected' | 'error';
    conversationId: string;
  }> {
  type: 'connection';
}

// Start event
export interface StartEvent
  extends BaseEvent<{
    conversationId: string;
    userMessage?: ChatMessage;
  }> {
  type: 'start';
}

// Content event
export interface ContentEvent
  extends BaseEvent<{
    text: string;
    chunkId?: string;
    progress?: StreamingProgress;
  }> {
  type: 'content';
}

// Progress event
export interface ChatProgressEvent
  extends BaseEvent<{
    step: string;
    message: string;
    timestamp: number;
    metadata: ProgressMetadata;
  }> {
  type: 'progress';
}

// Completion event
export interface CompletionEvent
  extends BaseEvent<{
    finishReason: string;
    duration: number;
    textChunks: number;
    responseLength: number;
    citations?: Citation[];
    aiResponse?: {
      conversationId: string;
      model: AIModel;
      usage?: {
        inputTokens: number;
        outputTokens: number;
        totalTokens: number;
      };
      cost?: number;
      resources?: string[];
    };
  }> {
  type: 'completion';
}

// Error event
export interface ErrorEvent
  extends BaseEvent<{
    error: string;
    message?: string;
    code?: string;
  }> {
  type: 'error';
}

// Union type for all stream events
export type StreamEvent =
  | ConnectionEvent
  | StartEvent
  | ContentEvent
  | ChatProgressEvent
  | CompletionEvent
  | ErrorEvent;

// Hook options and return types
export interface UseChatOptions {
  conversationId?: string;
  model?: AIModel; // Support both legacy and new models
  resources?: string[];
  projectId?: string;
}

export interface UseChatReturn {
  // State
  messages: ChatMessage[];
  isLoading: boolean;
  isStreaming: boolean;
  conversationId: string | null;
  currentModel: AIModel; // Support both legacy and new models
  allowSendMessage: boolean;

  // Streaming state
  error: string | null;
  currentProgress: ChatProgressEvent['data'] | null;

  // Actions
  createConversation: () => Promise<string>;
  sendMessage: (
    message: string,
    images?: ImageUpload[],
    options?: { model?: AIModel; resources?: string[] }
  ) => Promise<void>;
  switchModel: (model: AIModel) => void;
  resetChat: () => void;

  // Streaming controls
  stopStreaming: () => void;
}

// API request/response types
export interface CreateConversationRequest {
  projectId: string;
}

export interface CreateConversationResponse {
  id: string;
}

export interface CreateMessageRequest {
  conversationId: string;
}

// Chat preferences types
export interface ChatPreferences {
  enableSearchQuery: boolean;
  userInstructions: string;
}
