/**
 * Chat Adapters Utility
 *
 * This file contains utility functions for working with chat messages.
 * All legacy conversion functions have been removed as the migration
 * to the new backend API format is complete.
 */

import type { ChatMessage } from '../types';

// Re-export createTypingMessage from message-factory for convenience
export { createTypingMessage } from './message-factory';

/**
 * Validates that a message has required fields
 */
export const isValidChatMessage = (message: any): message is ChatMessage =>
  message &&
  typeof message.id === 'string' &&
  typeof message.conversationId === 'string' &&
  (message.role === 'assistant' || message.role === 'user') &&
  typeof message.content === 'string' &&
  typeof message.createdAt === 'string';

/**
 * Sorts messages by creation date
 */
export const sortMessagesByDate = (messages: ChatMessage[]): ChatMessage[] =>
  [...messages].sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());

/**
 * Gets the last message from a conversation
 */
export const getLastMessage = (messages: ChatMessage[]): ChatMessage | null => {
  if (messages.length === 0) return null;
  const sorted = sortMessagesByDate(messages);
  return sorted[sorted.length - 1];
};
