import type { StreamEvent } from '../types';

export const transformServerResponse = (eventData: any): StreamEvent | null => {
  // Handle legacy format with 'text' field
  if (eventData.text && !eventData.type) {
    return {
      type: 'content',
      data: { text: eventData.text },
    };
  }

  // Handle new structured format - check if it has a type field
  if (eventData.type) {
    return transformEventByType(eventData);
  }

  // If no type, try to infer from the data structure
  if (eventData.status) {
    return {
      type: 'connection',
      data: {
        status: eventData.status,
        conversationId: eventData.conversationId || '',
      },
    };
  }

  return null;
};

const transformEventByType = (eventData: any): StreamEvent | null => {
  switch (eventData.type) {
    case 'connection':
      return {
        type: 'connection',
        data: {
          status: eventData.status || 'connected',
          conversationId: eventData.conversationId || '',
        },
      };

    case 'start':
      return {
        type: 'start',
        data: {
          conversationId: eventData.conversationId || '',
          userMessage: eventData.userMessage,
        },
      };

    case 'content':
      return {
        type: 'content',
        data: {
          text: eventData.text || '',
          chunkId: eventData.chunkId,
          progress: eventData.progress,
        },
      };

    case 'progress':
      return {
        type: 'progress',
        data: {
          step: eventData.step || '',
          message: eventData.message || '',
          timestamp: eventData.timestamp || Date.now(),
          metadata: eventData.metadata || {},
        },
      };

    case 'completion':
      return {
        type: 'completion',
        data: {
          finishReason: eventData.finishReason || 'STOP',
          duration: eventData.duration || 0,
          textChunks: eventData.textChunks || eventData.totalChunks || 0,
          responseLength: eventData.responseLength || 0,
          citations: eventData.citations || [],
          aiResponse: eventData.aiResponse,
        },
      };

    case 'error':
      return {
        type: 'error',
        data: {
          error: eventData.error || eventData.message || 'Unknown error',
          message: eventData.message,
          code: eventData.code,
        },
      };

    default:
      return null;
  }
};
