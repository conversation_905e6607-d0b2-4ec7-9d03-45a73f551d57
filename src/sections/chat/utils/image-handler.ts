import { toast } from 'sonner';
import { uuidv4 } from 'minimal-shared/utils';

import { validateImages, IMAGE_CONSTRAINTS } from './image-validation';

import type { ImageUpload } from '../types';

// Error message mapping for better UX
const getErrorMessage = (code: string): string => {
  switch (code) {
    case 'SIZE':
      return 'File Too Large';
    case 'FORMAT':
      return 'Unsupported Format';
    case 'COUNT':
      return 'Too Many Images';
    case 'TOTAL_SIZE':
      return 'Total Size Too Large';
    default:
      return 'Upload Error';
  }
};

/**
 * Centralized image handling utility for consistent validation and error handling
 */
export class ImageHandler {
  /**
   * Process and validate new images to be added
   * @param newFiles - Array of files to add
   * @param existingImages - Currently attached images
   * @returns Validated image uploads that can be added
   */
  static async processImages(
    newFiles: File[],
    existingImages: ImageUpload[] = []
  ): Promise<ImageUpload[]> {
    if (newFiles.length === 0) return [];

    // Check if adding these files would exceed the limit
    const totalAfterAdd = existingImages.length + newFiles.length;
    if (totalAfterAdd > IMAGE_CONSTRAINTS.MAX_IMAGES_PER_MESSAGE) {
      const availableSlots = IMAGE_CONSTRAINTS.MAX_IMAGES_PER_MESSAGE - existingImages.length;
      if (availableSlots <= 0) {
        toast.error('Maximum Images Reached', {
          description: `You already have ${IMAGE_CONSTRAINTS.MAX_IMAGES_PER_MESSAGE} images attached`,
        });
        return [];
      }
      
      // Only take what we can fit
      newFiles = newFiles.slice(0, availableSlots);
      toast.warning('Image Limit', {
        description: `Only adding first ${availableSlots} image${availableSlots > 1 ? 's' : ''} due to limit`,
      });
    }

    // Create temporary image objects for validation
    const tempImages: ImageUpload[] = newFiles.map((file) => ({
      id: uuidv4(),
      file,
    }));

    // Validate all images (existing + new)
    const allImages = [...existingImages, ...tempImages];
    const errors = await validateImages(allImages);

    // Filter out errors that only relate to the new images or global constraints
    const newImageIds = new Set(tempImages.map((img) => img.id));
    const relevantErrors = errors.filter(
      (error) =>
        newImageIds.has(error.imageId) || error.code === 'COUNT' || error.code === 'TOTAL_SIZE'
    );

    if (relevantErrors.length > 0) {
      // Show toast notifications for errors
      relevantErrors.forEach((error) => {
        toast.error(getErrorMessage(error.code), {
          description: `${error.filename}: ${error.error}`,
        });
      });

      // Only add valid images
      const invalidImageIds = new Set(relevantErrors.map((err) => err.imageId));
      const validImages = tempImages.filter((img) => !invalidImageIds.has(img.id));

      if (validImages.length > 0) {
        toast.success('Valid Images Added', {
          description: `${validImages.length} out of ${newFiles.length} image${newFiles.length > 1 ? 's' : ''} added successfully`,
        });
      }

      return validImages;
    }

    // All images are valid
    return tempImages;
  }

  /**
   * Handle file drop rejection with appropriate error messages
   * @param rejectedFiles - Files rejected by dropzone
   */
  static handleDropRejection(rejectedFiles: Array<{ file: File; errors: Array<{ code: string; message: string }> }>) {
    rejectedFiles.forEach(({ file, errors }) => {
      errors.forEach((error) => {
        if (error.code === 'file-too-large') {
          toast.error('File Too Large', {
            description: `${file.name}: File size exceeds ${Math.round(IMAGE_CONSTRAINTS.MAX_FILE_SIZE / (1024 * 1024))}MB`,
          });
        } else if (error.code === 'file-invalid-type') {
          toast.error('Unsupported Format', {
            description: `${file.name}: Only image files are supported`,
          });
        } else {
          toast.error('Upload Error', {
            description: `${file.name}: ${error.message}`,
          });
        }
      });
    });
  }

  /**
   * Extract image files from clipboard data
   * @param clipboardData - Clipboard data from paste event
   * @returns Array of image files
   */
  static async extractImagesFromClipboard(
    clipboardData: DataTransfer | null
  ): Promise<File[]> {
    if (!clipboardData) return [];

    const items = Array.from(clipboardData.items || []);
    const imageItems = items.filter((item) => item.type.startsWith('image/'));

    if (imageItems.length === 0) return [];

    const imageFiles = await Promise.all(
      imageItems.map((item) => {
        const file = item.getAsFile();
        return file ? Promise.resolve(file) : Promise.resolve(null);
      })
    );

    return imageFiles.filter((file): file is File => file !== null);
  }

  /**
   * Check if clipboard has text content along with images
   * @param clipboardData - Clipboard data from paste event
   * @returns true if clipboard contains text
   */
  static clipboardHasText(clipboardData: DataTransfer | null): boolean {
    if (!clipboardData) return false;
    const items = Array.from(clipboardData.items || []);
    return items.some((item) => item.type === 'text/plain');
  }

  /**
   * Get dropzone accept configuration
   * @returns Accept configuration for dropzone
   */
  static getDropzoneAccept(): Record<string, string[]> {
    return {
      'image/*': IMAGE_CONSTRAINTS.SUPPORTED_FORMATS.map((format) =>
        format.replace('image/', '.')
      ),
    };
  }

  /**
   * Check if maximum images limit is reached
   * @param currentCount - Current number of attached images
   * @returns true if limit is reached
   */
  static isMaxImagesReached(currentCount: number): boolean {
    return currentCount >= IMAGE_CONSTRAINTS.MAX_IMAGES_PER_MESSAGE;
  }

  /**
   * Get remaining image slots
   * @param currentCount - Current number of attached images
   * @returns Number of remaining slots
   */
  static getRemainingSlots(currentCount: number): number {
    return Math.max(0, IMAGE_CONSTRAINTS.MAX_IMAGES_PER_MESSAGE - currentCount);
  }
}