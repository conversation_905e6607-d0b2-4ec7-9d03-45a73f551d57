import type { ImageUpload } from '../types';

export const IMAGE_CONSTRAINTS = {
  MAX_FILE_SIZE: 20 * 1024 * 1024, // 20MB
  MAX_IMAGES_PER_MESSAGE: 5,
  MAX_TOTAL_PAYLOAD: 100 * 1024 * 1024, // 100MB
  SUPPORTED_FORMATS: [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/bmp',
    'image/tiff',
  ],
} as const;

export interface ImageValidationError {
  imageId: string;
  filename: string;
  error: string;
  code: 'SIZE' | 'FORMAT' | 'COUNT' | 'TOTAL_SIZE';
}

export const validateImages = async (images: ImageUpload[]): Promise<ImageValidationError[]> => {
  const errors: ImageValidationError[] = [];

  // Check image count
  if (images.length > IMAGE_CONSTRAINTS.MAX_IMAGES_PER_MESSAGE) {
    errors.push({
      imageId: 'count',
      filename: 'Multiple files',
      error: `Maximum ${IMAGE_CONSTRAINTS.MAX_IMAGES_PER_MESSAGE} images allowed per message`,
      code: 'COUNT',
    });
  }

  // Check total payload size
  const totalSize = images.reduce((sum, img) => sum + img.file.size, 0);
  if (totalSize > IMAGE_CONSTRAINTS.MAX_TOTAL_PAYLOAD) {
    errors.push({
      imageId: 'total',
      filename: 'Multiple files',
      error: `Total images size exceeds ${formatFileSize(IMAGE_CONSTRAINTS.MAX_TOTAL_PAYLOAD)}`,
      code: 'TOTAL_SIZE',
    });
  }

  // Validate each image
  for (const image of images) {
    const imageErrors = await validateSingleImage(image);
    errors.push(...imageErrors);
  }

  return errors;
};

const validateSingleImage = async (image: ImageUpload): Promise<ImageValidationError[]> => {
  const errors: ImageValidationError[] = [];
  const { file, id } = image;

  // Check file size
  if (file.size > IMAGE_CONSTRAINTS.MAX_FILE_SIZE) {
    errors.push({
      imageId: id,
      filename: file.name,
      error: `File size exceeds ${formatFileSize(IMAGE_CONSTRAINTS.MAX_FILE_SIZE)}`,
      code: 'SIZE',
    });
  }

  // Check file format
  if (!IMAGE_CONSTRAINTS.SUPPORTED_FORMATS.includes(file.type as any)) {
    errors.push({
      imageId: id,
      filename: file.name,
      error: `Unsupported format. Supported: ${IMAGE_CONSTRAINTS.SUPPORTED_FORMATS.join(', ')}`,
      code: 'FORMAT',
    });
  }

  return errors;
};

export const fileToBase64 = (file: File): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const base64 = reader.result as string;
      // Remove data URL prefix (e.g., "data:image/jpeg;base64,")
      const base64Data = base64.split(',')[1];
      resolve(base64Data);
    };
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};
