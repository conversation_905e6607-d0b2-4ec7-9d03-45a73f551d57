import type { Chat<PERSON><PERSON>, ChatMessage } from 'src/types';

import { uuidv4 } from 'minimal-shared/utils';

import type { AIModel, ImageAttachment } from '../types';

export interface CreateMessageOptions {
  id?: string;
  content?: string;
  role: ChatRole;
  conversationId: string;
  model?: AIModel;
  images?: ImageAttachment[];
  createdAt?: string;
  updatedAt?: string;
}

export const createMessage = (options: CreateMessageOptions): ChatMessage => {
  const now = new Date().toISOString();

  return {
    id: options.id || uuidv4(),
    conversationId: options.conversationId,
    role: options.role,
    content: options.content || '',
    model: options.model,
    images: options.images,
    createdAt: options.createdAt || now,
    updatedAt: options.updatedAt || now,
  };
};

// Convenience function for creating typing indicators
export const createTypingMessage = (conversationId: string): ChatMessage =>
  createMessage({
    id: 'typing-indicator',
    role: 'assistant',
    conversationId,
  });
