import type { StreamEvent } from 'src/types';

import { transformServerResponse } from './chat-event-transformer';

export const parseSSEStream = (chunk: string): StreamEvent[] => {
  const lines = chunk.split('\n');
  const events: StreamEvent[] = [];

  for (const line of lines) {
    if (line.startsWith('data: ')) {
      const dataContent = line.slice(6).trim();

      if (dataContent === '[DONE]') {
        continue;
      }

      try {
        const eventData = JSON.parse(dataContent);
        const transformedEvent = transformServerResponse(eventData);
        if (transformedEvent) {
          events.push(transformedEvent);
        }
      } catch (parseError) {
        console.warn('Failed to parse SSE event:', line, parseError);
      }
    } else if (line.trim() && !line.startsWith('event:') && !line.startsWith('id:')) {
      try {
        const eventData = JSON.parse(line);
        const transformedEvent = transformServerResponse(eventData);
        if (transformedEvent) {
          events.push(transformedEvent);
        }
      } catch (parseError) {
        console.warn('Failed to parse direct JSON:', line, parseError);
      }
    }
  }

  return events;
};
