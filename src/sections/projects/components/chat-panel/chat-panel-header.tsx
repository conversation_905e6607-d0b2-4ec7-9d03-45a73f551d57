import type { AIModel, LegacyModel } from 'src/sections/chat/types';

import { Stack, Typography, IconButton } from '@mui/material';

import useFeatureFlags from 'src/hooks/feature-flags';

import { Iconify } from 'src/components/iconify';

import { AppFeatures } from 'src/types/features';

import { ModelSwitch } from './model-switch';
import { ModelSelector } from './model-selector';

interface ChatPanelHeaderProps {
  selectedModel: AIModel; // Support both legacy and new types
  onModelChange: (model: AIModel) => void;
  onNewChat?: () => void;
  disabled?: boolean;
  hasMessages?: boolean;
}

const ChatPanelHeader: React.FC<ChatPanelHeaderProps> = ({
  selectedModel,
  onModelChange,
  onNewChat,
  disabled = false,
  hasMessages = false,
}) => {
  const { isFlagEnabled } = useFeatureFlags();
  const isMultiAgentsEnabled = isFlagEnabled(AppFeatures.MULTI_MODELS);

  return (
    <Stack
      direction="row"
      alignItems="center"
      justifyContent="space-between"
      sx={{
        mb: 2,
      }}
    >
      <Stack direction="row" alignItems="center" gap={2}>
        <Typography variant="subtitle2">Chat</Typography>

        {!isMultiAgentsEnabled ? (
          <ModelSwitch
            selectedModel={selectedModel as LegacyModel}
            onModelChange={onModelChange as (model: LegacyModel) => void}
            disabled={disabled}
          />
        ) : (
          /* 🚀 MULTI-AGENT MODE: New dropdown for all providers */
          <ModelSelector
            selectedModel={selectedModel as AIModel}
            onModelChange={onModelChange as (model: AIModel) => void}
            disabled={disabled}
          />
        )}
      </Stack>

      {onNewChat && hasMessages && (
        <IconButton onClick={onNewChat} disabled={disabled} size="small">
          <Iconify icon="material-symbols:add-comment-rounded" />
        </IconButton>
      )}
    </Stack>
  );
};

export default ChatPanelHeader;
