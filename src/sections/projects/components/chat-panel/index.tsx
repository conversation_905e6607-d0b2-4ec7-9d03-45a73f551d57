import type { Resource } from 'src/types';
import type { SxProps } from '@mui/material';
import type { AIModel } from 'src/sections/chat/types';
import type { GeminiModelType } from 'src/lib/firebase';

import { useRef, useMemo, useState, useEffect, useCallback } from 'react';

import { Paper } from '@mui/material';

import useFeatureFlags from 'src/hooks/feature-flags';
import { useAvailableModels } from 'src/hooks/use-available-models';

import { LEGACY_MODELS } from 'src/sections/chat/types';

import { AppFeatures } from 'src/types/features';

import ProjectActions from '../project-actions';
import ChatPanelHeader from './chat-panel-header';
import useChat from '../../../chat/hooks/use-chat';
import { ChatBox, type ChatBoxRef } from '../../../chat/components/chat-box';

interface ChatPanelProps {
  selectedFiles: Resource[];
  projectId: string;
  canEdit?: boolean;
  sx?: SxProps;
}

const QUICK_REPLY_OPTIONS = ['Summarise', 'Discuss', 'Evaluate'];

const ChatPanel: React.FC<ChatPanelProps> = ({
  selectedFiles,
  projectId,
  canEdit = true,
  sx = {},
}) => {
  const { isFlagEnabled } = useFeatureFlags();
  const isMultiModelsEnabled = isFlagEnabled(AppFeatures.MULTI_MODELS);
  const { models, loading, fallbackToLegacy } = useAvailableModels();

  // 🎯 HYBRID MODEL SELECTION STRATEGY
  const getDefaultModel = useCallback((): AIModel => {
    if (!isMultiModelsEnabled || fallbackToLegacy) {
      return LEGACY_MODELS.GEMINI_2_5_FLASH;
    }

    // 🚀 MULTI-AGENT MODE: 100% API-driven
    if (models.length === 0) return ''; // Wait for API response

    // Prefer balanced models (GPT-4o-mini or similar)
    const balancedModel = models.find((m) => m.tier === 'balanced' || m.id.includes('4o-mini'));
    if (balancedModel) return balancedModel.id;

    // Fallback to first available model
    return models[0].id;
  }, [isMultiModelsEnabled, fallbackToLegacy, models]);

  const [selectedModel, setSelectedModel] = useState<AIModel>(
    isMultiModelsEnabled && !fallbackToLegacy ? '' : LEGACY_MODELS.GEMINI_2_5_FLASH
  );
  const chatBoxRef = useRef<ChatBoxRef>(null);

  // Initialize model selection when API data loads or flag changes
  useEffect(() => {
    if (
      isMultiModelsEnabled &&
      !fallbackToLegacy &&
      !loading &&
      models.length > 0 &&
      !selectedModel
    ) {
      setSelectedModel(getDefaultModel());
    } else if (
      (!isMultiModelsEnabled || fallbackToLegacy) &&
      selectedModel !== LEGACY_MODELS.GEMINI_2_5_FLASH
    ) {
      setSelectedModel(LEGACY_MODELS.GEMINI_2_5_FLASH);
    }
  }, [isMultiModelsEnabled, fallbackToLegacy, models, loading, selectedModel, getDefaultModel]);

  // Get resource IDs for the new backend API
  const resourceIds = useMemo(() => selectedFiles.map((file) => file.id), [selectedFiles]);

  const {
    messages,
    isLoading,
    isStreaming,
    sendMessage,
    stopStreaming,
    switchModel,
    resetChat,
    allowSendMessage,
    conversationId,
    currentProgress,
  } = useChat({
    model: selectedModel,
    resources: resourceIds,
    projectId,
  });

  const prevProjectIdRef = useRef<string>(projectId);

  // Handle model switching
  const handleModelChange = useCallback(
    async (newModel: AIModel) => {
      setSelectedModel(newModel);
      // For legacy mode or fallback, ensure we pass the correct GeminiModelType
      if (!isMultiModelsEnabled || fallbackToLegacy) {
        await switchModel(newModel as GeminiModelType);
      } else {
        // For multi-agent mode, pass the model as AIModel
        await switchModel(newModel as AIModel);
      }
    },
    [switchModel, isMultiModelsEnabled, fallbackToLegacy]
  );

  // Reset conversation when project changes
  useEffect(() => {
    if (prevProjectIdRef.current !== projectId) {
      // Reset chat when switching projects
      resetChat();
      prevProjectIdRef.current = projectId;
    }
  }, [projectId, resetChat]);

  const handleNewChat = useCallback(() => {
    // Use the ref to trigger the dialog in ChatBox
    chatBoxRef.current?.openNewChatDialog();
  }, []);

  return (
    <Paper
      elevation={1}
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        p: 2,
        width: {
          xs: '100%',
          md: '50%',
        },
        overflow: 'hidden',
        ...sx,
      }}
    >
      <ChatPanelHeader
        selectedModel={selectedModel}
        onModelChange={handleModelChange}
        onNewChat={handleNewChat}
        disabled={isLoading || isStreaming}
        hasMessages={messages.length > 0}
      />

      <ChatBox
        ref={chatBoxRef}
        containerSx={{
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
          minHeight: 0,
        }}
        messageListSx={{
          flex: 1,
          minHeight: 0,
          pt: 2,
          px: 0,
          pr: 1,
        }}
        inputSx={{
          flexShrink: 0,
        }}
        messages={messages}
        loading={isLoading}
        isReplying={isStreaming}
        disabled={!allowSendMessage}
        sendMessage={sendMessage}
        stopConversation={stopStreaming}
        quickReplyOptions={QUICK_REPLY_OPTIONS}
        actions={<ProjectActions projectId={projectId} canEdit={canEdit} />}
        conversationId={conversationId}
        onResetChat={resetChat}
        currentProgress={currentProgress}
      />
    </Paper>
  );
};

export default ChatPanel;
