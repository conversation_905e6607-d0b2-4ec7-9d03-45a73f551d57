import type { Resource } from 'src/types';
import type { SxProps } from '@mui/material';
import type { AIModel } from 'src/sections/chat/types';

import { useRef, useMemo, useState, useEffect, useCallback } from 'react';

import { Paper } from '@mui/material';

import useFeatureFlags from 'src/hooks/feature-flags';
import { useAvailableModels } from 'src/hooks/use-available-models';

import { LEGACY_MODELS } from 'src/sections/chat/types';

import { AppFeatures } from 'src/types/features';

import ProjectActions from '../project-actions';
import ChatPanelHeader from './chat-panel-header';
import useChat from '../../../chat/hooks/use-chat';
import { ChatBox, type ChatBoxRef } from '../../../chat/components/chat-box';

interface ChatPanelProps {
  selectedFiles: Resource[];
  projectId: string;
  canEdit?: boolean;
  sx?: SxProps;
}

const QUICK_REPLY_OPTIONS = ['Summarise', 'Discuss', 'Evaluate'];

const ChatPanel: React.FC<ChatPanelProps> = ({
  selectedFiles,
  projectId,
  canEdit = true,
  sx = {},
}) => {
  const { isFlagEnabled } = useFeatureFlags();
  const isMultiModelsEnabled = isFlagEnabled(AppFeatures.MULTI_MODELS);
  const { models, loading, fallbackToLegacy } = useAvailableModels();

  const [selectedModel, setSelectedModel] = useState<AIModel>(LEGACY_MODELS.GEMINI_2_5_FLASH);
  const chatBoxRef = useRef<ChatBoxRef>(null);

  // Calculate the optimal model based on current state
  const optimalModel = useMemo((): AIModel => {
    // Use legacy model if multi-models is disabled or we need to fallback
    if (!isMultiModelsEnabled || fallbackToLegacy) {
      return LEGACY_MODELS.GEMINI_2_5_FLASH;
    }

    // Use legacy model while loading or if no models available
    if (loading || models.length === 0) {
      return LEGACY_MODELS.GEMINI_2_5_FLASH;
    }

    // Use first available model from API
    return models[0].id;
  }, [isMultiModelsEnabled, fallbackToLegacy, loading, models]);

  // Only sync when optimal model changes due to external factors (not user selection)
  const prevOptimalModelRef = useRef<AIModel>(optimalModel);
  useEffect(() => {
    // Only update if the optimal model changed (not the selected model)
    if (prevOptimalModelRef.current !== optimalModel) {
      setSelectedModel(optimalModel);
      prevOptimalModelRef.current = optimalModel;
    }
  }, [optimalModel]);

  // Get resource IDs for the new backend API
  const resourceIds = useMemo(() => selectedFiles.map((file) => file.id), [selectedFiles]);

  const {
    messages,
    isLoading,
    isStreaming,
    sendMessage,
    stopStreaming,
    switchModel,
    resetChat,
    allowSendMessage,
    conversationId,
    currentProgress,
  } = useChat({
    model: selectedModel,
    resources: resourceIds,
    projectId,
  });

  const prevProjectIdRef = useRef<string>(projectId);

  // Handle model switching
  const handleModelChange = useCallback(
    (newModel: AIModel) => {
      setSelectedModel(newModel);
      switchModel(newModel);
    },
    [switchModel]
  );

  // Reset conversation when project changes
  useEffect(() => {
    if (prevProjectIdRef.current !== projectId) {
      // Reset chat when switching projects
      resetChat();
      prevProjectIdRef.current = projectId;
    }
  }, [projectId, resetChat]);

  const handleNewChat = useCallback(() => {
    // Use the ref to trigger the dialog in ChatBox
    chatBoxRef.current?.openNewChatDialog();
  }, []);

  return (
    <Paper
      elevation={1}
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        p: 2,
        width: {
          xs: '100%',
          md: '50%',
        },
        overflow: 'hidden',
        ...sx,
      }}
    >
      <ChatPanelHeader
        selectedModel={selectedModel}
        onModelChange={handleModelChange}
        onNewChat={handleNewChat}
        disabled={isLoading || isStreaming}
        hasMessages={messages.length > 0}
      />

      <ChatBox
        ref={chatBoxRef}
        containerSx={{
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
          minHeight: 0,
        }}
        messageListSx={{
          flex: 1,
          minHeight: 0,
          pt: 2,
          px: 0,
          pr: 1,
        }}
        inputSx={{
          flexShrink: 0,
        }}
        messages={messages}
        loading={isLoading}
        isReplying={isStreaming}
        disabled={!allowSendMessage}
        sendMessage={sendMessage}
        stopConversation={stopStreaming}
        quickReplyOptions={QUICK_REPLY_OPTIONS}
        actions={<ProjectActions projectId={projectId} canEdit={canEdit} />}
        conversationId={conversationId}
        onResetChat={resetChat}
        currentProgress={currentProgress}
      />
    </Paper>
  );
};

export default ChatPanel;
