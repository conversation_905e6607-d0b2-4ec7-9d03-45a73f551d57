import type { AIModel } from 'src/sections/chat/types';

import { useState } from 'react';

import Menu from '@mui/material/Menu';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import MenuItem from '@mui/material/MenuItem';
import Typography from '@mui/material/Typography';
import ErrorIcon from '@mui/icons-material/Error';
import ListItemText from '@mui/material/ListItemText';
import CircularProgress from '@mui/material/CircularProgress';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';

import { useAvailableModels } from 'src/hooks/use-available-models';

interface ModelSelectorProps {
  selectedModel: AIModel;
  onModelChange: (model: AIModel) => void;
  disabled?: boolean;
}

export const ModelSelector: React.FC<ModelSelectorProps> = ({
  selectedModel,
  onModelChange,
  disabled = false,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const { models, loading, error, fallbackToLegacy, getModelById } = useAvailableModels();

  const currentModelInfo = getModelById(selectedModel);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleModelSelect = (modelId: string) => {
    onModelChange(modelId as AIModel);
    handleClose();
  };

  // Loading state
  if (loading) {
    return (
      <Button variant="outlined" size="small" disabled sx={{ minWidth: 140 }}>
        <CircularProgress size={16} sx={{ mr: 1 }} />
        Loading...
      </Button>
    );
  }

  // Error state with fallback indication
  if (error) {
    return (
      <Button variant="outlined" size="small" disabled color="error" sx={{ minWidth: 140 }}>
        <ErrorIcon sx={{ mr: 1 }} fontSize="small" />
        {fallbackToLegacy ? 'API Error' : 'Error'}
      </Button>
    );
  }

  return (
    <>
      <Button
        variant="outlined"
        size="small"
        onClick={handleClick}
        disabled={disabled || !currentModelInfo}
        endIcon={<KeyboardArrowDownIcon />}
        sx={{ minWidth: 140 }}
      >
        <Stack direction="row" alignItems="center" spacing={1}>
          <Typography variant="caption">{currentModelInfo?.name || selectedModel}</Typography>
        </Stack>
      </Button>

      <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={handleClose}>
        {models.map((model) => (
          <MenuItem
            key={model.id}
            onClick={() => handleModelSelect(model.id)}
            selected={model.id === selectedModel}
          >
            <ListItemText primary={model.name} secondary={model.description} />
          </MenuItem>
        ))}
      </Menu>
    </>
  );
};
