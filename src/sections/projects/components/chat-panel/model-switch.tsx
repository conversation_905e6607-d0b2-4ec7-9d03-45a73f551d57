import type { LegacyModel } from 'src/sections/chat/types';

import Switch from '@mui/material/Switch';
import Tooltip from '@mui/material/Tooltip';
import FormControlLabel from '@mui/material/FormControlLabel';

import { LEGACY_MODELS } from 'src/sections/chat/types';

interface Props {
  selectedModel: LegacyModel;
  onModelChange: (model: LegacyModel) => void;
  disabled?: boolean;
}

export const ModelSwitch: React.FC<Props> = ({
  selectedModel,
  onModelChange,
  disabled = false,
}) => {
  // Use hardcoded values exactly as current implementation
  const isGeminiPro = selectedModel === LEGACY_MODELS.GEMINI_2_5_PRO;

  const handleSwitchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newModel: LegacyModel = event.target.checked
      ? LEGACY_MODELS.GEMINI_2_5_PRO
      : LEGACY_MODELS.GEMINI_2_5_FLASH;
    onModelChange(newModel);
  };

  return (
    <Tooltip title="Advanced mode" arrow placement="top">
      <FormControlLabel
        label=""
        control={
          <Switch
            checked={isGeminiPro}
            onChange={handleSwitchChange}
            size="small"
            disabled={disabled}
          />
        }
        sx={{ m: 0 }}
      />
    </Tooltip>
  );
};
