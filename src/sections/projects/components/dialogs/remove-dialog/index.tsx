import type { Project } from 'src/types/project';

import { toast } from 'sonner';

import LoadingButton from '@mui/lab/LoadingButton';
import {
  Dialog,
  Button,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
} from '@mui/material';

import { useDeleteProjectMutation } from 'src/store/api/projects';

interface Props {
  open: boolean;
  onClose: () => void;
  project: Pick<Project, 'id' | 'name'>;
}

const RemoveProjectDialog: React.FC<Props> = ({ open, onClose, project }) => {
  const [triggerDeleteProject, { isLoading }] = useDeleteProjectMutation();

  const handleDelete = async () => {
    try {
      await triggerDeleteProject({ id: project.id }).unwrap();
      onClose();
    } catch (error) {
      toast.error('Failed to delete project');
    }
  };

  return (
    <Dialog open={open} onClose={onClose} onClick={(evt) => evt.stopPropagation()}>
      <DialogTitle>Delete Project</DialogTitle>
      <DialogContent>
        <DialogContentText>
          Are you sure you want to delete &quot;{project.name}&quot;? This action cannot be undone.
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <LoadingButton
          onClick={handleDelete}
          color="error"
          variant="contained"
          loading={isLoading}
          disabled={isLoading}
        >
          Delete
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
};

export default RemoveProjectDialog;
