
import { Tooltip, IconButton } from '@mui/material';

import { Iconify } from 'src/components/iconify';

interface RefreshIconProps {
  onRefetch: () => void;
  shouldAnimateRefetch?: boolean;
  size?: 'small' | 'medium';
  sx?: object;
}

const RefreshIcon = ({ 
  onRefetch, 
  shouldAnimateRefetch = false, 
  size = 'medium',
  sx = {} 
}: RefreshIconProps) => {
  const iconSize = size === 'small' ? '0.9rem' : '1.1rem';
  const buttonPadding = size === 'small' ? 0.5 : undefined;

  // Dynamic tooltip based on state
  const tooltipText = shouldAnimateRefetch ? 'New file detected' : 'Refresh files';

  return (
    <Tooltip title={tooltipText} placement="top">
      <IconButton 
        size="small" 
        onClick={onRefetch} 
        sx={{ 
          p: buttonPadding,
          borderRadius: 2,
          ...sx 
        }}
      >
        <Iconify
          icon="material-symbols:refresh"
          sx={{
            color: (theme) => shouldAnimateRefetch? "success.main" : 'text.secondary',
            fontSize: iconSize,
            borderRadius: 2,
            transition: 'all 0.2s ease-in-out',
            '&:hover': {
              color: !shouldAnimateRefetch ? 'primary.main' : null,
              transform: 'rotate(180deg)',
            },
          }}
        />
      </IconButton>
    </Tooltip>
  );
};

export default RefreshIcon; 