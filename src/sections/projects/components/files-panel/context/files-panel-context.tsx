import type { ReactNode } from 'react';

import { useState, useContext, useCallback, createContext } from 'react';

interface FileExpansionState {
  fileId: string;
  isExpanded: boolean;
  seekTime?: number;
  shouldAutoPlay?: boolean;
}

interface FilesPanelContextValue {
  expansionState: FileExpansionState | null;
  expandFile: (fileId: string, seekTime?: number, shouldAutoPlay?: boolean) => void;
  updateSeekTime: (fileId: string, seekTime?: number, shouldAutoPlay?: boolean) => void;
  collapseFile: (fileId: string) => void;
  clearExpansionState: () => void;
}

const FilesPanelContext = createContext<FilesPanelContextValue | null>(null);

interface FilesPanelProviderProps {
  children: ReactNode;
}

export const FilesPanelProvider = ({ children }: FilesPanelProviderProps) => {
  const [expansionState, setExpansionState] = useState<FileExpansionState | null>(null);

  const expandFile = useCallback((fileId: string, seekTime?: number, shouldAutoPlay?: boolean) => {
    setExpansionState({
      fileId,
      isExpanded: true,
      seekTime,
      shouldAutoPlay,
    });
  }, []);

  const updateSeekTime = useCallback((fileId: string, seekTime?: number, shouldAutoPlay?: boolean) => {
    setExpansionState((prev) => {
      if (prev?.fileId === fileId && prev.isExpanded) {
        return {
          ...prev,
          seekTime,
          shouldAutoPlay,
        };
      }
      return prev;
    });
  }, []);

  const collapseFile = useCallback((fileId: string) => {
    setExpansionState((prev) => {
      if (prev?.fileId === fileId) {
        return null;
      }
      return prev;
    });
  }, []);

  const clearExpansionState = useCallback(() => {
    setExpansionState(null);
  }, []);

  const value: FilesPanelContextValue = {
    expansionState,
    expandFile,
    updateSeekTime,
    collapseFile,
    clearExpansionState,
  };

  return (
    <FilesPanelContext.Provider value={value}>
      {children}
    </FilesPanelContext.Provider>
  );
};

export const useFilesPanelContext = () => {
  const context = useContext(FilesPanelContext);
  if (!context) {
    throw new Error('useFilesPanelContext must be used within a FilesPanelProvider');
  }
  return context;
}; 