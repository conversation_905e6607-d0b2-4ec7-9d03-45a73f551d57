import type { Resource } from 'src/types';
import type { SxProps } from '@mui/material';
import type { ResourceItem } from 'src/sections/resources/components/resources-list';

import { memo, useMemo, useState, useEffect, useCallback } from 'react';

import VideocamIcon from '@mui/icons-material/Videocam';
import { Box, Card, Stack, Collapse, Checkbox, Typography } from '@mui/material';
import InsertDriveFileRoundedIcon from '@mui/icons-material/InsertDriveFileRounded';

import { hasFileExtension, getDisplayFileName } from 'src/utils/file-utils';

import { TranscriptStatus } from 'src/types';

import { Editor } from 'src/components/editor';
import { Iconify } from 'src/components/iconify';
import { fileFormat } from 'src/components/file-thumbnail';
import TruncateTypography from 'src/components/truncate-typography';

import ResourceActions from 'src/sections/resources/components/resource-actions';
import useCheckResourceStatus from 'src/sections/resources/hooks/check-resource-status';
import ResourceThumbnail from 'src/sections/resources/components/resource-card/components/resource-thumbnail';
import ResourcePreviewer from 'src/sections/resources/components/resource-card/components/resource-previewer';
import { useFilePreviewer } from 'src/sections/resources/components/resource-card/components/file-previewer-modal/file-previewer-provider';

import { useFilesPanelContext } from '../context/files-panel-context';

interface FileCardEnhancedProps {
  file: ResourceItem;
  sx?: SxProps;
  onSelect: (file: Resource, isSelected: boolean) => void;
  isSelected: boolean;
  onItemDeleted?: () => void;
}

const FileCardEnhanced = memo<FileCardEnhancedProps>(({ file, sx, onSelect, isSelected, onItemDeleted }) => {
  const [expanded, setExpanded] = useState(false);
  const [showEditor, setShowEditor] = useState(false);
  const [editorContent, setEditorContent] = useState<string>('');
  const [isLoadingContent, setIsLoadingContent] = useState(false);
  const [seekTime, setSeekTime] = useState<number | undefined>(undefined);
  const [shouldAutoPlay, setShouldAutoPlay] = useState<boolean>(false);
  
  const { openPreview } = useFilePreviewer();
  const { expansionState, clearExpansionState } = useFilesPanelContext();

  useCheckResourceStatus(file.id);

  // Memoize expensive computations
  const fileInfo = useMemo(() => {
    const fFormat = fileFormat(file.fileName || file.name || '');
    const isAudioOrVideo = fFormat === 'audio' || fFormat === 'video';
    const isFileReady = isAudioOrVideo
      ? file.transcriptionJobStatus === TranscriptStatus.Completed
      : true;
    const isMarkdownFile = hasFileExtension(file.name, 'md');
    const isTextFile = hasFileExtension(file.name, 'txt');
    const canShowInEditor = isMarkdownFile || isTextFile;

    return {
      format: fFormat,
      isAudioOrVideo,
      isFileReady,
      canShowInEditor,
    };
  }, [file.fileName, file.name, file.transcriptionJobStatus]);

  // Handle external expansion from context
  useEffect(() => {
    if (expansionState && expansionState.fileId === file.id) {
      if (expansionState.isExpanded && !expanded) {
        // New expansion
        setExpanded(true);
        setSeekTime(expansionState.seekTime);
        setShouldAutoPlay(expansionState.shouldAutoPlay || false);
        
        // Load content for supported files
        if (fileInfo.canShowInEditor) {
          fetchFileContent();
        }
      } else if (expansionState.isExpanded && expanded) {
        // File is already expanded, update seek time
        setSeekTime(expansionState.seekTime);
        setShouldAutoPlay(expansionState.shouldAutoPlay || false);
      }
    } else if (expansionState && expansionState.fileId !== file.id && expanded) {
      // Collapse this file if another file is being expanded
      setExpanded(false);
      setShowEditor(false);
      setEditorContent('');
      setIsLoadingContent(false);
      setSeekTime(undefined);
      setShouldAutoPlay(false);
    }
  }, [expansionState, file.id, expanded, fileInfo.canShowInEditor]);

  const fetchFileContent = useCallback(async () => {
    if (!file.url) return;
    
    try {
      setIsLoadingContent(true);
      const response = await fetch(file.url);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch content: ${response.status} ${response.statusText}`);
      }
      
      const contentType = response.headers.get('content-type');
      if (contentType && !contentType.includes('text/') && !contentType.includes('application/octet-stream')) {
        throw new Error('File does not appear to be a text file');
      }
      
      const text = await response.text();
      setEditorContent(text);
      setShowEditor(true);
    } catch (err) {
      console.error('Failed to fetch file content:', err);
      // Fallback to regular preview
      if (file.url && !fileInfo.isAudioOrVideo) {
        openPreview(file);
      }
    } finally {
      setIsLoadingContent(false);
    }
  }, [file.url, fileInfo.isAudioOrVideo, openPreview]);

  const handleExpandClick = useCallback(() => {
    if (expanded) {
      // When collapsing, reset the editor state
      setShowEditor(false);
      setEditorContent('');
      setIsLoadingContent(false);
      setSeekTime(undefined);
      setShouldAutoPlay(false);
      clearExpansionState();
    } else {
      // When expanding, immediately load content for supported files
      if (fileInfo.canShowInEditor) {
        fetchFileContent();
      }
    }
    setExpanded(!expanded);
  }, [expanded, fileInfo.canShowInEditor, fetchFileContent, clearExpansionState]);

  const handleCheckboxChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      event.stopPropagation();
      onSelect(file, event.target.checked);
    },
    [file, onSelect]
  );

  const handleActionsClick = useCallback((event: React.MouseEvent) => {
    event.stopPropagation();
  }, []);

  // Memoize display name
  const displayName = useMemo(() => getDisplayFileName(file.name), [file.name]);

  return (
    <Card
      sx={{
        width: '100%',
        bgcolor: 'background.paper',
        borderRadius: 1,
        border: '1px solid',
        borderColor: 'divider',
        boxShadow: 'none',
        ...sx,
      }}
    >
      <Stack
        direction="row"
        alignItems="center"
        spacing={2}
        onClick={handleExpandClick}
        sx={{
          p: 1,
          cursor: 'pointer',
          transition: 'all 0.2s ease',
          borderBottom: expanded ? '1px solid' : 'none',
          borderColor: 'divider',
          bgcolor: expanded ? 'background.neutral' : 'transparent',
          '&:hover': {
            bgcolor: expanded ? 'action.hover' : 'action.hover',
          },
        }}
      >
        {fileInfo.isAudioOrVideo ? (
          <VideocamIcon color="action" sx={{ flexShrink: 0 }} />
        ) : (
          <InsertDriveFileRoundedIcon color="action" sx={{ flexShrink: 0 }} />
        )}

        <Stack sx={{ flexGrow: 1, minWidth: 0 }}>
          <TruncateTypography
            text={displayName}
            variant="subtitle1"
            fontSize={14}
            sx={{
              whiteSpace: 'nowrap',
            }}
            maxLength={expanded ? undefined : 20}
          />

        </Stack>

        <Stack direction="row" alignItems="center" sx={{ flexShrink: 0 }}>
          {fileInfo.isFileReady && (
            <Checkbox 
              checked={isSelected} 
              onChange={handleCheckboxChange} 
              size="small"
              onClick={(e) => e.stopPropagation()}
            />
          )}
          <Iconify
            icon={
              expanded
                ? 'material-symbols:collapse-content-rounded'
                : 'material-symbols:expand-content-rounded'
            }
            sx={{
              transition: 'transform 0.2s',
              color: 'action.active',
              mr: 1,
            }}
          />
          <Box onClick={handleActionsClick}>
            <ResourceActions 
              resource={file} 
              hiddenActions={['select']} 
              onItemDeleted={onItemDeleted}
            />
          </Box>
        </Stack>
      </Stack>

      <Collapse in={expanded} timeout="auto" unmountOnExit>
        {showEditor ? (
          <Box sx={{ 
            pt: 0,
          }}>
            <Editor
              value={editorContent}
              editable={false}
              toolbarVisible={false}
              placeholder=""
              sx={{
                minHeight: 200,
                maxHeight: 400,
                overflow: 'auto',
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 0,
                bgcolor: 'background.paper',
              }}
            />
          </Box>
        ) : (
          <Box 
            sx={{ 
              pt: 0, 
              borderTop: '1px solid',
              borderColor: 'divider',
              bgcolor: 'background.neutral',
            }}
          >
            {isLoadingContent ? (
              <Box sx={{ 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center', 
                minHeight: expanded ? 200 : 100,
                color: 'text.secondary',
                flexDirection: 'column',
                gap: 1,
              }}>
                <Iconify icon="eos-icons:loading" sx={{ fontSize: 24, animation: 'spin 1s linear infinite' }} />
                <Typography variant="body2">Loading content...</Typography>
              </Box>
            ) : fileInfo.isAudioOrVideo ? (
              <ResourcePreviewer 
                data={file} 
                enableLazyLoading={false}
                seekTime={seekTime}
                shouldAutoPlay={shouldAutoPlay}
              />
            ) : (
              <ResourceThumbnail data={file} enableLazyLoading={false} />
            )}
          </Box>
        )}
      </Collapse>
    </Card>
  );
});

// Add display name for debugging
FileCardEnhanced.displayName = 'FileCardEnhanced';

export default FileCardEnhanced; 