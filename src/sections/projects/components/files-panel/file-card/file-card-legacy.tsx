import type { Resource } from 'src/types';
import type { SxProps } from '@mui/material';
import type { ResourceItem } from 'src/sections/resources/components/resources-list';

import { format } from 'date-fns';
import { memo, useMemo, useState, useCallback } from 'react';

import VideocamIcon from '@mui/icons-material/Videocam';
import { Box, Card, Stack, Collapse, Checkbox, Typography } from '@mui/material';
import InsertDriveFileRoundedIcon from '@mui/icons-material/InsertDriveFileRounded';

import { getDisplayFileName } from 'src/utils/file-utils';

import { TranscriptStatus } from 'src/types';

import { Iconify } from 'src/components/iconify';
import { fileFormat } from 'src/components/file-thumbnail';
import TranscodingStatus from 'src/components/transcoding-status';
import TruncateTypography from 'src/components/truncate-typography';

import { getTranscriptionStatusDisplay } from 'src/sections/resources/utils';
import ResourceActions from 'src/sections/resources/components/resource-actions';
import useCheckResourceStatus from 'src/sections/resources/hooks/check-resource-status';
import ResourceThumbnail from 'src/sections/resources/components/resource-card/components/resource-thumbnail';
import ResourcePreviewer from 'src/sections/resources/components/resource-card/components/resource-previewer';
import { useFilePreviewer } from 'src/sections/resources/components/resource-card/components/file-previewer-modal/file-previewer-provider';

interface FileCardLegacyProps {
  file: ResourceItem;
  sx?: SxProps;
  onSelect: (file: Resource, isSelected: boolean) => void;
  isSelected: boolean;
  onItemDeleted?: () => void;
}

const FileCardLegacy = memo<FileCardLegacyProps>(({ file, sx, onSelect, isSelected, onItemDeleted }) => {
  const [expanded, setExpanded] = useState(false);
  const { openPreview } = useFilePreviewer();

  useCheckResourceStatus(file.id);

  const handleExpandClick = useCallback(() => {
    setExpanded(!expanded);
  }, [expanded]);

  const handleCheckboxChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      event.stopPropagation();
      onSelect(file, event.target.checked);
    },
    [file, onSelect]
  );

  const handlePreviewClick = useCallback(() => {
    if (file.url && !fileInfo.isAudioOrVideo) {
      openPreview(file);
    }
  }, [file, openPreview]);

  const handleActionsClick = useCallback((event: React.MouseEvent) => {
    event.stopPropagation();
  }, []);

  // Memoize expensive computations
  const fileInfo = useMemo(() => {
    const fFormat = fileFormat(file.fileName || file.name || '');
    const isAudioOrVideo = fFormat === 'audio' || fFormat === 'video';
    const isFileReady = isAudioOrVideo
      ? file.transcriptionJobStatus === TranscriptStatus.Completed
      : true;

    return {
      format: fFormat,
      isAudioOrVideo,
      isFileReady,
    };
  }, [file.fileName, file.name, file.transcriptionJobStatus]);

  // Memoize formatted date
  const formattedDate = useMemo(
    () => format(file.fileLastModified, 'dd MMM yyyy, hh:mm a'),
    [file.fileLastModified]
  );

  // Memoize display name
  const displayName = useMemo(() => getDisplayFileName(file.name), [file.name]);

  // Memoize transcription status display
  const transcriptionStatusDisplay = useMemo(
    () => getTranscriptionStatusDisplay(file.transcriptionJobStatus),
    [file.transcriptionJobStatus]
  );

  return (
    <Card
      sx={{
        width: '100%',
        bgcolor: 'background.paper',
        borderRadius: 1,
        border: '1px solid',
        borderColor: 'divider',
        boxShadow: 'none',
        ...sx,
      }}
    >
      <Stack
        direction="row"
        alignItems="center"
        spacing={2}
        onClick={handleExpandClick}
        sx={{
          p: 1,
          cursor: 'pointer',
          '&:hover': {
            bgcolor: 'action.hover',
          },
        }}
      >
        {fileInfo.isAudioOrVideo ? (
          <VideocamIcon color="action" sx={{ flexShrink: 0 }} />
        ) : (
          <InsertDriveFileRoundedIcon color="action" sx={{ flexShrink: 0 }} />
        )}

        <Stack sx={{ flexGrow: 1, minWidth: 0 }}>
          <TruncateTypography
            text={displayName}
            variant="subtitle1"
            fontSize={14}
            sx={{
              whiteSpace: 'nowrap',
            }}
            maxLength={expanded ? undefined : 20}
          />
        </Stack>

        <Stack direction="row" alignItems="center" sx={{ flexShrink: 0 }}>
          {fileInfo.isFileReady && (
            <Checkbox 
              checked={isSelected} 
              onChange={handleCheckboxChange} 
              size="small"
              onClick={(e) => e.stopPropagation()}
            />
          )}
          <Iconify
            icon={
              expanded
                ? 'material-symbols:collapse-content-rounded'
                : 'material-symbols:expand-content-rounded'
            }
            sx={{
              transition: 'transform 0.2s',
              color: 'action.active',
              mr: 1,
            }}
          />
          <Box onClick={handleActionsClick}>
            <ResourceActions 
              resource={file} 
              hiddenActions={['select']} 
              onItemDeleted={onItemDeleted}
            />
          </Box>
        </Stack>
      </Stack>

      <Collapse in={expanded} timeout="auto" unmountOnExit>
        <Box 
          sx={{ p: 1, pt: 0, cursor: 'pointer' }}
          onClick={handlePreviewClick}
        >
          {fileInfo.isAudioOrVideo ? (
            <ResourcePreviewer data={file} enableLazyLoading={false} />
          ) : (
            <ResourceThumbnail data={file} enableLazyLoading={false} />
          )}
        </Box>
        {expanded && (
          <Stack
            direction="row"
            alignItems="center"
            justifyContent="space-between"
            sx={{ px: 1, pb: 1 }}
          >
            {fileInfo.isFileReady ? (
              <Typography
                variant="body2"
                sx={{
                  color: 'text.disabled',
                  fontSize: 12,
                  pt: 0.5,
                  px: 0.5,
                  fontStyle: 'italic',
                }}
              >
                Last Modified:&nbsp;
                <Box component="span" sx={{ fontWeight: 500, color: 'text.primary' }}>
                  {formattedDate}
                </Box>
              </Typography>
            ) : (
              <Stack direction="row" gap={1} alignItems="center">
                {!file.isTranscoding && (
                  <Typography
                    variant="caption"
                    color="info.main"
                    fontWeight={600}
                    sx={{ fontSize: 12 }}
                  >
                    {transcriptionStatusDisplay}
                  </Typography>
                )}
                {file.isTranscoding && <TranscodingStatus />}
              </Stack>
            )}
          </Stack>
        )}
      </Collapse>
    </Card>
  );
});

// Add display name for debugging
FileCardLegacy.displayName = 'FileCardLegacy';

export default FileCardLegacy; 