import type { Resource } from 'src/types';
import type { SxProps } from '@mui/material';
import type { ResourceItem } from 'src/sections/resources/components/resources-list';

import { memo } from 'react';

import useFeatureFlags from 'src/hooks/feature-flags';

import { AppFeatures } from 'src/types';

import FileCardLegacy from './file-card-legacy';
import FileCardEnhanced from './file-card-enhanced';

interface FileCardProps {
  file: ResourceItem;
  sx?: SxProps;
  onSelect: (file: Resource, isSelected: boolean) => void;
  isSelected: boolean;
  onItemDeleted?: () => void;
}

const FileCard = memo<FileCardProps>(({ file, sx, onSelect, isSelected, onItemDeleted }) => {
  const { isFlagEnabled } = useFeatureFlags();
  const isEnhancedEnabled = isFlagEnabled(AppFeatures.NEW_PREVIEW_EDITOR_FONT_SIZE_CONTROL);

  if (isEnhancedEnabled) {
    return (
      <FileCardEnhanced
        file={file}
        sx={sx}
        onSelect={onSelect}
        isSelected={isSelected}
        onItemDeleted={onItemDeleted}
      />
    );
  }

  return (
    <FileCardLegacy
      file={file}
      sx={sx}
      onSelect={onSelect}
      isSelected={isSelected}
      onItemDeleted={onItemDeleted}
    />
  );
});

// Add display name for debugging
FileCard.displayName = 'FileCard';

export default FileCard;
