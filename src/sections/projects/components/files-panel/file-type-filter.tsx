import { memo, useMemo, useState, useCallback } from 'react';

import {
  Box,
  Menu,
  alpha,
  Button,
  styled,
  Checkbox,
  MenuItem,
  useTheme,
  ListItemText,
} from '@mui/material';

import { Iconify } from 'src/components/iconify';

import { FileType } from 'src/types/project';

interface FileTypeFilterProps {
  selectedFileTypes: FileType[];
  onFileTypesChange: (fileTypes: FileType[]) => void;
}

const FILE_TYPE_OPTIONS: Array<{ value: FileType; label: string; icon: string }> = [
  { value: FileType.VIDEO, label: 'Video', icon: 'material-symbols:videocam' },
  { value: FileType.AUDIO, label: 'Audio', icon: 'material-symbols:music-note' },
  { value: FileType.DOCUMENT, label: 'Document', icon: 'material-symbols:description' },
  { value: FileType.IMAGE, label: 'Image', icon: 'material-symbols:image' },
  { value: FileType.UNKNOWN, label: 'Other', icon: 'material-symbols:help' },
];

// Styled components for better performance
const FilterButton = styled(Button, {
  shouldForwardProp: (prop) => prop !== 'hasSelection',
})<{ hasSelection: boolean }>(({ theme, hasSelection }) => ({
  minWidth: 92,
  height: 40,
  justifyContent: 'space-between',
  color: theme.palette.text.primary,
  borderColor: hasSelection ? theme.palette.primary.main : theme.palette.divider,
  backgroundColor: hasSelection
    ? alpha(theme.palette.primary.main, 0.08)
    : theme.palette.background.paper,
  fontWeight: hasSelection ? 500 : 400,
  fontSize: '0.875rem',
  '&:hover': {
    backgroundColor: alpha(theme.palette.action.hover, 0.5),
    borderColor: theme.palette.primary.main,
    color: theme.palette.text.primary,
  },
  '& .MuiButton-endIcon': {
    marginLeft: 'auto',
    color: theme.palette.text.secondary,
  },
}));

const IconWrapper = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: 20,
  height: 20,
  borderRadius: theme.spacing(1),
  backgroundColor: alpha(theme.palette.action.selected, 0.3),
  color: theme.palette.action.active,
}));

const FileTypeFilter = memo<FileTypeFilterProps>(({ selectedFileTypes, onFileTypesChange }) => {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleOpenMenu = useCallback((event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  }, []);

  const handleCloseMenu = useCallback(() => {
    setAnchorEl(null);
  }, []);

  const handleFileTypeToggle = useCallback(
    (fileType: FileType) => {
      const newSelectedTypes = selectedFileTypes.includes(fileType)
        ? selectedFileTypes.filter((type) => type !== fileType)
        : [...selectedFileTypes, fileType];

      onFileTypesChange(newSelectedTypes);
    },
    [selectedFileTypes, onFileTypesChange]
  );

  const handleClearAll = useCallback(() => {
    onFileTypesChange([]);
    handleCloseMenu();
  }, [onFileTypesChange, handleCloseMenu]);

  // Memoize expensive computations
  const buttonState = useMemo(() => {
    const hasSelection = selectedFileTypes.length > 0;
    let label = 'Types';

    if (hasSelection) {
      if (selectedFileTypes.length === 1) {
        const option = FILE_TYPE_OPTIONS.find((opt) => opt.value === selectedFileTypes[0]);
        label = option?.label || 'Types';
      } else {
        label = `${selectedFileTypes.length} Types`;
      }
    }

    return { hasSelection, label };
  }, [selectedFileTypes]);

  // Memoize menu styles
  const menuStyles = useMemo(
    () => ({
      paper: {
        sx: {
          minWidth: 200,
          maxHeight: 280,
          mt: 0.5,
          backgroundColor: theme.palette.background.paper,
          border: `1px solid ${theme.palette.divider}`,
          borderRadius: 1,
          boxShadow: theme.shadows[8],
          '& .MuiMenuItem-root': {
            px: 1.5,
            py: 0.75,
            color: theme.palette.text.primary,
            fontSize: '0.875rem',
            '&:hover': {
              backgroundColor: theme.palette.action.hover,
            },
          },
        },
      },
    }),
    [theme]
  );

  return (
    <Box>
      <FilterButton
        variant="outlined"
        size="small"
        onClick={handleOpenMenu}
        endIcon={<Iconify icon="material-symbols:keyboard-arrow-down" />}
        hasSelection={buttonState.hasSelection}
        aria-label="Filter files by type"
        aria-expanded={open}
        aria-haspopup="menu"
      >
        {buttonState.label}
      </FilterButton>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleCloseMenu}
        transformOrigin={{ horizontal: 'left', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'left', vertical: 'bottom' }}
        slotProps={menuStyles}
      >
        

        {/* Types Options */}
        {FILE_TYPE_OPTIONS.map((option) => {
          const isSelected = selectedFileTypes.includes(option.value);
          return (
            <MenuItem
              key={option.value}
              onClick={() => handleFileTypeToggle(option.value)}
              sx={{
                py: 0.75,
                px: 1.5,
                color: theme.palette.text.primary,
                fontSize: '0.875rem',
                backgroundColor: isSelected
                  ? alpha(theme.palette.primary.main, 0.08)
                  : 'transparent',
                '&:hover': {
                  backgroundColor: theme.palette.action.hover,
                },
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                borderRadius: 1,
                mx: 0.5,
              }}
            >
              <Checkbox
                checked={isSelected}
                size="small"
                sx={{
                  color: theme.palette.text.disabled,
                  '&.Mui-checked': {
                    color: theme.palette.primary.main,
                  },
                  // Prevent checkbox from handling clicks
                  pointerEvents: 'none',
                }}
              />
              <IconWrapper>
                <Iconify icon={option.icon} width={14} />
              </IconWrapper>
              <Box
                sx={{
                  color: theme.palette.text.primary,
                  fontSize: '0.875rem',
                  fontWeight: isSelected ? 500 : 400,
                  flexGrow: 1,
                  ml: 1,
                }}
              >
                {option.label}
              </Box>
            </MenuItem>
          );
        })}

        {/* Clear All Option */}
        <MenuItem
          onClick={handleClearAll}
          disabled={!buttonState.hasSelection}
          sx={{
            py: 0.75,
            px: 1.5,
            borderTop: `1px solid ${theme.palette.divider}`,
            mt: 0.5,
            mx: 0.5,
            color: theme.palette.text.secondary,
            fontSize: '0.875rem',
             '&:hover': {
              backgroundColor: theme.palette.action.hover,
              color: theme.palette.text.primary,
            },
          }}
        >
          <Iconify icon="material-symbols:close" width={16} sx={{ mr: 1.5, color: 'inherit' }} />
          <ListItemText primary="clear all" />
        </MenuItem>
      </Menu>

      {/* Selected Types as Chips */}
      {/* TODO: Implement this */}
      {/* {buttonState.hasSelection && (
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.75, mt: 1 }}>
          {selectedFileTypes.map((fileType) => {
            const option = FILE_TYPE_OPTIONS.find((opt) => opt.value === fileType);
            if (!option) return null;

            return (
              <Chip
                key={fileType}
                label={option.label}
                size="small"
                onClick={() => handleFileTypeToggle(fileType)}
                onDelete={() => handleFileTypeToggle(fileType)}
                deleteIcon={<Iconify icon="material-symbols:close" width={14} />}
                sx={{
                  height: 24,
                  backgroundColor: alpha(theme.palette.primary.main, 0.08),
                  color: theme.palette.text.primary,
                  border: `1px solid ${theme.palette.divider}`,
                  cursor: 'pointer',
                  '& .MuiChip-label': {
                    px: 1,
                    fontWeight: 500,
                    fontSize: '0.75rem',
                    color: theme.palette.text.primary,
                  },
                  '& .MuiChip-deleteIcon': {
                    color: theme.palette.text.secondary,
                    '&:hover': {
                      color: theme.palette.text.primary,
                      backgroundColor: theme.palette.action.hover,
                      borderRadius: '50%',
                    },
                  },
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.primary.main, 0.12),
                    borderColor: theme.palette.primary.main,
                  },
                  '&:focus': {
                    backgroundColor: alpha(theme.palette.primary.main, 0.12),
                    borderColor: theme.palette.primary.main,
                  },
                }}
              />
            );
          })}
        </Box>
      )} */}
    </Box>
  );
});

FileTypeFilter.displayName = 'FileTypeFilter';

export default FileTypeFilter;
