import type { FileType } from 'src/types/project';
import type { SelectChangeEvent } from '@mui/material';
import type { SortOption } from 'src/hooks/use-resource-pagination';

import { memo, useState, useEffect, useCallback } from 'react';

import {
  Box,
  Stack,
  Select,
  MenuItem,
  Collapse,
  TextField,
  InputLabel,
  IconButton,
  FormControl,
  InputAdornment,
} from '@mui/material';

import { Iconify } from 'src/components/iconify';

import FileTypeFilter from './file-type-filter';

interface FilesPanelFiltersProps {
  expanded: boolean;
  searchQuery: string;
  sortOption: SortOption;
  selectedFileTypes: FileType[];
  onSearchChange: (query: string) => void;
  onSortChange: (option: SortOption) => void;
  onFileTypesChange: (fileTypes: FileType[]) => void;
}

const sortOptions = [
  { value: 'latest' as const, label: 'Latest' },
  { value: 'oldest' as const, label: 'Oldest' },
  { value: 'title-asc' as const, label: 'Title (A-Z)' },
  { value: 'title-desc' as const, label: 'Title (Z-A)' },
];

// Debounce delay in milliseconds
const SEARCH_DEBOUNCE_DELAY = 300;

const FilesPanelFilters = memo<FilesPanelFiltersProps>(
  ({
    expanded,
    searchQuery,
    sortOption,
    selectedFileTypes,
    onSearchChange,
    onSortChange,
    onFileTypesChange,
  }) => {
    // Local state for immediate UI updates
    const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
    // State to track search input focus only
    const [isSearchInputFocused, setIsSearchInputFocused] = useState(false);

    // Sync local state with prop when it changes externally
    useEffect(() => {
      setLocalSearchQuery(searchQuery);
    }, [searchQuery]);

    // Debounced search effect
    useEffect(() => {
      const timeoutId = setTimeout(() => {
        if (localSearchQuery !== searchQuery) {
          onSearchChange(localSearchQuery);
        }
      }, SEARCH_DEBOUNCE_DELAY);

      return () => clearTimeout(timeoutId);
    }, [localSearchQuery, searchQuery, onSearchChange]);

    const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
      setLocalSearchQuery(event.target.value);
    }, []);

    const handleSortChange = useCallback(
      (event: SelectChangeEvent<SortOption>) => {
        onSortChange(event.target.value as SortOption);
      },
      [onSortChange]
    );

    const handleClearSearch = useCallback(() => {
      setLocalSearchQuery('');
      onSearchChange('');
    }, [onSearchChange]);

    const handleSearchFocus = useCallback(() => {
      setIsSearchInputFocused(true);
    }, []);

    const handleSearchBlur = useCallback(() => {
      setIsSearchInputFocused(false);
    }, []);

    return (
      <Collapse in={expanded} timeout="auto" unmountOnExit>
        <Box 
          sx={{
            display: 'flex',
            alignItems: 'flex-start',
            gap: 1,
          }}
        >
          {/* Search Input */}
          <TextField
            size="small"
            placeholder="Search files..."
            value={localSearchQuery}
            onChange={handleSearchChange}
            onFocus={handleSearchFocus}
            onBlur={handleSearchBlur}
            id='search-input'
            InputProps={{
              startAdornment: (
                <label htmlFor='search-input'>
                  <InputAdornment position="start">
                    <Iconify icon="material-symbols:search" />
                  </InputAdornment>
                </label>
              ),
              endAdornment: localSearchQuery && (
                <InputAdornment position="end">
                  <IconButton size="small" onClick={handleClearSearch} sx={{ p: 0.5 }}>
                    <Iconify icon="material-symbols:close" />
                  </IconButton>
                </InputAdornment>
              ),
            }}
            sx={{
              flex: isSearchInputFocused ? '1 1 100%' : '1 1 auto',
              transition: 'flex 250ms cubic-bezier(0.4, 0, 0.2, 1), min-width 250ms cubic-bezier(0.4, 0, 0.2, 1)',
              width: '100%',
              '& .MuiInputBase-root': {
                width: '100%',
              },
            }}
          />

          {/* Sort and Filter Controls */}
          <Stack 
            direction="row" 
            spacing={1} 
            sx={{ 
              alignItems: 'flex-start',
              flexShrink: 0,
              opacity: isSearchInputFocused ? 0 : 1,
              transform: isSearchInputFocused ? 'translateX(10px)' : 'translateX(0)',
              visibility: isSearchInputFocused ? 'hidden' : 'visible',
              width: isSearchInputFocused ? 0 : 200,
              flex: isSearchInputFocused ? '0 0 0px' : '0 0 200px',
              overflow: isSearchInputFocused ? 'hidden' : 'visible',
              transition: isSearchInputFocused 
                ? 'opacity 150ms ease, transform 150ms ease, visibility 150ms ease, width 200ms ease, flex 200ms ease, overflow 150ms ease'
                : 'width 200ms ease 100ms, flex 200ms ease 100ms, opacity 300ms ease 200ms, transform 300ms ease 200ms, visibility 300ms ease 200ms, overflow 300ms ease 200ms',
            }}
          >
            <FormControl size="small" sx={{ minWidth: 92 }}>
              <InputLabel>Sort by</InputLabel>
              <Select value={sortOption} onChange={handleSortChange} label="Sort by">
                {sortOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FileTypeFilter
              selectedFileTypes={selectedFileTypes}
              onFileTypesChange={onFileTypesChange}
            />
          </Stack>
        </Box>
      </Collapse>
    );
  }
);

// Add display name for debugging
FilesPanelFilters.displayName = 'FilesPanelFilters';

export default FilesPanelFilters;
