
import { varAlpha } from 'minimal-shared/utils';

import { Box, Link, Stack, Tooltip, Checkbox, Typography, IconButton } from '@mui/material';

import { Iconify } from 'src/components/iconify';

import { RefreshIcon } from './components';

interface FilesPanelHeaderProps {
  totalFiles: number;
  filteredFiles: number;
  selectedCount: number;
  limit: number;
  isAllSelected: boolean;
  isIndeterminate: boolean;
  filtersExpanded: boolean;
  isPolling?: boolean;
  onSelectAll: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onSelectAllResources?: () => void;
  onClearSelection?: () => void;
  onFiltersToggle: () => void;
  onRefetch: () => void;
  shouldAnimateRefetch?: boolean;
  showUpdatedMessage?: boolean;
}

const FilesPanelHeader = ({
  totalFiles,
  filteredFiles,
  selectedCount,
  limit,
  isAllSelected,
  isIndeterminate,
  filtersExpanded,
  isPolling = false,
  onSelectAll,
  onSelectAllResources,
  onClearSelection,
  onFiltersToggle,
  onRefetch,
  shouldAnimateRefetch = false,
  showUpdatedMessage = false,
}: FilesPanelHeaderProps) => {

  // Show the "files updated" message when shouldAnimateRefetch changes to true
  const displayText =
    totalFiles === filteredFiles
      ? `Total Files: ${totalFiles}`
      : `Files: ${filteredFiles} of ${totalFiles}`;

  // Render function for "files updated" message
  const renderUpdatedMessage = () => {
    if (!showUpdatedMessage) {
      return null;
    }

    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          mt: 0.5,
          mb: 1,
          backgroundColor: (theme) => varAlpha(theme.vars.palette.success.mainChannel, 0.12),
          borderRadius: 1,
          px: 1.5,
          py: 0.5,
          border: (theme) => `1px solid ${varAlpha(theme.vars.palette.success.mainChannel, 0.24)}`,
        }}
      >
        <Typography 
          variant="body2" 
          sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: 1,
            color: 'success.main',
          }}
        >
          Files been updated,
          <Link
            component="button"
            variant="body2"
            onClick={onRefetch}
            sx={{
              cursor: 'pointer',
              textDecoration: 'none',
              color: 'success.main',
              display: 'flex',
              alignItems: 'center',
              gap: 0.5,
              '&:hover': {
                textDecoration: 'underline',
              },
            }}
          >
            <Iconify icon="material-symbols:refresh" sx={{ fontSize: '1rem' }} />
            refresh
          </Link>
          data
        </Typography>
      </Box>
    );
  };

  // Render function for selection status
  const renderSelectionStatus = () => {
    const hasPartialSelection = selectedCount >= limit && totalFiles > limit;
    const hasFullSelection = selectedCount === totalFiles && totalFiles > limit;

    if (selectedCount === 0) {
      return null;
    }

    // Always return a container with consistent height to prevent layout jumping
    return (
      <Box
        sx={{
          minHeight: hasPartialSelection || hasFullSelection ? 20 : 0, // Consistent height
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          mt: hasPartialSelection || hasFullSelection ? 0.5 : 0,
          transition: 'margin-top 0.2s ease',
          backgroundColor: (theme) => varAlpha(theme.vars.palette.grey['500Channel'], 0.08),
          borderRadius: 1,
          px: hasPartialSelection || hasFullSelection ? 1.5 : 0,
          py: hasPartialSelection || hasFullSelection ? 0.5 : 0,
          mb: hasPartialSelection || hasFullSelection ? 2 : 0,
        }}
      >
        {hasFullSelection && onClearSelection && (
          <Typography 
            variant="body2" 
            sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: 0.5,
              color: 'text.secondary',
            }}
          >
            All {totalFiles} resources are selected.
            <Link
              component="button"
              variant="body2"
              onClick={onClearSelection}
              sx={{
                cursor: 'pointer',
                textDecoration: 'underline',
                color: 'primary.main',
                ml: 0.5,
              }}
            >
              Clear selection
            </Link>
          </Typography>
        )}

        {hasPartialSelection && onSelectAllResources && !hasFullSelection && (
          <Link
            component="button"
            variant="body2"
            onClick={onSelectAllResources}
            sx={{
              cursor: 'pointer',
              textDecoration: 'underline',
              color: 'primary.main',
            }}
          >
            Select all {totalFiles} resources
          </Link>
        )}
      </Box>
    );
  };

  return (
    <Stack>
      <Stack direction="row" alignItems="center" justifyContent="space-between">
        <Stack direction="row" alignItems="center" spacing={1}>
          <Typography variant="subtitle2">{displayText}</Typography>
          
          {isPolling && (
            <Tooltip title="Processing files in background" placement="top">
              <Iconify
                icon="material-symbols:sync"
                sx={{
                  color: 'primary.main',
                  animation: 'spin 2s linear infinite',
                  fontSize: '1rem',
                  '@keyframes spin': {
                    from: { transform: 'rotate(0deg)' },
                    to: { transform: 'rotate(360deg)' },
                  },
                }}
              />
            </Tooltip>
          )}
        </Stack>

        <Stack direction="row" alignItems="center">
          <RefreshIcon 
            onRefetch={onRefetch}
            shouldAnimateRefetch={shouldAnimateRefetch}
            size="medium"
          />
          <Tooltip title={filtersExpanded ? 'Hide filters' : 'Show filters'} placement="top">
            <IconButton size="small" onClick={onFiltersToggle}>
              <Iconify
                icon="material-symbols:filter-alt-outline"
                sx={{ color: filtersExpanded ? 'primary.main' : 'text.secondary' }}
              />
            </IconButton>
          </Tooltip>
          <Checkbox
            size="small"
            checked={isAllSelected}
            indeterminate={isIndeterminate}
            onChange={onSelectAll}
            disabled={filteredFiles === 0}
          />
        </Stack>
      </Stack>
      {renderUpdatedMessage()}
      {renderSelectionStatus()}
    </Stack>
  );
};

export default FilesPanelHeader;
