import type { SelectChangeEvent } from '@mui/material';

import { memo, forwardRef } from 'react';

import {
  Box,
  Stack,
  Select,
  MenuItem,
  Pagination,
  Typography,
  FormControl,
} from '@mui/material';

import { RefreshIcon } from './components';
import { PAGINATION_OPTIONS } from './constants';

interface FilesPanelPaginationProps {
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
  isLoading?: boolean;
  onPageChange: (page: number) => void;
  onLimitChange: (limit: number) => void;
  onRefetch: () => void;
  shouldAnimateRefetch?: boolean;
}

const FilesPanelPagination = memo(
  forwardRef<HTMLDivElement, FilesPanelPaginationProps>(({
    page,
    limit,
    totalCount,
    totalPages,
    isLoading = false,
    onPageChange,
    onLimitChange,
    onRefetch,
    shouldAnimateRefetch = false,
  }, ref) => {
    const handlePageChange = (_: React.ChangeEvent<unknown>, newPage: number) => {
      onPageChange(newPage);
    };

    const handleLimitChange = (event: SelectChangeEvent<number>) => {
      onLimitChange(Number(event.target.value));
    };

    // Calculate display range
    const startIndex = (page - 1) * limit + 1;
    const endIndex = Math.min(page * limit, totalCount);

    if (totalCount === 0) {
      return null;
    }

    return (
      <Box
        ref={ref}
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 1,
          pt: 1,
          borderTop: '1px dashed',
          borderColor: 'divider',
        }}
      >
        {/* Results info and page size selector */}
        <Stack
          direction="row"
          alignItems="center"
          justifyContent="space-between"
          sx={{ minHeight: 36 }}
        >
          <Stack direction="row" alignItems="center" spacing={1}>
            <RefreshIcon 
              onRefetch={onRefetch}
              shouldAnimateRefetch={shouldAnimateRefetch}
              size="small"
            />
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              {isLoading ? 'Loading...' : `${startIndex}-${endIndex} of ${totalCount}`}
            </Typography>
          </Stack>

          <Stack direction="row" alignItems="center" spacing={1}>
            <Typography variant="body2" sx={{ color: 'text.secondary', fontSize: 12 }}>
              Rows:
            </Typography>
            <FormControl size="small">
              <Select
                value={limit}
                onChange={handleLimitChange}
                disabled={isLoading}
                sx={{
                  minWidth: 60,
                  height: 32,
                  '& .MuiSelect-select': {
                    py: 0.5,
                    fontSize: 12,
                  },
                }}
              >
                {PAGINATION_OPTIONS.map((option) => (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Stack>
        </Stack>

        {/* Pagination controls */}
        {totalPages > 1 && (
          <Stack direction="row" justifyContent="center">
            <Pagination
              count={totalPages}
              page={page}
              onChange={handlePageChange}
              disabled={isLoading}
              size="small"
              color="primary"
              showFirstButton
              showLastButton
              sx={{
                '& .MuiPaginationItem-root': {
                  fontSize: 12,
                  minWidth: 28,
                  height: 28,
                },
              }}
            />
          </Stack>
        )}
      </Box>
    );
  })
);

FilesPanelPagination.displayName = 'FilesPanelPagination';

export default FilesPanelPagination; 