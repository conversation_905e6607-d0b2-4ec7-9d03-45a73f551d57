import type { SelectChangeEvent } from '@mui/material';

import { memo, useState, useEffect, useCallback } from 'react';

import {
  Box,
  Fade,
  Stack,
  Paper,
  alpha,
  Select,
  styled,
  MenuItem,
  useTheme,
  TextField,
  Pagination,
  Typography,
  InputLabel,
  IconButton,
  FormControl,
  InputAdornment,
} from '@mui/material';

import { Iconify } from 'src/components/iconify';

import type { SortOption } from './index';

// Styled components for better performance
const StyledPaper = styled(Paper)(({ theme }) => ({
  position: 'sticky',
  bottom: 0,
  left: 0,
  right: 0,
  zIndex: theme.zIndex.appBar - 1,
  marginTop: 'auto',
  borderRadius: '12px 12px 0 0',
  overflow: 'hidden',
  backgroundColor: alpha(theme.palette.background.paper, 0.95),
  backdropFilter: 'blur(8px)',
  border: `1px solid ${alpha(theme.palette.divider, 0.12)}`,
  borderBottom: 'none',
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  flexGrow: 1,
  '& .MuiOutlinedInput-root': {
    backgroundColor: alpha(theme.palette.background.default, 0.5),
  },
}));

const StyledFormControl = styled(FormControl)(({ theme }) => ({
  minWidth: 140,
  '& .MuiOutlinedInput-root': {
    backgroundColor: alpha(theme.palette.background.default, 0.5),
  },
}));

const StyledSelect = styled(Select)(({ theme }) => ({
  minWidth: 65,
  height: 32,
  backgroundColor: alpha(theme.palette.background.default, 0.5),
  '& .MuiSelect-select': {
    paddingTop: theme.spacing(0.5),
    paddingBottom: theme.spacing(0.5),
    fontSize: 12,
    fontWeight: 500,
  },
}));

const StyledPagination = styled(Pagination)(() => ({
  '& .MuiPaginationItem-root': {
    fontSize: 12,
    minWidth: 32,
    height: 32,
    fontWeight: 500,
    '&.Mui-selected': {
      fontWeight: 600,
    },
  },
  '& .MuiPaginationItem-icon': {
    fontSize: 16,
  },
}));

const LoadingIcon = styled(Iconify)(() => ({
  fontSize: 16,
  animation: 'spin 1s linear infinite',
  '@keyframes spin': {
    '0%': { transform: 'rotate(0deg)' },
    '100%': { transform: 'rotate(360deg)' },
  },
}));

interface FilesPanelStickyControlsProps {
  // Search props
  searchQuery: string;
  onSearchChange: (query: string) => void;

  // Sort props
  sortOption: SortOption;
  onSortChange: (option: SortOption) => void;

  // Pagination props
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
  isLoading?: boolean;
  onPageChange: (page: number) => void;
  onLimitChange: (limit: number) => void;

  // Display props
  filteredCount?: number;
  showControls?: boolean;
}

const sortOptions = [
  { value: 'latest' as const, label: 'Latest' },
  { value: 'oldest' as const, label: 'Oldest' },
  { value: 'title-asc' as const, label: 'Title (A-Z)' },
  { value: 'title-desc' as const, label: 'Title (Z-A)' },
];

const ROWS_PER_PAGE_OPTIONS = [5, 10, 25, 50];
const SEARCH_DEBOUNCE_DELAY = 300;

const FilesPanelStickyControls = memo<FilesPanelStickyControlsProps>(
  ({
    // Search props
    searchQuery,
    onSearchChange,

    // Sort props
    sortOption,
    onSortChange,

    // Pagination props
    page,
    limit,
    totalCount,
    totalPages,
    isLoading = false,
    onPageChange,
    onLimitChange,

    // Display props
    filteredCount,
    showControls = true,
  }) => {
    const theme = useTheme();

    // Local state for immediate UI updates
    const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);

    // Sync local state with prop when it changes externally
    useEffect(() => {
      setLocalSearchQuery(searchQuery);
    }, [searchQuery]);

    // Debounced search effect
    useEffect(() => {
      const timeoutId = setTimeout(() => {
        if (localSearchQuery !== searchQuery) {
          onSearchChange(localSearchQuery);
        }
      }, SEARCH_DEBOUNCE_DELAY);

      return () => clearTimeout(timeoutId);
    }, [localSearchQuery, searchQuery, onSearchChange]);

    // Event handlers
    const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
      setLocalSearchQuery(event.target.value);
    }, []);

    const handleSortChange = useCallback(
      (event: SelectChangeEvent<SortOption>) => {
        onSortChange(event.target.value as SortOption);
      },
      [onSortChange]
    );

    const handleClearSearch = useCallback(() => {
      setLocalSearchQuery('');
      onSearchChange('');
    }, [onSearchChange]);

    const handlePageChange = useCallback(
      (_: React.ChangeEvent<unknown>, newPage: number) => {
        onPageChange(newPage);
      },
      [onPageChange]
    );

    const handleLimitChange = useCallback(
      (event: SelectChangeEvent<unknown>) => {
        onLimitChange(Number(event.target.value));
      },
      [onLimitChange]
    );

    // Calculate display range
    const startIndex = totalCount > 0 ? (page - 1) * limit + 1 : 0;
    const endIndex = Math.min(page * limit, totalCount);
    const displayCount = filteredCount !== undefined ? filteredCount : totalCount;

    if (!showControls || totalCount === 0) {
      return null;
    }

    return (
      <Fade in={showControls} timeout={200}>
        <StyledPaper elevation={8}>
          <Box sx={{ p: 2 }}>
            {/* Search and Sort Controls */}
            <Stack direction="row" spacing={1.5} sx={{ mb: 2 }}>
              <StyledTextField
                size="small"
                placeholder="Search files..."
                value={localSearchQuery}
                onChange={handleSearchChange}
                disabled={isLoading}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Iconify
                        icon="material-symbols:search"
                        sx={{
                          color: 'text.disabled',
                          fontSize: 18,
                        }}
                      />
                    </InputAdornment>
                  ),
                  endAdornment: localSearchQuery && (
                    <InputAdornment position="end">
                      <IconButton
                        size="small"
                        onClick={handleClearSearch}
                        disabled={isLoading}
                        sx={{
                          p: 0.5,
                          color: 'text.secondary',
                          '&:hover': {
                            color: 'text.primary',
                          },
                        }}
                      >
                        <Iconify icon="material-symbols:close" sx={{ fontSize: 16 }} />
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />

              <StyledFormControl>
                <InputLabel>Sort by</InputLabel>
                <Select
                  value={sortOption}
                  onChange={handleSortChange}
                  label="Sort by"
                  disabled={isLoading}
                >
                  {sortOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </StyledFormControl>
            </Stack>

            {/* Results info and pagination controls */}
            <Stack spacing={1.5}>
              {/* Results info and page size selector */}
              <Stack
                direction="row"
                alignItems="center"
                justifyContent="space-between"
                sx={{ minHeight: 32 }}
              >
                <Typography
                  variant="body2"
                  sx={{
                    color: 'text.secondary',
                    fontWeight: 500,
                  }}
                >
                  {isLoading ? (
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <LoadingIcon icon="eos-icons:loading" />
                      <span>Loading...</span>
                    </Stack>
                  ) : (
                    `${startIndex}-${endIndex} of ${displayCount}${
                      filteredCount !== undefined && filteredCount !== totalCount
                        ? ` (filtered from ${totalCount})`
                        : ''
                    }`
                  )}
                </Typography>

                <Stack direction="row" alignItems="center" spacing={1}>
                  <Typography
                    variant="body2"
                    sx={{
                      color: 'text.secondary',
                      fontSize: 12,
                      fontWeight: 500,
                    }}
                  >
                    Rows:
                  </Typography>
                  <FormControl size="small">
                    <StyledSelect
                      value={limit}
                      onChange={(event: SelectChangeEvent<unknown>) =>
                        handleLimitChange(event as SelectChangeEvent<number>)
                      }
                      disabled={isLoading}
                    >
                      {ROWS_PER_PAGE_OPTIONS.map((option) => (
                        <MenuItem key={option} value={option}>
                          {option}
                        </MenuItem>
                      ))}
                    </StyledSelect>
                  </FormControl>
                </Stack>
              </Stack>

              {/* Pagination controls */}
              {totalPages > 1 && (
                <Stack direction="row" justifyContent="center">
                  <StyledPagination
                    count={totalPages}
                    page={page}
                    onChange={handlePageChange}
                    disabled={isLoading}
                  />
                </Stack>
              )}
            </Stack>
          </Box>
        </StyledPaper>
      </Fade>
    );
  }
);

FilesPanelStickyControls.displayName = 'FilesPanelStickyControls';

export default FilesPanelStickyControls;
