import type { SxProps } from '@mui/material';
import type { RootState } from 'src/store/reducers';
import type {
  Resource,
  ProjectEventBase,
  ResourceMovedEvent,
  ProjectDeletedEvent,
  ResourceCreatedEvent,
  ResourceDeletedEvent,
  ResourceUpdatedEvent,
  MemberChangedRoleEvent,
} from 'src/types';

import { toast } from 'sonner';
import { useStore, useDispatch } from 'react-redux';
import { useRef, useMemo, useState, useEffect, useCallback } from 'react';

import { List, Paper, Stack } from '@mui/material';

import { paths } from 'src/routes/paths';
import { useRouter, usePathname } from 'src/routes/hooks';

import useUserSessions from 'src/hooks/user-sessions';
import useUserInitialContext from 'src/hooks/user-initial-context';
import { useResourcePagination } from 'src/hooks/use-resource-pagination';
import { useUploadCompletionRefetch } from 'src/hooks/use-upload-completion-refetch';
import { useTranscodingStatusPolling } from 'src/hooks/use-transcoding-status-polling';

import { AUTH } from 'src/lib/firebase';
import { SSEEventType } from 'src/types';
import { useEventListener } from 'src/hooks';
import { useCitationContext } from 'src/contexts/citation-context';
import { useLazyGetProjectAllResourceIdsQuery } from 'src/store/api/projects/hooks';
import { optimisticRemoveResource, optimisticUpdateResource } from 'src/store/api/projects/utils';

import { Scrollbar } from 'src/components/scrollbar';
import { EmptyContent } from 'src/components/empty-content';
import { LoadingScreen } from 'src/components/loading-screen';

import { FilePreviewerModal } from 'src/sections/resources/components/resource-card/components/file-previewer-modal/file-previewer-modal';
import { FilePreviewerProvider } from 'src/sections/resources/components/resource-card/components/file-previewer-modal/file-previewer-provider';

import FileCard from './file-card';
import SessionCard from './session-card';
import { POLLING_INTERVALS } from './constants';
import FilesPanelHeader from './files-panel-header';
import PendingUploadCard from './pending-upload-card';
import FilesPanelFilters from './files-panel-filters';
import FilesPanelPagination from './files-panel-pagination';
import { FilesPanelProvider, useFilesPanelContext } from './context/files-panel-context';

export type { SortOption } from 'src/hooks/use-resource-pagination';

// Constants
const DEFAULT_ITEMS_PER_PAGE = 10;
const INITIAL_FILTERS_EXPANDED = true;
const UPDATED_MESSAGE_DURATION = 5000;

const refEvent = new Map<string, boolean>();
const TIME_TO_DELETE_EVENT = 3000;

interface FilesPanelProps {
  selectedFiles: Resource[];
  onFilesSelected?: (files: Resource[]) => void;
  projectId: string;
  sx?: SxProps;
}

const FilesPanelContent = ({ selectedFiles, onFilesSelected, projectId, sx = {} }: FilesPanelProps) => {
  const router = useRouter();
  const pathname = usePathname();
  
  const store = useStore();
  const dispatch = useDispatch();
  const prevProjectIdRef = useRef<string | null>(null);
  const { expandFile, updateSeekTime, expansionState } = useFilesPanelContext();
  const { setCitationClickHandler, clearCitationClickHandler } = useCitationContext();

  const { defaultProject, refetchGetUserInitialContext } = useUserInitialContext()
  const [filtersExpanded, setFiltersExpanded] = useState(INITIAL_FILTERS_EXPANDED);
  const [shouldAnimateRefetch, setShouldAnimateRefetch] = useState(false);
  const [showUpdatedMessage, setShowUpdatedMessage] = useState(false);

  const { ongoingSessions = [] } = useUserSessions({ projectId });

  // Lazy trigger for getting all resource IDs
  const [getAllResourceIds, { isLoading: isLoadingAllIds }] =
    useLazyGetProjectAllResourceIdsQuery();

  // Use the pagination hook for API-level pagination and sorting
  const {
    resources,
    totalCount,
    isLoading,
    isFetching,
    page,
    limit,
    totalPages,
    searchQuery,
    deferredSearchQuery,
    sortOption,
    selectedFileTypes,
    onPageChange,
    onLimitChange,
    onSearchChange,
    onSortChange,
    onFileTypesChange,
    refetch,
    onResetPagination,
  } = useResourcePagination({
    projectId,
    initialLimit: DEFAULT_ITEMS_PER_PAGE,
  });

  // Monitor transcoding status using the resources we already have
  // This replaces the need for a separate TranscodingStatusMonitor component
  const { isPolling } = useTranscodingStatusPolling({
    projectId,
    resources,
    pollingInterval: POLLING_INTERVALS.TRANSCODING_STATUS,
    errorRetryInterval: POLLING_INTERVALS.ERROR_RETRY,
  });

  const triggerShowUpdatedMessage = useCallback(() => {
    setShouldAnimateRefetch(true);
    setShowUpdatedMessage(true);
    setTimeout(() => {
      setShowUpdatedMessage(false);
    }, UPDATED_MESSAGE_DURATION);
  }, []);

  // Handle upload completion with debounced refetch
  const { pendingUploads } = useUploadCompletionRefetch({
    projectId,
    onRefetch: () => {
      triggerShowUpdatedMessage();
    },
    debounceMs: 500,
  });

  // Resources are already sorted by the API
  const sortedResources = resources;

  // Handle citation clicks
  const handleCitationClick = useCallback(
    (fileId: string, timestampSeconds?: number) => {
      // Find the file that matches the file_id from the citation
      // Since the current Resource type doesn't have metadata, we'll try to match by:
      // 1. Direct ID match
      // 2. File name containing the file_id
      // 3. Original filename containing the file_id
      const matchingFile = sortedResources.find((file) => {
        // Direct ID match
        if (file.id === fileId) {
          return true;
        }

        // Check if file name contains the file_id
        if (file.name && file.name.includes(fileId)) {
          return true;
        }

        // Check if file name contains the file_id
        if (file.fileName && file.fileName.includes(fileId)) {
          return true;
        }

        return false;
      });

      if (matchingFile) {
        // Check if the file is already expanded
        const isAlreadyExpanded =
          expansionState?.fileId === matchingFile.id && expansionState?.isExpanded;

        if (isAlreadyExpanded) {
          // If already expanded, just update the seek time
          updateSeekTime(matchingFile.id, timestampSeconds, true);
        } else {
          // If not expanded, expand the file
          expandFile(matchingFile.id, timestampSeconds, true);
        }
      } else {
        console.warn('File not found for citation:', fileId);
      }
    },
    [sortedResources, expandFile, updateSeekTime, expansionState]
  );

  // Register the citation click handler with the context
  useEffect(() => {
    setCitationClickHandler(handleCitationClick);
    return () => {
      clearCitationClickHandler();
    };
  }, [handleCitationClick, setCitationClickHandler, clearCitationClickHandler]);

  // Memoize selected file IDs set
  const selectedFileIds = useMemo(() => new Set(selectedFiles.map((f) => f.id)), [selectedFiles]);

  // Memoize selection state
  const selectionState = useMemo(() => {
    const hasNoItems =
      totalCount === 0 && ongoingSessions.length === 0 && pendingUploads.length === 0;

    // Check if all resources are selected (comparing with total count, not just current page)
    const allSelected = selectedFiles.length === totalCount && totalCount > 0;

    // For current page, check if all visible resources are selected
    const allCurrentPageSelected =
      resources.length > 0 && resources.every((resource) => selectedFileIds.has(resource.id));

    // Indeterminate state for the checkbox:
    // - Some resources selected but not all resources globally
    // - Or some current page resources selected but not all current page resources
    const someCurrentPageSelected = resources.some((resource) => selectedFileIds.has(resource.id));
    const partiallySelected =
      (selectedFiles.length > 0 && selectedFiles.length < totalCount) ||
      (someCurrentPageSelected && !allCurrentPageSelected);

    return {
      isEmpty: hasNoItems,
      isAllSelected: allSelected,
      isIndeterminate: partiallySelected,
      allCurrentPageSelected,
    };
  }, [
    totalCount,
    ongoingSessions.length,
    pendingUploads.length,
    selectedFiles.length,
    resources,
    selectedFileIds,
  ]);

  // Create a lookup map for all resources for quick ID to Resource conversion
  const resourceLookup = useMemo(() => {
    const lookup = new Map<string, Resource>();
    resources.forEach((resource) => {
      lookup.set(resource.id, resource);
    });
    return lookup;
  }, [resources]);

  // Memoize event handlers
  const handleSelectFile = useCallback(
    (file: Resource, isSelected: boolean) => {
      if (!onFilesSelected) return;

      if (isSelected) {
        onFilesSelected([...selectedFiles, file]);
      } else {
        onFilesSelected(selectedFiles.filter((f) => f.id !== file.id));
      }
    },
    [selectedFiles, onFilesSelected]
  );

  const handleSelectAll = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      if (!onFilesSelected) return;

      if (event.target.checked) {
        // Add current page resources to existing selected files
        const newSelection = [...selectedFiles];
        resources.forEach((resource) => {
          if (!selectedFileIds.has(resource.id)) {
            newSelection.push(resource);
          }
        });
        onFilesSelected(newSelection);
      } else {
        // Remove current page resources from selected files
        const currentPageIds = new Set(resources.map((r) => r.id));
        const filteredSelection = selectedFiles.filter((file) => !currentPageIds.has(file.id));
        onFilesSelected(filteredSelection);
      }
    },
    [resources, onFilesSelected, selectedFiles, selectedFileIds]
  );

  // Handle select all resources (call API to get all IDs)
  const handleSelectAllResources = useCallback(async () => {
    if (!onFilesSelected || isLoadingAllIds) return;

    try {
      const response = await getAllResourceIds({ id: projectId }).unwrap();

      const allResources: Resource[] = response.map((id) => {
        const existingResource = resourceLookup.get(id);
        if (existingResource) {
          return existingResource;
        }

        return {
          id,
          projectId,
        } as Resource;
      });

      onFilesSelected(allResources);
    } catch (error) {
      console.error('Failed to get all resource IDs:', error);
    }
  }, [getAllResourceIds, projectId, onFilesSelected, isLoadingAllIds, resourceLookup]);

  // Handle clear selection
  const handleClearSelection = useCallback(() => {
    if (!onFilesSelected) return;
    onFilesSelected([]);
  }, [onFilesSelected]);

  const handleFiltersToggle = useCallback(() => {
    setFiltersExpanded(!filtersExpanded);
  }, [filtersExpanded]);

  const handleRefetch = useCallback(() => {
    refetch();
    setShouldAnimateRefetch(false); // Clear the animation state when refetching
    setShowUpdatedMessage(false);
  }, [refetch]);

  // Handle item deletion - trigger refresh animation
  const handleItemDeleted = useCallback(() => {
    triggerShowUpdatedMessage();
  }, [triggerShowUpdatedMessage]);

  const handleRefEvent = useCallback(
    (event: ProjectEventBase) => {
      refEvent.set(event.eventId, true);
    },
    [refEvent]
  );

  const handleRefEventTimeout = useCallback((eventId: string) => {
    setTimeout(() => {
      refEvent.delete(eventId);
    }, TIME_TO_DELETE_EVENT);
  }, []);

  const isRefEvent = useCallback((eventId: string) => refEvent.get(eventId), []);

  useEventListener(SSEEventType.RESOURCE_CREATED, (event: ResourceCreatedEvent) => {
    if (isRefEvent(event.eventId)) return;

    handleRefEvent(event);

    triggerShowUpdatedMessage();

    // check if user create the file don't show toast
    if (event.userId !== AUTH.currentUser?.uid) {
      toast.success('New file added to project', {
        description: `${event.data.name} has been added to the project`,
        duration: 3000,
        position: 'bottom-right',
      });
    }

    handleRefEventTimeout(event.eventId);
  });

  useEventListener(SSEEventType.RESOURCE_UPDATED, (event: ResourceUpdatedEvent) => {
    if (isRefEvent(event.eventId)) return;

    handleRefEvent(event);

    const state = store.getState() as RootState;
    optimisticUpdateResource(dispatch, state, projectId, event.data.id, event.data.name);

    // Show toast notification if the user didn't delete the resource themselves
    if (event.userId !== AUTH.currentUser?.uid) {
      toast.info('File updated', {
        description: 'A file has been updated in the project',
        duration: 3000,
        position: 'bottom-right',
      });
    }

    handleRefEventTimeout(event.eventId);
  });

  useEventListener(SSEEventType.RESOURCE_DELETED, (event: ResourceDeletedEvent) => {
    if (isRefEvent(event.eventId)) return;

    handleRefEvent(event);

    const state = store.getState() as RootState;
    optimisticRemoveResource(dispatch, state, projectId, event.data.id);

    // Remove from selected files if it was selected
    if (selectedFileIds.has(event.data.id) && onFilesSelected) {
      const updatedSelection = selectedFiles.filter((file) => file.id !== event.data.id);
      onFilesSelected(updatedSelection);
    }

    triggerShowUpdatedMessage();

    // Show toast notification if the user didn't delete the resource themselves
    if (event.userId !== AUTH.currentUser?.uid) {
      toast.info('File deleted', {
        description: 'A file has been removed from the project',
        duration: 3000,
        position: 'bottom-right',
      });
    }

    handleRefEventTimeout(event.eventId);
  });

  useEventListener(SSEEventType.ACL_MEMBER_CHANGE_ROLE, (event: MemberChangedRoleEvent) => {
    if (isRefEvent(event.eventId)) return;

    handleRefEvent(event);

    if (event.data.userId === AUTH.currentUser?.uid) {
      refetch();
      handleRefEventTimeout(event.eventId);
    }
  });

  useEventListener(SSEEventType.PROJECT_DELETED, (event: ProjectDeletedEvent) => {
    const eventData = event.data;
    const eventId = event.eventId;

    if (isRefEvent(eventId)) return;

    handleRefEvent(event);

    refetchGetUserInitialContext() 

    const isLocationProjectDeleted = paths.project.details(eventData.projectId) === pathname;
    if (defaultProject?.id && isLocationProjectDeleted) {
      router.replace(`${paths.project.details(defaultProject.id)}`);
    }

    toast.success(`Project ${eventData.projectName} deleted`);

    handleRefEventTimeout(eventId);
  });

  useEventListener(SSEEventType.RESOURCE_MOVED_OUT_OF_PROJECT, (event: ResourceMovedEvent) => {
    const eventData = event.data;
    const eventId = event.eventId;

    if (isRefEvent(eventId)) return;

    handleRefEvent(event);

    const state = store.getState() as RootState;
    optimisticRemoveResource(dispatch, state, projectId, event.data.id);

    // Remove from selected files if it was selected
    if (selectedFileIds.has(event.data.id) && onFilesSelected) {
      const updatedSelection = selectedFiles.filter((file) => file.id !== event.data.id);
      onFilesSelected(updatedSelection);
    }

    toast.success(`File ${eventData.name} moved out of project`);

    handleRefEventTimeout(eventId);
  });

  useEventListener(SSEEventType.RESOURCE_MOVED_TO_PROJECT, (event: ResourceMovedEvent) => {
    const eventData = event.data;
    const eventId = event.eventId;

    if (isRefEvent(eventId)) return;

    triggerShowUpdatedMessage();

    toast.success(`File ${eventData.name} moved to project`);

    handleRefEventTimeout(eventId);
  });


  useEffect(() => {
    // Check if this is the initial mount or if the project actually changed
    if (prevProjectIdRef.current !== null && prevProjectIdRef.current !== projectId) {
      // Project has switched - reset everything
      onResetPagination();
      // Reset filters to expanded state (optional)
      setFiltersExpanded(INITIAL_FILTERS_EXPANDED);
    }

    // Update the ref for next comparison
    prevProjectIdRef.current = projectId;
  }, [projectId, onResetPagination]);

  // Early return for loading state
  if (isLoading) {
    return (
      <Paper
        elevation={1}
        sx={{
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          p: 2,
          width: {
            xs: '100%',
            md: '25%',
          },
          height: ['50%', '100%'],
          ...sx,
        }}
      >
        <LoadingScreen />
      </Paper>
    );
  }

  return (
    <Paper
      elevation={1}
      sx={{
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        p: 2,
        width: {
          xs: '100%',
          md: '25%',
        },
        height: ['50%', '100%'],
        ...sx,
      }}
    >
      <Stack spacing={1}>
        <FilesPanelHeader
          totalFiles={totalCount}
          filteredFiles={totalCount}
          selectedCount={selectedFiles?.length || 0}
          limit={limit || 0}
          isAllSelected={selectionState.allCurrentPageSelected || selectionState.isAllSelected}
          isIndeterminate={selectionState.isIndeterminate && !selectionState.allCurrentPageSelected}
          filtersExpanded={filtersExpanded}
          onSelectAll={handleSelectAll}
          onSelectAllResources={handleSelectAllResources}
          onClearSelection={handleClearSelection}
          onFiltersToggle={handleFiltersToggle}
          isPolling={isPolling}
          onRefetch={handleRefetch}
          shouldAnimateRefetch={shouldAnimateRefetch}
          showUpdatedMessage={showUpdatedMessage}
        />

        <FilesPanelFilters
          expanded={filtersExpanded}
          searchQuery={searchQuery}
          sortOption={sortOption}
          selectedFileTypes={selectedFileTypes}
          onSearchChange={onSearchChange}
          onSortChange={onSortChange}
          onFileTypesChange={onFileTypesChange}
        />
      </Stack>

      <Scrollbar
        sx={{
          height: '100%',
          width: '100%',
          mt: 1,
        }}
      >
        {selectionState.isEmpty ? (
          <EmptyContent
            title={deferredSearchQuery ? 'No files match your search' : 'No files available'}
            description={
              deferredSearchQuery
                ? 'Try adjusting your search terms'
                : 'Upload file or start recording to get started'
            }
            sx={{
              height: '100%',
            }}
          />
        ) : (
          <FilePreviewerProvider resources={sortedResources}>
            <List
              dense
              sx={{
                flex: 1,
                overflowY: 'auto',
                height: '100%',
                gap: 1,
                display: 'flex',
                flexDirection: 'column',
              }}
            >
              {/* Pending Uploads */}
              {pendingUploads.map((upload) => (
                <PendingUploadCard key={upload.id} data={upload} />
              ))}

              {/* On-going Sessions */}
              {ongoingSessions.map((session) => (
                <SessionCard key={session.id} session={session} />
              ))}

              {/* Files */}
              {sortedResources.map((file) => (
                <FileCard
                  key={file.id}
                  file={file}
                  onSelect={handleSelectFile}
                  isSelected={selectedFileIds.has(file.id)}
                  onItemDeleted={handleItemDeleted}
                />
              ))}
            </List>
            <FilePreviewerModal />
          </FilePreviewerProvider>
        )}
      </Scrollbar>

      {/* Pagination */}
      {totalCount > 0 && (
        <FilesPanelPagination
          page={page}
          limit={limit}
          totalCount={totalCount}
          totalPages={totalPages}
          isLoading={isFetching}
          onPageChange={onPageChange}
          onLimitChange={onLimitChange}
          onRefetch={handleRefetch}
          shouldAnimateRefetch={shouldAnimateRefetch}
        />
      )}
    </Paper>
  );
};

const FilesPanel = ({ selectedFiles, onFilesSelected, projectId, sx = {} }: FilesPanelProps) => (
  <FilesPanelProvider>
    <FilesPanelContent
      selectedFiles={selectedFiles}
      onFilesSelected={onFilesSelected}
      projectId={projectId}
      sx={sx}
    />
  </FilesPanelProvider>
);

export default FilesPanel;
