import type { SxProps } from '@mui/material';
import type { ResourceUploadQueueItem } from 'src/types';

import { useState } from 'react';
import { useDispatch } from 'react-redux';

import { Box, Card, Stack, IconButton, Typography, CircularProgress } from '@mui/material';

import { fData } from 'src/utils/format-number';
import { getDisplayFileName } from 'src/utils/file-utils';

import { removeFromUploadQueue } from 'src/store/slices/resources/slice';

import { Iconify } from 'src/components/iconify';
import TruncateTypography from 'src/components/truncate-typography';
import { fileThumb, fileFormat } from 'src/components/file-thumbnail/utils';

interface PendingUploadCardProps {
  data: ResourceUploadQueueItem;
  sx?: SxProps;
}

const PendingUploadCard = ({ data, sx }: PendingUploadCardProps) => {
  const dispatch = useDispatch();
  const [isHovered, setIsHovered] = useState(false);
  const { id, file, progress, status, cancelToken } = data;

  const format = fileFormat(file.name);

  const statusText = (() => {
    switch (status) {
      case 'pending':
        return 'Upload pending';
      case 'uploading':
        return 'Uploading...';
      case 'completed':
        return 'Completed';
      case 'processing':
        return 'Processing...';
      case 'failed':
        return 'Upload failed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return '';
    }
  })();

  const showCancelAction = isHovered && status !== 'processing';
  const shouldRemove = status && ['pending', 'completed', 'failed', 'cancelled'].includes(status);

  const handleCancel = () => {
    if (shouldRemove) {
      dispatch(removeFromUploadQueue({ id }));
      return;
    }

    cancelToken?.cancel();
  };

  return (
    <Card
      onMouseOver={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      sx={{
        width: '100%',
        bgcolor: 'background.paper',
        borderRadius: 1,
        border: '1px solid',
        borderColor: 'divider',
        boxShadow: 'none',
        ...sx,
      }}
    >
      <Stack
        direction="row"
        alignItems="center"
        spacing={2}
        sx={{
          p: 1,
        }}
      >
        <Box
          component="img"
          src={fileThumb(format)}
          sx={{
            flexShrink: 0,
            width: 24,
            height: 24,
            objectFit: 'contain',
          }}
        />

        <Stack sx={{ flexGrow: 1, minWidth: 0 }}>
          <TruncateTypography
            text={getDisplayFileName(file.name)}
            variant="subtitle1"
            fontSize={14}
            sx={{
              whiteSpace: 'nowrap',
            }}
          />

          <Stack direction="row" alignItems="center" spacing={1}>
            <Typography variant="caption" color="text.secondary">
              {fData(file.size)}
            </Typography>
            <Box
              sx={{
                width: 2,
                height: 2,
                borderRadius: '50%',
                bgcolor: 'currentColor',
              }}
            />
            <Typography variant="caption" color="text.secondary">
              {statusText}
            </Typography>
          </Stack>
        </Stack>

        <Stack direction="row" alignItems="center" sx={{ flexShrink: 0 }}>
          {showCancelAction ? (
            <IconButton onClick={handleCancel} size="small">
              <Iconify icon="material-symbols:cancel" />
            </IconButton>
          ) : (
            <Box sx={{ position: 'relative', display: 'inline-flex' }}>
              <CircularProgress
                color={progress === 100 ? 'success' : 'primary'}
                variant="determinate"
                value={progress}
                size={32}
              />
              <Box
                sx={{
                  top: 0,
                  left: 0,
                  bottom: 0,
                  right: 0,
                  position: 'absolute',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Typography
                  variant="caption"
                  component="div"
                  sx={{ color: 'text.secondary', fontSize: 8, fontWeight: 600 }}
                >{`${progress ?? 0}%`}</Typography>
              </Box>
            </Box>
          )}
        </Stack>
      </Stack>
    </Card>
  );
};

export default PendingUploadCard;
