import { alpha } from '@mui/material/styles';
import { Check as CheckIcon, Close as CloseIcon } from '@mui/icons-material';
import {
  List,
  Stack,
  Avatar,
  Divider,
  Tooltip,
  ListItem,
  Typography,
  IconButton,
  ListItemText,
  ListItemAvatar,
  CircularProgress,
} from '@mui/material';

import { RequestStatus } from 'src/store/api/projects';

import type { AccessRequestsListProps } from './types';

// ----------------------------------------------------------------------

export function AccessRequestsList({
  data,
  title,
  loading,
  onStatusChange,
  isApprovingOrRejecting = {},
  showDivider = false,
}: AccessRequestsListProps) {
  if (loading) {
    return null;
  }

  if (data.length === 0) {
    return null;
  }

  return (
    <>
      {showDivider && <Divider />}
      {title && (
        <Typography variant="subtitle2" sx={{ mt: 1 }}>
          {title}
        </Typography>
      )}
      <List
        sx={{
          width: '100%',
          maxHeight: '300px',
          overflow: 'auto',
        }}
      >
        {data.map((request) => (
          <ListItem
            key={request.id}
            secondaryAction={
              <Stack direction="row" alignItems="center" spacing={1}>
                {isApprovingOrRejecting[request.id] ? (
                  <>
                    <CircularProgress size={24} />
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      {isApprovingOrRejecting[request.id] === RequestStatus.APPROVED
                        ? 'Approving...'
                        : 'Rejecting...'}
                    </Typography>
                  </>
                ) : (
                  <Stack direction="row" spacing={1}>
                    <Tooltip title="Approve request" arrow placement="top">
                      <IconButton
                        size="small"
                        color="primary"
                        onClick={() => onStatusChange(request.id, RequestStatus.APPROVED)}
                      >
                        <CheckIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Reject request" arrow placement="top">
                      <IconButton
                        size="small"
                        color="default"
                        onClick={() => onStatusChange(request.id, RequestStatus.REJECTED)}
                        sx={{
                          '&:hover': {
                            bgcolor: (theme) => alpha(theme.palette.error.main, 0.1),
                            color: 'error.main',
                          },
                        }}
                      >
                        <CloseIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Stack>
                )}
              </Stack>
            }
          >
            <ListItemAvatar>
              <Avatar alt={request?.user?.displayName} src={request.user.photoURL}>
                {request?.user?.displayName?.charAt(0)?.toUpperCase()}
              </Avatar>
            </ListItemAvatar>
            <ListItemText
              primary={
                <Typography variant="subtitle2" noWrap>
                  {request.user.displayName}
                </Typography>
              }
              secondary={
                <Typography variant="body2" noWrap sx={{ color: 'text.secondary' }}>
                  {request.user.email}
                </Typography>
              }
            />
          </ListItem>
        ))}
      </List>
    </>
  );
}
