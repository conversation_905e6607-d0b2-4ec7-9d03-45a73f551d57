import type { PendingInvitation } from 'src/types/project';

import {
  List,
  Chip,
  Stack,
  alpha,
  Avatar,
  Button,
  Select,
  Divider,
  ListItem,
  useTheme,
  MenuItem,
  Typography,
  IconButton,
  FormControl,
  ListItemText,
  ListItemAvatar,
  CircularProgress,
} from '@mui/material';

import { ProjectRole } from 'src/store/api/projects';

import { Iconify } from 'src/components/iconify';

import { getRoleDisplay } from '../utils';

import type { InvitationsListProps } from './types';

// ----------------------------------------------------------------------

export function InvitationsList({
  data,
  title,
  loading,
  showDivider = false,
  onResend,
  isResending = {},
  onCancel,
  isCanceling = {},
  onRoleChange,
  isChangingRole = {},
}: InvitationsListProps) {
  const theme = useTheme();

  const renderLoadingState = () => (
    <Stack direction="row" alignItems="center" spacing={1}>
      <CircularProgress
        size={18}
        thickness={4}
        sx={{
          color: 'primary.main',
          animation: 'spin 1s linear infinite',
        }}
      />
      <Typography
        variant="body2"
        sx={{
          color: 'text.secondary',
          fontSize: '0.75rem',
          fontWeight: 500,
        }}
      >
        Updating...
      </Typography>
    </Stack>
  );

  const renderRoleSelector = (invitation: PendingInvitation) => (
    <FormControl size="small" sx={{ minWidth: 100 }}>
      <Select
        value={invitation.role}
        onChange={(e) => onRoleChange?.(invitation.id, e.target.value as ProjectRole)}
        variant="outlined"
        IconComponent={(props) => <Iconify icon="mingcute:down-line" {...props} />}
        sx={{
          '.MuiOutlinedInput-notchedOutline': {
            border: 'none',
          },
          borderRadius: 0,
          fontSize: '0.875rem',
          transition: theme.transitions.create(['background-color'], {
            duration: theme.transitions.duration.shorter,
          }),
          '&:hover': {
            bgcolor: alpha(theme.palette.action.hover, 0.04),
          },
          '&.Mui-focused': {
            bgcolor: 'transparent',
          },
        }}
        MenuProps={{
          PaperProps: {
            sx: {
              mt: 0.5,
              boxShadow: theme.shadows[8],
            },
          },
        }}
      >
        <MenuItem value={ProjectRole.EDITOR}>Can edit</MenuItem>
        <MenuItem value={ProjectRole.VIEWER}>Can view</MenuItem>
      </Select>
    </FormControl>
  );

  const renderExpiredChip = () => (
    <Chip
      size="small"
      label="Expired"
      icon={<Iconify icon="material-symbols:schedule" width={14} />}
      sx={{
        color: theme.palette.mode === 'dark' ? '#fff' : 'error.main',
        bgcolor: alpha(theme.palette.error.main, theme.palette.mode === 'dark' ? 0.15 : 0.08),
        border: '1px solid',
        borderColor: alpha(theme.palette.error.main, theme.palette.mode === 'dark' ? 0.4 : 0.3),
        '& .MuiChip-icon': {
          color: theme.palette.mode === 'dark' ? '#fff' : 'error.main',
        },
        '& .MuiChip-label': {
          fontWeight: 600,
          fontSize: '0.75rem',
          color: theme.palette.mode === 'dark' ? '#fff' : 'error.main',
        },
        // Professional hover effects
        transition: theme.transitions.create(['background-color', 'border-color', 'transform'], {
          duration: theme.transitions.duration.shorter,
        }),
        '&:hover': {
          bgcolor: alpha(theme.palette.error.main, theme.palette.mode === 'dark' ? 0.2 : 0.12),
          borderColor: alpha(theme.palette.error.main, theme.palette.mode === 'dark' ? 0.5 : 0.4),
          transform: 'scale(1.02)',
        },
      }}
    />
  );

  if (loading) {
    return null;
  }

  if (data.length === 0) {
    return null;
  }

  return (
    <>
      {showDivider && <Divider />}
      {title && (
        <Typography variant="subtitle2" sx={{ mt: 1 }}>
          {title}
        </Typography>
      )}
      <List
        sx={{
          width: '100%',
          maxHeight: '300px',
          overflow: 'auto',
        }}
      >
        {data.map((invitation) => (
          <ListItem
            key={invitation.id}
            secondaryAction={
              <Stack direction="row" alignItems="center" spacing={1}>
                {invitation.canResend &&
                  onResend &&
                  (isResending[invitation.id] ? (
                    <CircularProgress size={20} sx={{ color: 'primary.main' }} />
                  ) : (
                    <IconButton
                      size="small"
                      onClick={() => onResend(invitation.email)}
                      sx={{
                        color: theme.palette.mode === 'dark' ? '#fff' : 'primary.main',
                        cursor: 'pointer',
                        '&:hover': {
                          color: theme.palette.mode === 'dark' ? '#fff' : 'primary.main',
                        },
                      }}
                    >
                      <Iconify icon="mingcute:refresh-2-line" width={16} />
                    </IconButton>
                  ))}

                {/* Role selector or status display */}
                {invitation.isExpired ? (
                  renderExpiredChip()
                ) : isChangingRole[invitation.id] ? (
                  renderLoadingState()
                ) : onRoleChange ? (
                  renderRoleSelector(invitation)
                ) : (
                  <Typography
                    variant="body2"
                    noWrap
                    sx={{
                      color: 'text.secondary',
                      textTransform: 'capitalize',
                      minWidth: 'fit-content',
                      fontSize: '0.875rem',
                    }}
                  >
                    {getRoleDisplay(invitation.role)}
                  </Typography>
                )}

                {onCancel &&
                  (isCanceling[invitation.id] ? (
                    <CircularProgress size={20} sx={{ color: 'error.main' }} />
                  ) : (
                    <Button
                      size="small"
                      variant="text"
                      onClick={() => onCancel(invitation.id, invitation.email)}
                      sx={{
                        minWidth: 'auto',
                        px: 1,
                        py: 0.5,
                        textTransform: 'none',
                        '&:hover': {
                          bgcolor: alpha(theme.palette.error.main, 0.08),
                          color: 'error.main',
                        },
                      }}
                    >
                      Cancel
                    </Button>
                  ))}
              </Stack>
            }
          >
            <ListItemAvatar>
              <Avatar alt={invitation.email} src={invitation.email}>
                {invitation?.email?.charAt(0)?.toUpperCase()}
              </Avatar>
            </ListItemAvatar>
            <ListItemText primary={invitation.email} secondary="Sent" />
          </ListItem>
        ))}
      </List>
    </>
  );
}
