import type { ProjectRole } from 'src/store/api/projects';
import type { PendingInvitation } from 'src/types/project';

import type { UserListProps } from '../types';

export type InvitationsListProps = UserListProps & {
  data: PendingInvitation[];
  onResend?: (email: string) => void;
  isResending?: Record<string, boolean>;
  onCancel?: (inviteId: string, email: string) => void;
  isCanceling?: Record<string, boolean>;
  onRoleChange?: (inviteId: string, role: ProjectRole) => void;
  isChangingRole?: Record<string, boolean>;
};
