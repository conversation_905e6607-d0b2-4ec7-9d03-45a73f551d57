import type { ProjectMember } from 'src/types';

import {
  List,
  Stack,
  Avatar,
  Select,
  Divider,
  ListItem,
  MenuItem,
  Typography,
  FormControl,
  ListItemText,
  ListItemAvatar,
  CircularProgress,
} from '@mui/material';

import { ProjectRole } from 'src/store/api/projects';

import { Iconify } from 'src/components/iconify';
import { RoleBadge } from 'src/components/role-badge';

import { ListSkeleton } from '../list-skeleton';

import type { MembersListProps } from './types';

// ----------------------------------------------------------------------

export function MembersList({
  data,
  title,
  loading,
  onRoleChange,
  isRemoving = {},
  showDivider = false,
  hideActions = false,
  emptyText = 'No members',
  projectCreated,
}: MembersListProps) {
  if (loading) {
    return <ListSkeleton />;
  }

  if (data.length === 0) {
    return (
      <Typography variant="body2" sx={{ py: 2, textAlign: 'center', color: 'text.secondary' }}>
        {emptyText}
      </Typography>
    );
  }

  const Actions = ({ user }: { user: ProjectMember }) => {
    if (hideActions) {
      return null;
    }

    return (
      <Stack direction="row" alignItems="center" spacing={1}>
        {isRemoving[user.userId] ? (
          <>
            <CircularProgress size={24} />
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              Removing...
            </Typography>
          </>
        ) : (
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <Select
              value={user.role}
              onChange={(e) => onRoleChange(user.userId, e.target.value, user.user.displayName)}
              variant="outlined"
              IconComponent={(props) => <Iconify icon="mingcute:down-line" {...props} />}
              sx={{
                '.MuiOutlinedInput-notchedOutline': {
                  border: 'none',
                },
                borderRadius: 0,
              }}
            >
              <MenuItem value={ProjectRole.EDITOR}>Can edit</MenuItem>
              <MenuItem value={ProjectRole.VIEWER}>Can view</MenuItem>
              <MenuItem value="DELETE" sx={{ color: 'error.main' }}>
                Delete
              </MenuItem>
            </Select>
          </FormControl>
        )}
      </Stack>
    );
  };

  const renderItemUser = (user: ProjectMember) => (
    <ListItem key={user.id} secondaryAction={<Actions user={user} />}>
      <ListItemAvatar>
        <Avatar alt={user?.user?.displayName} src={user?.user?.photoURL}>
          {user?.user?.displayName?.charAt(0)?.toUpperCase()}
        </Avatar>
      </ListItemAvatar>
      <ListItemText
        primary={user.user.displayName || user.user.email}
        secondary={
          <Stack direction="row" alignItems="center" spacing={1}>
            <Typography variant="caption" color="text.secondary">
              {user.user.email}
            </Typography>
            <RoleBadge role={user.role} />
          </Stack>
        }
      />
    </ListItem>
  );

  const renderItemProjectCreated = () =>{
    if (!projectCreated) {
      return null;
    }

    return renderItemUser({
      id: projectCreated.uid,
      userId: projectCreated.uid,
      role: ProjectRole.OWNER,
      user: projectCreated,
    });
  }

  return (
    <>
      {showDivider && <Divider />}
      {title && (
        <Typography variant="subtitle2" sx={{ mt: 1 }}>
          {title}
        </Typography>
      )}
      <List
        sx={{
          width: '100%',
          maxHeight: '300px',
          overflow: 'auto',
        }}
      >
        {renderItemProjectCreated()}
        {data.map((user) => (
          renderItemUser(user)
        ))}
      </List>
    </>
  );
}
