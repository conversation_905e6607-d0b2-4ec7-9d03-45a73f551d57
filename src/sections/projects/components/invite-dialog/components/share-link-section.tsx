import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';
import InputAdornment from '@mui/material/InputAdornment';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

interface ShareLinkSectionProps {
  generatedShareLink: string;
  isGeneratingLink: boolean;
  onGenerateLink: () => void;
}

export function ShareLinkSection({ 
  generatedShareLink, 
  isGeneratingLink, 
  onGenerateLink 
}: ShareLinkSectionProps) {
  
  const handleCopyLink = () => {
    try {
      navigator.clipboard.writeText(generatedShareLink);
      toast.success('Link copied to clipboard');
    } catch (error) {
      console.error('Clipboard error:', error);
      toast.error('Could not copy to clipboard. Please copy manually.');
    }
  };

  const handleFocus = (event: React.FocusEvent<HTMLInputElement>) => {
    event.target.select();
  };

  return (
    <>
      {generatedShareLink ? (
        <TextField
          size="small"
          value={generatedShareLink}
          onFocus={handleFocus}
          slotProps={{
            input: {
              readOnly: true,
              endAdornment: (
                <InputAdornment position="end">
                  <Button size="small" onClick={handleCopyLink}>
                    Copy
                  </Button>
                </InputAdornment>
              ),
            },
          }}
          sx={{
            '& .MuiInputBase-input': {
              fontSize: '0.875rem',
            },
            '& .MuiInputBase-root': {
              paddingRight: '4px',
            },
            width: 350
          }}
        />
      ) : (
        <Button
          startIcon={<Iconify icon="mingcute:link-line" />}
          sx={{ mr: 'auto' }}
          onClick={onGenerateLink}
          disabled={isGeneratingLink}
        >
          {isGeneratingLink ? 'Generating...' : 'Generate share link'}
        </Button>
      )}
    </>
  );
} 