import type { MemberAddedEvent, MemberLeaveEvent, MemberRemovedEvent, MemberChangedRoleEvent, MemberAcceptInvitationEvent } from 'src/types';

import { useBoolean } from 'minimal-shared/hooks';
import { isActiveLink } from 'minimal-shared/utils';
import { useRef, useMemo, useState, useEffect, useCallback } from 'react';

import { paths } from 'src/routes/paths';
import { useParams, useRouter, usePathname } from 'src/routes/hooks';

import useUserInitialContext from 'src/hooks/user-initial-context';

import { fDateTime } from 'src/utils/format-time';

import { AUTH } from 'src/lib/firebase';
import { SSEEventType } from 'src/types';
import { useEventListener } from 'src/hooks';
import { useCreateShortlinkMutation } from 'src/store/api/url-resolver/hooks';
import {
  ProjectRole,
  RequestStatus,
  InvitationStatus,
  useInviteUserMutation,
  usePublishShareLinkMutation,
  useResendInvitationMutation,
  useCancelInvitationMutation,
  useRemoveProjectMemberMutation,
  useChangeInvitationRoleMutation,
  useApproveOrRejectAccessMutation,
  useLazyGetListAccessRequestsQuery,
  useLazyGetListProjectMembersQuery,
  useChangeRoleOfProjectMemberMutation,
  useLazyGetListPendingInvitationsQuery,
} from 'src/store/api/projects';

import { toast } from 'src/components/snackbar';

import { useAuthContext } from 'src/auth/hooks';

import { InviteContext } from './invite-context';

import type { InviteProviderProps } from '../types';
// ----------------------------------------------------------------------

// Cache duration in milliseconds (5 minutes)
const DATA_CACHE_DURATION = 5 * 60 * 1000;

export function InviteProvider({ children }: InviteProviderProps) {
  const { value: open, onTrue: onOpenDialog, onFalse: onClose } = useBoolean();
  const pathname = usePathname();
  const { id: projectId } = useParams();
  const { authenticated } = useAuthContext();
  const { projects, defaultProject, currentProject } = useUserInitialContext();
  const oldProjectId = useRef(projectId);
  const router = useRouter();

  // Data freshness tracking
  const [lastFetchTime, setLastFetchTime] = useState<number>(0);

  // Loading states for individual actions
  const [isApprovingOrRejecting, setIsApprovingOrRejecting] = useState<Record<string, string>>({});
  const [isRemoving, setIsRemoving] = useState<Record<string, boolean>>({});
  const [isResending, setIsResending] = useState<Record<string, boolean>>({});
  const [isCanceling, setIsCanceling] = useState<Record<string, boolean>>({});
  const [isChangingRole, setIsChangingRole] = useState<Record<string, boolean>>({});
  const [generatedShareLink, setGeneratedShareLink] = useState<string>('');
  const [isGeneratingLink, setIsGeneratingLink] = useState(false);

  const [inviteUserMutation] = useInviteUserMutation();
  const [changeUserRoleMutation] = useChangeRoleOfProjectMemberMutation();
  const [changeInvitationRoleMutation] = useChangeInvitationRoleMutation();
  const [publishShareLinkMutation] = usePublishShareLinkMutation();
  const [approveOrRejectAccessMutation] = useApproveOrRejectAccessMutation();
  const [removeProjectMemberMutation] = useRemoveProjectMemberMutation();
  const [resendInvitationMutation] = useResendInvitationMutation();
  const [cancelInvitationMutation] = useCancelInvitationMutation();
  const [createShortlinkMutation] = useCreateShortlinkMutation();

  // Reset states when project changes
  useEffect(() => {
    setIsApprovingOrRejecting({});
    setIsRemoving({});
    setIsResending({});
    setIsCanceling({});
    setIsChangingRole({});
    setLastFetchTime(0);
  }, [projectId]);

  // Lazy query hooks
  const [
    triggerGetProjectMembers,
    { data: projectMembers, isLoading: isMembersLoading, isFetching: isMembersFetching },
  ] = useLazyGetListProjectMembersQuery();

  const [
    triggerGetPendingInvitations,
    {
      data: pendingInvitations,
      isLoading: isInvitationsLoading,
      isFetching: isInvitationsFetching,
    },
  ] = useLazyGetListPendingInvitationsQuery();

  const [
    triggerGetAccessRequests,
    {
      data: accessRequests,
      isLoading: isAccessRequestsLoading,
      isFetching: isAccessRequestsFetching,
    },
  ] = useLazyGetListAccessRequestsQuery();

  // Check if data is fresh enough
  const isDataFresh = useCallback(() => {
    const now = Date.now();
    return now - lastFetchTime < DATA_CACHE_DURATION;
  }, [lastFetchTime]);

  // Check if we have any data loaded
  const hasData = useMemo(
    () => !!(projectMembers || pendingInvitations || accessRequests),
    [projectMembers, pendingInvitations, accessRequests]
  );

  // Function to fetch all data without opening the dialog
  const fetchUsersData = useCallback((force = false) => {
    if (!projectId) return;

    // Skip fetching if data is fresh and we're not forcing a refresh
    if (!force && isDataFresh() && hasData) {
      return;
    }

    triggerGetProjectMembers({
      id: projectId,
    });

    const isOwnerProject = currentProject?.isOwner

    if (isOwnerProject) {
      triggerGetPendingInvitations({
        id: projectId,
        query: {
          status: InvitationStatus.SENT,
        },
      });

      triggerGetAccessRequests({
        id: projectId,
        query: {
          status: RequestStatus.PENDING,
        },
      });
    }

    setLastFetchTime(Date.now());
  }, [
    projectId,
    triggerGetProjectMembers,
    triggerGetPendingInvitations,
    triggerGetAccessRequests,
    isDataFresh,
    hasData,
    currentProject?.isOwner
  ]);

  const onOpen = useCallback(() => {
    fetchUsersData();
    onOpenDialog();
  }, [fetchUsersData, onOpenDialog]);

  // Force refresh function for manual refresh
  const refreshData = useCallback(() => {
    fetchUsersData(true);
  }, [fetchUsersData]);

  const ranRef = useRef(false);
  // Separate effect for hash-based opening to avoid re-triggering when dialog opens
  useEffect(() => {
    // if the effect has already run, just open the dialog
    if (ranRef.current && !open) {
      onOpen();
      ranRef.current = false;
      return;
    }
    const currentHash = window.location.hash;
    if (!authenticated) return;

    if (currentHash === '#sharing' && authenticated) {
      onOpen();
      ranRef.current = true;
      router.cleanHash();
    }
  }, [onOpen, authenticated, router, open]);

  // Combine loading states and handle project change
  const isLoading = useMemo(() => {
    if (!projectId || !open) return false;
    if (
      oldProjectId.current !== projectId &&
      (isMembersFetching || isInvitationsFetching || isAccessRequestsFetching)
    ) {
      oldProjectId.current = projectId;
      return isMembersFetching || isInvitationsFetching || isAccessRequestsFetching;
    }
    return isMembersLoading || isInvitationsLoading || isAccessRequestsLoading;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    open,
    isMembersLoading,
    isInvitationsLoading,
    isAccessRequestsLoading,
    isMembersFetching,
    isInvitationsFetching,
    isAccessRequestsFetching,
  ]);

  const inviteUser = useCallback(
    async (email: string) => {
      if (!projectId) {
        toast.error('Cannot invite users without a project');
        return;
      }

      try {
        await inviteUserMutation({
          id: projectId,
          payload: {
            email,
            role: ProjectRole.VIEWER, // Default role for new invitees to match backend
          },
          query: {
            force: true,
          },
        }).unwrap();

        toast.success(`Invite sent to ${email}`);
      } catch (error) {
        toast.error('Couldn\'t send invite. Please try again.');
        console.error(error);
      }
    },
    [projectId, inviteUserMutation]
  );

  // Change user role
  const changeUserRole = useCallback(
    async (userId: string, role: ProjectRole) => {
      if (!projectId) {
        toast.error('Cannot change user role without a project');
        return;
      }

      try {
        await changeUserRoleMutation({
          id: projectId,
          memberId: userId,
          payload: { role },
        }).unwrap();

        toast.success('Role updated');
      } catch (error) {
        toast.error('Couldn\'t update role. Please try again.');
        console.error(error);
      }
    },
    [projectId, changeUserRoleMutation]
  );

  // Change invitation role
  const changeInvitationRole = useCallback(
    async (inviteId: string, role: ProjectRole) => {
      if (!projectId) {
        toast.error('Cannot change invitation role without a project');
        return;
      }

      try {
        setIsChangingRole((prev) => ({ ...prev, [inviteId]: true }));

        await changeInvitationRoleMutation({
          id: projectId,
          inviteId,
          payload: {
            role,
          },
        }).unwrap();

        toast.success('Invitation role updated successfully');
      } catch (error: any) {
        // More specific error handling
        const errorMessage = error?.data?.message || 'Couldn\'t update invitation role. Please try again.';
        toast.error(errorMessage);
        console.error('Change invitation role error:', error);
      } finally {
        setIsChangingRole((prev) => ({ ...prev, [inviteId]: false }));
      }
    },
    [projectId, changeInvitationRoleMutation]
  );

  const removeMember = useCallback(
    async (userId: string) => {
      if (!projectId) {
        toast.error('Cannot remove user without a project');
        return;
      }

      try {
        setIsRemoving((prev) => ({ ...prev, [userId]: true }));

        await removeProjectMemberMutation({
          id: projectId,
          memberId: userId,
        }).unwrap();

        toast.success('User removed');
      } catch (error) {
        toast.error('Couldn\'t remove user. Please refresh and try again.');
        console.error(error);
      } finally {
        setIsRemoving((prev) => ({ ...prev, [userId]: false }));
      }
    },
    [projectId, removeProjectMemberMutation]
  );

  const resendInvitation = useCallback(
    async (email: string) => {
      if (!projectId) {
        toast.error('Cannot resend invitation without a project');
        return;
      }

      try {
        setIsResending((prev) => ({ ...prev, [email]: true }));

        await resendInvitationMutation({
          id: projectId,
          payload: { email },
        }).unwrap();

        toast.success('Invite resent');
      } catch (error) {
        toast.error('Couldn\'t resend invite. Please try again.');
        console.error(error);
      } finally {
        setIsResending((prev) => ({ ...prev, [email]: false }));
      }
    },
    [projectId, resendInvitationMutation]
  );

  const cancelInvitation = useCallback(
    async (inviteId: string, email: string) => {
      if (!projectId) {
        toast.error('Cannot cancel invitation without a project');
        return;
      }

      try {
        setIsCanceling((prev) => ({ ...prev, [inviteId]: true }));

        await cancelInvitationMutation({
          id: projectId,
          inviteId,
        }).unwrap();

        toast.success('Invite canceled');
      } catch {
        toast.error('Couldn\'t cancel invite. Try again in a moment.');
      } finally {
        setIsCanceling((prev) => ({ ...prev, [inviteId]: false }));
      }
    },
    [projectId, cancelInvitationMutation]
  );

  const approveAccessRequest = useCallback(
    async (accessRequestId: string, status: RequestStatus) => {
      if (!projectId) {
        toast.error('Cannot approve access request without a project');
        return;
      }

      try {
        setIsApprovingOrRejecting((prev) => ({ ...prev, [accessRequestId]: status }));

        const isApproved = status === RequestStatus.APPROVED;

        await approveOrRejectAccessMutation({
          id: projectId,
          accessRequestId,
          payload: { isApproved },
        }).unwrap();

        toast.success(isApproved ? 'Access approved' : 'Access denied');
      } catch (error) {
        toast.error('Couldn\'t approve access. Try again or check permissions.');
        console.error(error);
      } finally {
        // remove the status from the record
        setIsApprovingOrRejecting((prev) => {
          const newState = { ...prev };
          delete newState[accessRequestId];
          return newState;
        });
      }
    },
    [projectId, approveOrRejectAccessMutation]
  );

  const copyInviteLink = useCallback(async () => {
    if (!projectId) {
      toast.error('Cannot copy invite link without a project');
      return;
    }

    try {
      setIsGeneratingLink(true);

      const shareLink = await publishShareLinkMutation({
        id: projectId!,
      }).unwrap();

      const link = `${window.location.origin}/invite-project/${projectId || 'default'}?token=${shareLink.token}`;
      const shortLink = await createShortlinkMutation({
        payload: {
          originalUrl: link,
          urlType: 'project_access_request',
          entityId: projectId,
        },
      }).unwrap();

      setGeneratedShareLink(shortLink.shortUrl);

      // Try to copy to clipboard, but don't fail if it doesn't work
      try {
        await navigator.clipboard.writeText(shortLink.shortUrl);
        toast.success('Link copied to clipboard');
      } catch (clipboardError) {
        console.error('Clipboard error:', clipboardError);
        toast.success('Link generated successfully');
      }
    } catch (error) {
      console.error('Generate share link error:', error);
      toast.error('Couldn\'t generate invite link. Please try again.');
    } finally {
      setIsGeneratingLink(false);
    }
  }, [projectId, publishShareLinkMutation, createShortlinkMutation]);

  // Reset share link when dialog closes
  const handleClose = useCallback(() => {
    setGeneratedShareLink('');
    onClose();
  }, [onClose]);

  useEventListener(SSEEventType.ACL_MEMBER_CHANGE_ROLE, (event: MemberChangedRoleEvent) => {
    if (event.data.userId === AUTH.currentUser?.uid) {
      toast.success('Your role has been updated', {
        description: `You are now a ${event.data.newRole} by ${event.data.actionUserName}`,
        duration: 3000,
      });
    } else {
      toast.success('User role has been updated', {
        description: `User ${event.data.userName} is now a ${event.data.newRole} by ${event.data.actionUserName}`,
        duration: 3000,
      });
    }
  });

  useEventListener(SSEEventType.ACL_MEMBER_NEW_MEMBER, (event: MemberAddedEvent) => {
    if (event.data.userId !== AUTH.currentUser?.uid) {
      // if the dialog is open, refetch the project members
      if (projectId && open) {
        triggerGetProjectMembers({
          id: projectId,
        });

        triggerGetPendingInvitations({
          id: projectId,
          query: {
            status: InvitationStatus.SENT,
          },
        });

        triggerGetAccessRequests({
          id: projectId,
          query: {
            status: RequestStatus.PENDING,
          },
        });
      }

      toast.success('User added to project', {
        description: `User ${event.data.userName} has been added to the project by ${event.data.addedBy}`,
        duration: 3000,
      });
    }
  });

  useEventListener(SSEEventType.ACL_MEMBER_LEAVE, (event: MemberLeaveEvent) => {
    if (event.data.userId === AUTH.currentUser?.uid) {
      toast.success('You have left the project', {
        description: `You have left the project at ${fDateTime(event.data.leftAt)}`,
        duration: 3000,
      });
    } else {
      // if the dialog is open, refetch the project members
      if (projectId && open) {
        triggerGetProjectMembers({
          id: projectId,
        });

        triggerGetPendingInvitations({
          id: projectId,
          query: {
            status: InvitationStatus.SENT,
          },
        });

        triggerGetAccessRequests({
          id: projectId,
          query: {
            status: RequestStatus.PENDING,
          },
        });
      }

      toast.success('User left the project', {
        description: `User ${event.data.userName} has left the project at ${fDateTime(event.data.leftAt)}`,
        duration: 3000,
      });
    }
  });

  useEventListener(SSEEventType.ACL_MEMBER_ACCEPT_INVITATION, (event: MemberAcceptInvitationEvent) => {
    if (event.userId === AUTH.currentUser?.uid) {
      if (projectId && open) {
        triggerGetAccessRequests({
          id: projectId,
          query: {
            status: RequestStatus.PENDING,
          },
        });
      }
      toast.success('You have accepted the invitation', {
        description: `You have accepted the invitation`,
        duration: 3000,
      });
    }
  });

  useEventListener(SSEEventType.ACL_MEMBER_REMOVED, (event: MemberRemovedEvent) => {

    if (event.data.userId === AUTH.currentUser?.uid) {
      toast.success('You have been removed from the project', {
        description: `You have been removed from the project`,
        duration: 3000,
      });


      const isProjectDetailsPage =
        projectId &&
        (isActiveLink(pathname, paths.project.details(projectId)) ||
          pathname.startsWith(paths.project.details(projectId)));

      if (isProjectDetailsPage) {
        // Redirect to first available project
        const firstProject = defaultProject || (projects && projects.length > 0 ? projects[0] : null);
        if (firstProject) {
          router.push(paths.project.details(firstProject.id));
        }
      }
    }
  });

  const contextValue = useMemo(
    () => ({
      open,
      onOpen,
      onClose: handleClose,
      fetchUsersData,
      refreshData,
      users: projectMembers || [],
      pendingInvitations: pendingInvitations || [],
      accessRequests: accessRequests || [],
      isLoading,
      isApprovingOrRejecting,
      isRemoving,
      isResending,
      isCanceling,
      isChangingRole,
      generatedShareLink,
      isGeneratingLink,
      inviteUser,
      changeUserRole,
      changeInvitationRole,
      copyInviteLink,
      approveAccessRequest,
      removeMember,
      resendInvitation,
      cancelInvitation,
    }),
    [
      open,
      onOpen,
      handleClose,
      fetchUsersData,
      refreshData,
      projectMembers,
      pendingInvitations,
      accessRequests,
      isLoading,
      isApprovingOrRejecting,
      isRemoving,
      isResending,
      isCanceling,
      isChangingRole,
      generatedShareLink,
      isGeneratingLink,
      inviteUser,
      changeUserRole,
      changeInvitationRole,
      copyInviteLink,
      approveAccessRequest,
      removeMember,
      resendInvitation,
      cancelInvitation,
    ]
  );

  return <InviteContext.Provider value={contextValue}>{children}</InviteContext.Provider>;
}
