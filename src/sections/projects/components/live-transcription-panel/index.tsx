import type { SxProps } from '@mui/material';
import type { LiveTranscriptionResult, LiveTranscriptionSegment } from 'src/types';

import { useDispatch } from 'react-redux';
import { useEffect, useCallback } from 'react';

import { Box, Paper, Stack, IconButton, Typography } from '@mui/material';

import { useLiveTranscription } from 'src/hooks/use-live-transcription';

import { setIsLiveTranscription } from 'src/store/slices/live-transcription/slice';

import { Iconify } from 'src/components/iconify';

import DeviceStatus from './device-status';
import RecordingControls from './recording-controls';
import AudioDeviceSelector from './audio-device-selector';
import TranscriptionDisplay from './transcription-display';

interface Props {
  sx?: SxProps;
  onTranscriptData?: (segments: LiveTranscriptionSegment[]) => void;
  onRecordingComplete?: (result: LiveTranscriptionResult) => void;
}

const LiveTranscriptionPanel: React.FC<Props> = ({
  sx = {},
  onTranscriptData,
  onRecordingComplete,
}) => {
  const dispatch = useDispatch();

  const {
    bootstrap,
    audioDevices,
    selectedAudioDeviceId,
    switchAudioDevice,
    state,
    startTranscription,
    stopTranscription,
    clearTranscription,
  } = useLiveTranscription();

  useEffect(() => {
    bootstrap();
  }, [bootstrap]);

  // Communicate transcript data back to parent component
  useEffect(() => {
    if (onTranscriptData && state.segments.length > 0) {
      onTranscriptData(state.segments);
    }
  }, [onTranscriptData, state.segments]);

  const handleDeviceChange = useCallback(
    (deviceId: string) => {
      switchAudioDevice(deviceId).catch(console.error);
    },
    [switchAudioDevice]
  );

  const handleStartRecording = useCallback(async () => {
    try {
      await startTranscription({
        language: 'en',
        enablePartialResults: true,
        enableSpeakerDiarization: true, // Enable speaker diarization for meeting participants
        enableProfanityFilter: false,
      });
    } catch (error) {
      console.error('Failed to start recording:', error);
    }
  }, [startTranscription]);

  const handleStopRecording = useCallback(async () => {
    try {
      stopTranscription();
    } catch (error) {
      console.error('Failed to stop recording:', error);
    }
  }, [stopTranscription]);

  const handleEndSession = useCallback(async () => {
    // Stop recording if active
    if (state.isRecording) {
      const result = await stopTranscription();
      // Call the callback if provided
      if (onRecordingComplete) {
        onRecordingComplete(result);
        clearTranscription();
      }
    } else clearTranscription();
    // Exit live transcription mode
    dispatch(setIsLiveTranscription(false));
  }, [state.isRecording, clearTranscription, dispatch, stopTranscription, onRecordingComplete]);

  return (
    <Paper
      elevation={1}
      sx={{
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        p: 2,
        width: {
          xs: '100%',
          md: '25%',
        },
        height: ['50%', '100%'],
        ...sx,
      }}
    >
      {/* Header */}
      <Box>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Stack direction="row" alignItems="center" spacing={1}>
            <Typography variant="subtitle1" color="textPrimary" sx={{ fontSize: 14 }}>
              Live Transcription
            </Typography>
            <DeviceStatus
              isRecording={state.isRecording}
              isConnected={state.isConnected}
              connectionState={state.connectionState}
              error={state.error}
            />
          </Stack>
          <IconButton onClick={handleEndSession} sx={{ p: 0 }}>
            <Iconify icon="material-symbols:close" width={20} />
          </IconButton>
        </Stack>
      </Box>

      {/* Audio Device Selection */}
      <AudioDeviceSelector
        audioDevices={audioDevices}
        selectedAudioDeviceId={selectedAudioDeviceId || ''}
        onDeviceChange={handleDeviceChange}
        sx={{ mt: state.isRecording ? 2 : 1 }}
      />

      {/* Recording Controls */}
      <RecordingControls
        isRecording={state.isRecording}
        connectionState={state.connectionState}
        error={state.error}
        onStartRecording={handleStartRecording}
        onStopRecording={handleStopRecording}
        onEndSession={handleEndSession}
        sx={{ mt: 1.5 }}
      />

      {/* Transcription Display */}
      <TranscriptionDisplay segments={state.segments} sx={{ mt: 1.5, flex: 1 }} />
    </Paper>
  );
};

export default LiveTranscriptionPanel;
