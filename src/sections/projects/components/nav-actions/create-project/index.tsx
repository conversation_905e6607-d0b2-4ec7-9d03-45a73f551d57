import { useState } from 'react';

import Box from '@mui/material/Box';
import Tooltip from '@mui/material/Tooltip';
import IconButton from '@mui/material/IconButton';

import { Iconify } from 'src/components/iconify';

import CreateProjectDialog from 'src/sections/projects/components/create-project-dialog';

// ----------------------------------------------------------------------

const CreateProjectNavAction = () => {
  const [open, setOpen] = useState(false);

  const handleOpen = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <>
      <Tooltip title="Create Project">
        <Box onClick={(e) => e.stopPropagation()} onMouseDown={(e) => e.stopPropagation()}>
          <IconButton size="small" onClick={handleOpen} onMouseDown={(e) => e.stopPropagation()}>
            <Iconify icon="material-symbols:add" width={16} />
          </IconButton>
        </Box>
      </Tooltip>

      {open && <CreateProjectDialog open={open} onClose={handleClose} />}
    </>
  );
};

export default CreateProjectNavAction;
