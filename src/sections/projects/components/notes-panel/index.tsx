import type { SxProps } from '@mui/material';
import type { Note } from 'src/store/api/notes/types';
import type { ProjectRole } from 'src/store/api/projects';
import type { NoteCreatedEvent, NoteDeletedEvent, NoteUpdatedEvent} from 'src/types';

import { toast } from 'sonner';
import { useDispatch } from 'react-redux';
import { useMemo, useState, useEffect, useCallback } from 'react';

import { Stack, Paper } from '@mui/material';

import { useEventListener } from 'src/hooks/use-event-listener';

import { SSEEventType } from 'src/types';
import { useAppSelector } from 'src/store';
import { selectSelectedNote } from 'src/store/slices/notes/selectors';
import { addNote, deleteNote, selectNote, storeNotes } from 'src/store/slices/notes/slice';
import {
  useCreateNoteMutation,
  useDeleteNoteMutation,
  useGetNotesByProjectQuery,
} from 'src/store/api/notes';

import { NoteDialog } from 'src/components/notes';
import { Scrollbar } from 'src/components/scrollbar';
import { NoteList } from 'src/components/notes/note-list';
import { EmptyContent } from 'src/components/empty-content';
import { LoadingScreen } from 'src/components/loading-screen';

import NotesPanelHeader from './notes-panel-header';
import NotesPanelFilters, { type NoteSortOption } from './notes-panel-filters';

interface NotesSectionProps {
  projectId: string;
  canEdit?: boolean;
  sx?: SxProps;
  role?: ProjectRole | string;
  isOwner?: boolean;
  currentUserId?: string;
}

const NotesPanel: React.FC<NotesSectionProps> = ({
  projectId,
  canEdit = true,
  sx = {},
  role,
  isOwner = false,
  currentUserId = '',
}) => {
  const dispatch = useDispatch();
  const selectedNote = useAppSelector(selectSelectedNote);

  // Search and sort state
  const [searchQuery, setSearchQuery] = useState('');
  const [sortOption, setSortOption] = useState<NoteSortOption>('latest');
  const [filtersExpanded, setFiltersExpanded] = useState(false);

  const [createNote, { isLoading: isCreating }] = useCreateNoteMutation();
  const [deleteNoteMutation] = useDeleteNoteMutation();
  const {
    data: apiNotes = [],
    isLoading: isApiLoading,
    refetch,
  } = useGetNotesByProjectQuery(
    { projectId },
    {
      refetchOnMountOrArgChange: true,
    }
  );

  useEffect(() => {
    if (!isApiLoading && apiNotes) {
      dispatch(storeNotes(apiNotes));
    }
  }, [dispatch, isApiLoading, apiNotes]);

  // Memoize filtering and sorting logic
  const { filteredAndSortedNotes, isEmpty } = useMemo(() => {
    // Filter notes by search query
    const filteredNotes = apiNotes.filter((note) => {
      const searchLower = searchQuery.toLowerCase();
      return (
        note.title.toLowerCase().includes(searchLower) ||
        note.content.toLowerCase().includes(searchLower)
      );
    });

    // Sort notes based on selected option
    const sortedNotes = [...filteredNotes].sort((a, b) => {
      switch (sortOption) {
        case 'latest':
          return (
            new Date(b.updatedAt || b.createdAt).getTime() -
            new Date(a.updatedAt || a.createdAt).getTime()
          );
        case 'oldest':
          return (
            new Date(a.updatedAt || a.createdAt).getTime() -
            new Date(b.updatedAt || b.createdAt).getTime()
          );
        case 'title-asc':
          return a.title.localeCompare(b.title);
        case 'title-desc':
          return b.title.localeCompare(a.title);
        default:
          return (
            new Date(b.updatedAt || b.createdAt).getTime() -
            new Date(a.updatedAt || a.createdAt).getTime()
          );
      }
    });

    return {
      filteredAndSortedNotes: sortedNotes,
      isEmpty: sortedNotes.length === 0,
    };
  }, [apiNotes, searchQuery, sortOption]);

  const handleSelectNote = (note: Note) => {
    dispatch(selectNote(note));
  };

  const handleCloseDialog = () => {
    refetch();
    dispatch(selectNote(null));
  };

  const handleAddNewNote = useCallback(() => {
    createNote({
      payload: {
        content: '',
        title: 'New Note',
        projectId,
      },
    })
      .then((res) => {
        if (res.data) {
          handleSelectNote(res.data);
          dispatch(addNote(res.data));
        }
      })
      .catch((err) => {
        toast.error('Failed to add new note');
      });
  }, [createNote, projectId, handleSelectNote]);

  const handleDelete = async (noteId: string) => {
    try {
      await deleteNoteMutation({ id: noteId });
      dispatch(deleteNote(noteId));
      await refetch();
      toast.success('Note deleted successfully');
    } catch (error) {
      toast.error('Failed to delete note');
    }
  };

  // Memoize event handlers
  const handleSearchChange = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  const handleSortChange = useCallback((option: NoteSortOption) => {
    setSortOption(option);
  }, []);

  const handleFiltersToggle = useCallback(() => {
    setFiltersExpanded(!filtersExpanded);
  }, [filtersExpanded]);

  useEventListener(SSEEventType.NOTE_CREATED,  async (event: NoteCreatedEvent) => {
    // check if the note is not created by the current user
    if (event.data.createdById !== currentUserId) {
      await refetch();
      toast.success('New note created', {
        description: `New note created by ${event.data.createdBy}`,
      });
    }
  });

  useEventListener(SSEEventType.NOTE_UPDATED,  async (event: NoteUpdatedEvent) => {
    // check if the note is not updated by the current user
    if (event.data.updatedById !== currentUserId) {
      await refetch();
      toast.success('Note updated', {
        description: `Note updated by ${event.data.updatedBy}`,
      });
    }
  });

  useEventListener(SSEEventType.NOTE_DELETED, async (event: NoteDeletedEvent) => {
    // check if the note is not deleted by the current user
    if (event.data.deletedById !== currentUserId) {
      await refetch();
      toast.success('Note deleted', {
        description: `Note deleted by ${event.data.deletedBy}`,
      });
    }
  });

  return (
    <>
      <Paper
        elevation={1}
        sx={{
          overflow: 'hidden',
          height: ['50%', '100%'],
          display: 'flex',
          flexDirection: 'column',
          p: 2,
          width: {
            xs: '100%',
            md: '25%',
          },
          ...sx,
        }}
      >
        {isApiLoading ? (
          <LoadingScreen />
        ) : (
          <>
            <Stack spacing={1}>
              <NotesPanelHeader
                totalNotes={apiNotes.length}
                filteredNotes={filteredAndSortedNotes.length}
                filtersExpanded={filtersExpanded}
                canEdit={canEdit}
                isCreating={isCreating}
                onFiltersToggle={handleFiltersToggle}
                onAddNote={canEdit ? handleAddNewNote : undefined}
              />
              <NotesPanelFilters
                expanded={filtersExpanded}
                searchQuery={searchQuery}
                sortOption={sortOption}
                onSearchChange={handleSearchChange}
                onSortChange={handleSortChange}
              />
            </Stack>
            <Scrollbar sx={{ height: '100%', mt: 1 }}>
              {isEmpty ? (
                <EmptyContent
                  title={searchQuery ? 'No notes match your search' : 'No notes available'}
                  description={
                    searchQuery
                      ? 'Try adjusting your search terms'
                      : 'Create your first note to get started'
                  }
                  sx={{
                    height: '100%',
                  }}
                />
              ) : (
                <NoteList
                  notes={filteredAndSortedNotes}
                  onSelectNote={handleSelectNote}
                  onDelete={handleDelete}
                  onAddNote={canEdit ? handleAddNewNote : undefined}
                  isCanDeleteResource={canEdit}
                  role={role}
                  isOwner={isOwner}
                  currentUserId={currentUserId}
                />
              )}
            </Scrollbar>
          </>
        )}
      </Paper>

      {/* Note Dialog */}
      <NoteDialog
        open={Boolean(selectedNote)}
        onClose={handleCloseDialog}
        isCanEdit={canEdit}
        role={role}
        isOwner={isOwner}
        currentUserId={currentUserId}
      />
    </>
  );
};

export default NotesPanel;
