import type { SelectChangeEvent } from '@mui/material';

import {
  Stack,
  Select,
  MenuItem,
  Collapse,
  TextField,
  InputLabel,
  IconButton,
  FormControl,
  InputAdornment,
} from '@mui/material';

import { Iconify } from 'src/components/iconify';

export type NoteSortOption = 'latest' | 'oldest' | 'title-asc' | 'title-desc';

interface NotesPanelFiltersProps {
  expanded: boolean;
  searchQuery: string;
  sortOption: NoteSortOption;
  onSearchChange: (query: string) => void;
  onSortChange: (option: NoteSortOption) => void;
}

const sortOptions = [
  { value: 'latest' as const, label: 'Latest' },
  { value: 'oldest' as const, label: 'Oldest' },
  { value: 'title-asc' as const, label: 'Title (A-Z)' },
  { value: 'title-desc' as const, label: 'Title (Z-A)' },
];

const NotesPanelFilters = ({
  expanded,
  searchQuery,
  sortOption,
  onSearchChange,
  onSortChange,
}: NotesPanelFiltersProps) => {
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onSearchChange(event.target.value);
  };

  const handleSortChange = (event: SelectChangeEvent<NoteSortOption>) => {
    onSortChange(event.target.value as NoteSortOption);
  };

  return (
    <Collapse in={expanded} timeout="auto" unmountOnExit>
      <Stack direction="row" spacing={1}>
        <TextField
          size="small"
          placeholder="Search notes..."
          value={searchQuery}
          onChange={handleSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Iconify icon="material-symbols:search" />
              </InputAdornment>
            ),
            endAdornment: searchQuery && (
              <InputAdornment position="end">
                <IconButton size="small" onClick={() => onSearchChange('')} sx={{ p: 0.5 }}>
                  <Iconify icon="material-symbols:close" />
                </IconButton>
              </InputAdornment>
            ),
          }}
          sx={{ flexGrow: 1 }}
        />

        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>Sort by</InputLabel>
          <Select value={sortOption} onChange={handleSortChange} label="Sort by">
            {sortOptions.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Stack>
    </Collapse>
  );
};

export default NotesPanelFilters;
