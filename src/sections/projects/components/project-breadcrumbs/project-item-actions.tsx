import type { UserProject } from 'src/types/user';

import { useMemo, useCallback } from 'react';
import { useBoolean, usePopover } from 'minimal-shared/hooks';

import { Toolt<PERSON>, Divider, MenuItem, MenuList, IconButton } from '@mui/material';

import { Iconify } from 'src/components/iconify';
import { CustomPopover } from 'src/components/custom-popover';

import LeaveProjectDialog from '../dialogs/leave-dialog';
import RenameProjectDialog from '../dialogs/rename-dialog';
import RemoveProjectDialog from '../dialogs/remove-dialog';

const ProjectItemActions: React.FC<{ data: UserProject }> = ({ data }) => {
  const menuActions = usePopover();
  const renameProjectDialog = useBoolean();
  const removeProjectDialog = useBoolean();
  const leaveProjectDialog = useBoolean();

  const isOwner = data.isOwner;
  const isDefault = data.isDefault;

  const isHasActions = useMemo(() => {
    const canRename = isOwner && !isDefault;
    const canDelete = isOwner && !isDefault;
    const canLeave = !isOwner;

    return canRename || canDelete || canLeave;
  }, [isOwner, isDefault]);

  const renderRenameProjectMenuItem = useCallback(() => {
    if (isDefault || !isOwner) return null;
    return (
      <>
        <MenuItem
          onClick={(evt) => {
            evt.stopPropagation();
            renameProjectDialog.onTrue();
            menuActions.onClose();
          }}
        >
          <Iconify icon="material-symbols:edit" />
          Rename
        </MenuItem>
        <Divider />
      </>
    );
  }, [isDefault, isOwner, menuActions, renameProjectDialog]);

  const renderDeleteProjectMenuItem = useCallback(() => {
    if (isDefault || !isOwner) return null;

    return (
      <MenuItem
        onClick={(evt) => {
          evt.stopPropagation();
          removeProjectDialog.onTrue();
          menuActions.onClose();
        }}
        sx={{ color: 'error.main' }}
      >
        <Iconify icon="material-symbols:delete" />
        Delete
      </MenuItem>
    );
  }, [isDefault, isOwner, menuActions, removeProjectDialog]);

  const renderLeaveProjectMenuItem = useCallback(() => {
    if (isOwner) return null;
    return (
      <MenuItem
        onClick={(evt) => {
          evt.stopPropagation();
          leaveProjectDialog.onTrue();
          menuActions.onClose();
        }}
        sx={{ color: 'inherit' }}
      >
        <Iconify icon="material-symbols:logout" />
        Leave Project
      </MenuItem>
    );
  }, [isOwner, menuActions, leaveProjectDialog]);

  if (!isHasActions) return null;

  return (
    <>
      <Tooltip title="More actions" placement="top" arrow>
        <IconButton
          disableRipple
          sx={{
            py: 0,
          }}
          onClick={(evt) => {
            evt.stopPropagation();
            evt.preventDefault();
            evt.nativeEvent.stopImmediatePropagation();
            menuActions.onOpen(evt);
          }}
          onMouseDown={(evt) => {
            evt.stopPropagation();
            evt.preventDefault();
          }}
        >
          <Iconify icon="material-symbols:more-vert" />
        </IconButton>
      </Tooltip>

      <CustomPopover
        open={menuActions.open}
        anchorEl={menuActions.anchorEl}
        onClose={(event: {}, reason: 'backdropClick' | 'escapeKeyDown') => {
          if (event && 'stopPropagation' in event) {
            (event as React.MouseEvent).stopPropagation();
            (event as React.MouseEvent).preventDefault();
          }
          menuActions.onClose();
        }}
        slotProps={{
          arrow: { placement: 'top-left' },
          paper: {
            onClick: (evt) => {
              evt.stopPropagation();
              evt.preventDefault();
            },
            onMouseDown: (evt) => {
              evt.stopPropagation();
              evt.preventDefault();
            },
          },
        }}
      >
        <MenuList>
          {renderRenameProjectMenuItem()}
          {renderDeleteProjectMenuItem()}
          {renderLeaveProjectMenuItem()}
        </MenuList>
      </CustomPopover>

      <RenameProjectDialog
        open={renameProjectDialog.value}
        onClose={renameProjectDialog.onFalse}
        project={data}
      />

      <RemoveProjectDialog
        open={removeProjectDialog.value}
        onClose={removeProjectDialog.onFalse}
        project={data}
      />

      <LeaveProjectDialog
        open={leaveProjectDialog.value}
        onClose={leaveProjectDialog.onFalse}
        project={data}
      />
    </>
  );
};

export default ProjectItemActions;
