import axios from 'axios';
import { useState, useEffect, useCallback } from 'react';

import { Box, Button, Typography } from '@mui/material';

import { paths } from 'src/routes/paths';
import { useRouter } from 'src/routes/hooks';

import useResponsive from 'src/hooks/use-responsive';

import { canManageNotes, getProjectPermissions } from 'src/utils/permissions';

import { AUTH } from 'src/lib/firebase';
import { useAppSelector } from 'src/store';
import { ProjectSSEProvider } from 'src/contexts';
import { useGetResourceUploadUrlMutation } from 'src/store/api/resources';
import { selectIsLiveTranscription } from 'src/store/slices/live-transcription/selectors';
import { type Resource, type LiveTranscriptionResult, type LiveTranscriptionSegment } from 'src/types';
import {
  useGetProjectInfoQuery,
  useGetProjectMembershipQuery,
  useCreateProjectResourceWithTranscriptionMutation,
} from 'src/store/api/projects';

import ChatPanel from 'src/sections/projects/components/chat-panel';
import FilesPanel from 'src/sections/projects/components/files-panel';
import NotesPanel from 'src/sections/projects/components/notes-panel';
import LiveTranscriptionPanel from 'src/sections/projects/components/live-transcription-panel';

import AidaSuggestionsPanel from '../components/aida-suggestions-panel';

const ProjectDetailsView: React.FC<{ projectId: string }> = ({ projectId }) => {
  const { isMobile } = useResponsive();
  const [selectedFiles, setSelectedFiles] = useState<Resource[]>([]);
  const [recordingCompleted, setRecordingCompleted] = useState(false);
  // Store transcript data for future use (e.g., saving to notes, AI analysis, export)
  const [transcriptData, setTranscriptData] = useState<LiveTranscriptionSegment[]>([]);
  const isLiveTranscription = useAppSelector(selectIsLiveTranscription);
  const [createResourceWithTranscription] = useCreateProjectResourceWithTranscriptionMutation();
  const [getResourceUploadUrl] = useGetResourceUploadUrlMutation();
  const userId = AUTH.currentUser?.uid ?? 'user';
  const router = useRouter();

  const {
    data: project,
    error: cannotGetProjectError,
    isLoading: isLoadingProject,
    refetch: refetchProject,
  } = useGetProjectInfoQuery(
    { id: projectId,  },
    {
      skip: !projectId,
    }
  );

  const { data: projectMembership, refetch: refetchProjectMembership } = useGetProjectMembershipQuery(
    { id: projectId },
    {
      skip: !projectId,
      refetchOnFocus: false,
    }
  );



  const isOwner = project?.createdById === userId;
  const permissions = getProjectPermissions(projectMembership?.role, isOwner);
  const canEdit = permissions.canEdit;
  const canComment = canManageNotes(projectMembership?.role, isOwner);
  // Reset selected files when projectId changes
  useEffect(() => {
    setSelectedFiles([]);
  }, [projectId]);

  // Handle transcript data from live transcription
  const handleTranscriptData = useCallback((segments: LiveTranscriptionSegment[]) => {
    setRecordingCompleted(false);
    setTranscriptData(segments);
  }, []);

  // Handle recording completion (when user stops transcription)
  const handleRecordingComplete = useCallback(
    async (result: LiveTranscriptionResult) => {
      try {
        const audioBlob: Blob = result.audioFile;
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const fileName = `recording-${timestamp}.webm`;
        const file = new File([audioBlob], fileName, { type: 'audio/webm' });
        const { fields, url } = await getResourceUploadUrl({
          payload: {
            fileName: file.name,
          },
        }).unwrap();
        const uploadedFileName = fields['x-ignore-file-name'];
        const formData = new FormData();

        Object.entries({ ...fields, file }).forEach(([key, value]) => {
          formData.append(key, value as any);
        });

        await axios.post(url, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });

        await createResourceWithTranscription({
          id: projectId,
          payload: {
            fileName: file.name,
            fileSize: file.size,
            fileLastModified: new Date(file.lastModified).toISOString(),
            uploadedFileName,
            transcript: result.transcriptData,
          },
        }).unwrap();
        setRecordingCompleted(true);
      } catch (error) {
        console.error('Error handling recording completion:', error);
        // Handle error (e.g., show notification, retry logic)
      }
    },
    [createResourceWithTranscription, getResourceUploadUrl, projectId]
  );
  if (isLoadingProject) {
    return null;
  }

  if (cannotGetProjectError) {
    return (
      <Box
        sx={{
          height: {
            xs: 'calc(100vh - var(--layout-header-mobile-height))',
            md: 'calc(100vh - var(--layout-header-desktop-height))',
          },
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          gap: 3,
          px: 2,
        }}
      >
        <Typography
          variant="h6"
          fontSize="2.5rem !important"
          sx={(theme) => ({
            color: theme.vars.palette.text.secondary,
            textAlign: 'center',
            fontWeight: 600,
            lineHeight: 1.2,
            opacity: 0.8,
          })}
        >
          Access denied or project not found
        </Typography>
        <Button
          variant="outlined"
          color="secondary"
          onClick={() => router.push(paths.project.root)}
          sx={{
            minWidth: 120,
          }}
        >
          Back to latest project
        </Button>
      </Box>
    );
  }

  return (
    <ProjectSSEProvider
      projectId={projectId}
      refetchProject={refetchProject}
      refetchProjectMembership={refetchProjectMembership}
    >
      <Box
      sx={{
        height: {
          xs: 'calc(100vh - var(--layout-header-mobile-height))',
          md: 'calc(100vh - var(--layout-header-desktop-height))',
        },
        overflow: {
          xs: 'auto',
          md: 'hidden',
        },
        display: 'flex',
        flexDirection: {
          xs: 'column',
          md: 'row',
        },
        gap: 2,
        position: 'fixed',
        px: 2,
        pb: 2,
        top: {
          xs: 'var(--layout-header-mobile-height)',
          md: 'var(--layout-header-desktop-height)',
        },
        left: 0,
        right: 0,
        bottom: 0,
      }}
    >
      
        {isLiveTranscription ? (
          <LiveTranscriptionPanel
            onTranscriptData={handleTranscriptData}
            onRecordingComplete={handleRecordingComplete}
          />
        ) : (
          <FilesPanel
            selectedFiles={selectedFiles}
            onFilesSelected={setSelectedFiles}
            projectId={projectId}
          />
        )}
        {isMobile ? (
          <>
            <NotesPanel
              projectId={projectId}
              canEdit={canComment}
              role={projectMembership?.role}
              isOwner={isOwner}
              currentUserId={userId}
            />
            {isLiveTranscription ? (
              <AidaSuggestionsPanel
                transcriptions={transcriptData}
                recordingCompleted={recordingCompleted}
              />
            ) : (
              <ChatPanel selectedFiles={selectedFiles} projectId={projectId} canEdit={canEdit} />
            )}
          </>
        ) : (
          <>
            {isLiveTranscription ? (
              <AidaSuggestionsPanel
                transcriptions={transcriptData}
                recordingCompleted={recordingCompleted}
              />
            ) : (
              <ChatPanel selectedFiles={selectedFiles} projectId={projectId} canEdit={canEdit} />
            )}
            <NotesPanel
              projectId={projectId}
              canEdit={canComment}
              role={projectMembership?.role}
              isOwner={isOwner}
              currentUserId={userId}
            />
          </>
        )}
      </Box>
    </ProjectSSEProvider>
  );
};

export default ProjectDetailsView;
