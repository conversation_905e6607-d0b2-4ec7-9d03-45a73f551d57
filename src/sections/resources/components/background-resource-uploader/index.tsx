import type { ResourceUploadQueueItem } from 'src/types';

import axios from 'axios';
import { toast } from 'sonner';
import { useEffect } from 'react';
import { useSelector } from 'react-redux';

import useAnalytics from 'src/hooks/analytics';
import { useUploadCache } from 'src/hooks/use-upload-cache';
import { useUploadQueueCacheSync } from 'src/hooks/use-upload-queue-cache-sync';

import axiosInstance from 'src/lib/axios';
import { useCreateResourceMutation } from 'src/store/api/resources';
import { selectResourceUploadQueue } from 'src/store/slices/resources/selectors';

import { fileFormat, fileTypeByUrl } from 'src/components/file-thumbnail/utils';

const BackgroundResourceUploader = () => {
  const { trackEvent } = useAnalytics();
  const [triggerCreateResource] = useCreateResourceMutation();

  const uploadQueue = useSelector(selectResourceUploadQueue);
  const { updateUploadQueueItemWithCache, clearCompletedUploadsWithCache } =
    useUploadQueueCacheSync();

  const { clearStaleUploads } = useUploadCache();

  const updateQueueItem = async (id: string, data: Partial<ResourceUploadQueueItem>) => {
    try {
      await updateUploadQueueItemWithCache(id, data);
    } catch (error) {
      console.error('Failed to update queue item:', error);
    }
  };

  const processQueueItem = async (item: ResourceUploadQueueItem) => {
    const { file, uploadUrlData, projectId, folderId } = item;
    const { fields, url } = uploadUrlData;
    const uploadedFileName = fields['x-ignore-file-name'];
    const formData = new FormData();

    Object.entries({ ...fields, file }).forEach(([key, value]) => {
      formData.append(key, value as any);
    });

    const fileType = fileTypeByUrl(file.name);
    const fFormat = fileFormat(file.name);
    const isAudioOrVideo = fFormat === 'audio' || fFormat === 'video';

    try {
      const source = axios.CancelToken.source();
      trackEvent({
        eventCategory: 'Resource',
        eventAction: 'Upload file',
        properties: {
          fileName: file.name,
          fileSize: file.size,
        },
      });
      await updateQueueItem(item.id, { status: 'uploading', cancelToken: source });
      await axiosInstance.post(url, formData, {
        cancelToken: source.token,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event?.progress ?? 0) * 100);
            updateQueueItem(item.id, {
              progress,
            }).catch(console.error);
          }
        },
      });

      // Trigger resource creation
      await updateQueueItem(item.id, { status: 'processing' });

      const payload = {
        fileName: file.name,
        fileSize: file.size,
        fileLastModified: new Date(file.lastModified).toISOString(),
        uploadedFileName,
        projectId,
        folderId,
        transcoded: item.transcoded,
        mode: item.mode,
      };

      await triggerCreateResource({
        payload,
      }).unwrap();

      trackEvent({
        eventCategory: 'Resource',
        eventAction: 'File uploaded successfully',
        properties: {
          ...payload,
          fileType,
          isAudioOrVideo,
        },
      });

      // Update item as completed
      await updateQueueItem(item.id, { status: 'completed' });

      toast.success('File uploaded', {
        description: `${file.name} has been uploaded successfully`,
        duration: 10000,
        position: 'bottom-right',
      });

      // Auto-cleanup completed uploads after 3 seconds to prevent duplicate display
      setTimeout(() => {
        clearCompletedUploadsWithCache();
      }, 3000);
    } catch (error: any) {
      if (axios.isCancel(error)) {
        await updateQueueItem(item.id, { status: 'cancelled' });
        console.log('Upload cancelled for:', item.file.name);
      } else {
        console.error('Error uploading file:', error);
        await updateQueueItem(item.id, { status: 'failed' });
        toast.error('Failed to upload', {
          description: `Something wrong when uploading file: ${file.name}`,
          position: 'bottom-right',
        });
      }
    }
  };

  const processQueue = () => {
    uploadQueue.forEach((item, idx) => {
      const prevItem = uploadQueue[idx - 1];
      const isPrevItemCompleted = prevItem
        ? prevItem.status && ['completed', 'failed', 'cancelled'].includes(prevItem.status)
        : true;

      if (isPrevItemCompleted && item.status === 'pending') {
        processQueueItem(item);
      }
    });
  };

  useEffect(() => {
    if (!uploadQueue.length) return;
    processQueue();
  }, [uploadQueue]);

  // Cleanup completed uploads periodically to prevent accumulation
  useEffect(() => {
    const cleanupInterval = setInterval(async () => {
      const hasCompletedUploads = uploadQueue.some((item) => item.status === 'completed');
      if (hasCompletedUploads) {
        clearCompletedUploadsWithCache();
      }

      // Also cleanup stale uploads (100% progress but not completed)
      await clearStaleUploads();
    }, 5000); // Check every 5 seconds

    return () => clearInterval(cleanupInterval);
  }, [uploadQueue, clearCompletedUploadsWithCache, clearStaleUploads]);

  return null;
};

export default BackgroundResourceUploader;
