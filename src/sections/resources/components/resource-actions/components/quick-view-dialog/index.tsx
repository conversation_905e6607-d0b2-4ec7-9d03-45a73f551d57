import type { DialogProps } from '@mui/material';
import type { ResourceItem } from 'src/sections/resources/components/resources-list';

import { useState, useEffect, useCallback } from 'react';

import {
  Box,
  Stack,
  Dialog,
  IconButton,
  Typography,
  DialogTitle,
  DialogContent,
} from '@mui/material';

import { hasFileExtension } from 'src/utils/file-utils';

import { Editor } from 'src/components/editor';
import { Iconify } from 'src/components/iconify';
import { LoadingScreen } from 'src/components/loading-screen';

interface QuickViewDialogProps extends Omit<DialogProps, 'resource'> {
  resource: ResourceItem;
  open: boolean;
  onClose: () => void;
}

const QuickViewDialog = ({ resource, open, onClose, ...props }: QuickViewDialogProps) => {
  const [content, setContent] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const isMarkdownFile = hasFileExtension(resource.name, 'md');
  const isTextFile = hasFileExtension(resource.name, 'txt');

  const fetchContent = useCallback(async () => {
    if (!resource.url) {
      setError('No URL provided for file');
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch(resource.url);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch content: ${response.status} ${response.statusText}`);
      }
      
      const contentType = response.headers.get('content-type');
      if (contentType && !contentType.includes('text/') && !contentType.includes('application/octet-stream')) {
        throw new Error('File does not appear to be a text file');
      }
      
      const text = await response.text();
      setContent(text);
    } catch (err) {
      console.error('Failed to fetch file content:', err);
      setError(err instanceof Error ? err.message : 'Failed to load file content');
    } finally {
      setIsLoading(false);
    }
  }, [resource.url]);

  useEffect(() => {
    if (open && (isMarkdownFile || isTextFile)) {
      fetchContent();
    }
  }, [open, isMarkdownFile, isTextFile, fetchContent]);

  const handleClose = useCallback(() => {
    setContent('');
    setError(null);
    setIsLoading(true);
    onClose();
  }, [onClose]);

  return (
    <Dialog
      fullWidth
      maxWidth="lg"
      open={open}
      onClose={handleClose}
      PaperProps={{
        sx: {
          height: '80vh',
          maxHeight: '80vh',
        },
      }}
      {...props}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <Stack direction="row" alignItems="center" spacing={1}>
          <Iconify 
            icon={isMarkdownFile ? 'material-symbols:description' : 'material-symbols:article'} 
            sx={{ fontSize: 20 }}
          />
          <Typography variant="h6" component="span">
            {resource.name}
          </Typography>
        </Stack>
        <IconButton onClick={handleClose} size="small">
          <Iconify icon="material-symbols:close" />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 0, display: 'flex', flexDirection: 'column' }}>
        {isLoading ? (
          <Box sx={{ flex: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <LoadingScreen />
          </Box>
        ) : error ? (
          <Box sx={{ flex: 1, display: 'flex', alignItems: 'center', justifyContent: 'center', p: 3 }}>
            <Typography color="error" textAlign="center">
              {error}
            </Typography>
          </Box>
        ) : (
          <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
            <Editor
              value={content}
              toolbarVisible={false}
              editable={false}
            />
          </Box>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default QuickViewDialog; 