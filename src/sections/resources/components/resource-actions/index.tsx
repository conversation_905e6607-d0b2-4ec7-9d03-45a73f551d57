import type { BoxProps } from '@mui/material';

import { toast } from 'sonner';
import { useMemo } from 'react';
import FileSaver from 'file-saver';
import { useDispatch, useSelector } from 'react-redux';
import { useBoolean, usePopover } from 'minimal-shared/hooks';

import { Box, Tooltip, Divider, MenuItem, MenuList, Checkbox, IconButton } from '@mui/material';

import useAnalytics from 'src/hooks/analytics';
import useFeatureFlags from 'src/hooks/feature-flags';
import useResourcePermissions from 'src/hooks/use-resource-permissions';

import { hasFileExtension } from 'src/utils/file-utils';

import { AppFeatures, TranscriptStatus } from 'src/types';
import { ConvertDownloadFormat } from 'src/store/api/resources';
import { toggleSelectResource } from 'src/store/slices/resources/slice';
import { selectSelectedResources } from 'src/store/slices/resources/selectors';

import { Iconify } from 'src/components/iconify';
import { CustomPopover } from 'src/components/custom-popover';

import useDownloadSource from 'src/sections/resources/hooks/use-download-source';
import useDownloadTranscription from 'src/sections/resources/hooks/download-transcription';

import QuickViewDialog from './components/quick-view-dialog';
import RemoveResourceDialog from './components/remove-dialog';
import RenameResourceDialog from './components/rename-dialog';
import MoveResourceDialog from './components/move-resource-dialog';

import type { ResourceItem } from '../resources-list';

type HiddenActions = 'select' | 'view' | 'download' | 'delete';

interface Props extends Omit<BoxProps, 'resource'> {
  resource: ResourceItem;
  hiddenActions?: HiddenActions[];
  appendActions?: React.ReactNode;
  onItemDeleted?: () => void;
}

const ResourceActions: React.FC<Props> = ({
  resource,
  sx = {},
  hiddenActions = [],
  appendActions,
  onItemDeleted,
  ...props
}) => {
  const { trackEvent } = useAnalytics();
  const dispatch = useDispatch();
  const { isFlagEnabled } = useFeatureFlags();

  const deleteDialog = useBoolean();
  const moveDialog = useBoolean();
  const renameDialog = useBoolean();
  const quickViewDialog = useBoolean();
  const menuActions = usePopover();
  const downloadSourceMenu = usePopover();

  const selectedResources = useSelector(selectSelectedResources);
  const { canEdit } = useResourcePermissions({ resource });

  const downloadTranscription = useDownloadTranscription();
  const { downloadSource, isDownloading: downloading } = useDownloadSource();

  const isSelected = selectedResources.some((item) => item.id === resource.id);

  const isSessionCard = resource.cardType === 'session';
  const isTranscriptionReady = resource.transcriptionJobStatus === TranscriptStatus.Completed;
  const isTranscriptionEmpty = !resource.transcription?.length;

  const allowSelect = !isSessionCard && isTranscriptionReady && !hiddenActions.includes('select');

  // Check if file is .md to control download format menu visibility
  // Memoized to prevent unnecessary re-calculations
  const isMarkdownFile = useMemo(() => hasFileExtension(resource.name, 'md'), [resource.name]);
  const isTextFile = useMemo(() => hasFileExtension(resource.name, 'txt'), [resource.name]);
  const isEnabledNewPreviewEditor = isFlagEnabled(AppFeatures.NEW_PREVIEW_EDITOR_FONT_SIZE_CONTROL);
  const canQuickView = useMemo(() => isEnabledNewPreviewEditor && (isMarkdownFile || isTextFile), [isEnabledNewPreviewEditor, isMarkdownFile, isTextFile]);

  const onDownloadSource = async (format?: ConvertDownloadFormat) => {
    await downloadSource(resource, format);
    downloadSourceMenu.onClose();
  };

  // Regular download for non-markdown files
  const onDownloadFile = () => {
    if (!resource.url) return;
    trackEvent({
      eventCategory: 'Resource',
      eventAction: 'Download file',
      properties: {
        resourceId: resource.id,
        resourceName: resource.name,
      },
    });

    FileSaver.saveAs(resource.url, resource.name);
    toast.success(`Download started for ${resource.name}`, {
      description: 'Please wait while the file being downloaded...',
      duration: 5000,
    });

    menuActions.onClose();
  };

  const onDownloadTranscription = () => {
    if (!resource.transcription?.length) return;
    trackEvent({
      eventCategory: 'Resource',
      eventAction: 'Download transcription',
      properties: {
        resourceId: resource.id,
        resourceName: resource.name,
      },
    });

    downloadTranscription(resource.transcription, `${resource.name}.docx`);
    toast.success(`Downloading transcription for ${resource.name}`, {
      description: 'Please wait while the file being downloaded...',
    });
  };

  const onSelectResource = (evt: React.ChangeEvent<HTMLInputElement>) => {
    evt.stopPropagation();
    dispatch(toggleSelectResource(resource));
  };

  const onOpenMenu = (evt: React.MouseEvent<HTMLButtonElement>) => {
    evt.stopPropagation();
    menuActions.onOpen(evt);
  };

  const onOpenDownloadSourceMenu = (evt: React.MouseEvent<HTMLElement>) => {
    evt.stopPropagation();
    downloadSourceMenu.onOpen(evt);
  };

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          ...sx,
        }}
        {...props}
      >
        {appendActions}
        {allowSelect && (
          <Tooltip title="Select file" placement="top" arrow>
            <Checkbox
              sx={{ py: 0 }}
              size="small"
              checked={isSelected}
              onChange={onSelectResource}
            />
          </Tooltip>
        )}
        <Tooltip title="More actions" placement="top" arrow>
          <IconButton disableRipple sx={{ py: 0 }} onClick={onOpenMenu}>
            <Iconify icon="material-symbols:more-vert" />
          </IconButton>
        </Tooltip>
      </Box>
      <CustomPopover
        open={menuActions.open}
        anchorEl={menuActions.anchorEl}
        onClose={menuActions.onClose}
        slotProps={{ arrow: { placement: 'top-left' } }}
      >
        <MenuList>
          {!isSessionCard && (
            <>
              {canQuickView && (
                <MenuItem onClick={quickViewDialog.onTrue}>
                  <Iconify icon="material-symbols:document-scanner" />
                  View content
                </MenuItem>
              )}
              <MenuItem onClick={renameDialog.onTrue} disabled={!canEdit}>
                <Iconify icon="material-symbols:edit" />
                Rename
              </MenuItem>
              <MenuItem onClick={moveDialog.onTrue} disabled={!canEdit}>
                <Iconify icon="material-symbols:drive-file-move" />
                Move
              </MenuItem>
              {/* Show Download Source menu for .md files, regular Download for others */}
              {!hiddenActions.includes('download') &&
                (isMarkdownFile ? (
                  <MenuItem onClick={onOpenDownloadSourceMenu} disabled={!!downloading}>
                    <Iconify icon="material-symbols:download" />
                    Download source
                    <Iconify icon="material-symbols:keyboard-arrow-right" sx={{ ml: 'auto' }} />
                  </MenuItem>
                ) : (
                  <MenuItem onClick={onDownloadFile}>
                    <Iconify icon="material-symbols:download" />
                    Download
                  </MenuItem>
                ))}
              {isTranscriptionReady && !isTranscriptionEmpty && (
                <MenuItem onClick={onDownloadTranscription}>
                  <Iconify icon="material-symbols:description" />
                  Download transcription
                </MenuItem>
              )}
            </>
          )}
          <Divider />
          <MenuItem
            onClick={deleteDialog.onTrue}
            sx={{
              color: canEdit ? 'error.main' : 'inherit',
            }}
            disabled={!canEdit}
          >
            <Iconify icon="material-symbols:delete" />
            Delete
          </MenuItem>
        </MenuList>
      </CustomPopover>

      {/* Download Source Format Menu - Only for .md files */}
      {isMarkdownFile && (
        <CustomPopover
          open={downloadSourceMenu.open}
          anchorEl={downloadSourceMenu.anchorEl}
          onClose={downloadSourceMenu.onClose}
          slotProps={{ arrow: { placement: 'left-top' } }}
        >
          <MenuList>
            <MenuItem
              onClick={() => onDownloadSource(ConvertDownloadFormat.Txt)}
              disabled={!!downloading}
            >
              <Iconify
                icon={
                  downloading === ConvertDownloadFormat.Txt
                    ? 'eos-icons:loading'
                    : 'material-symbols:description'
                }
              />
              As TXT
            </MenuItem>
            <MenuItem
              onClick={() => onDownloadSource(ConvertDownloadFormat.Docx)}
              disabled={!!downloading}
            >
              <Iconify
                icon={
                  downloading === ConvertDownloadFormat.Docx
                    ? 'eos-icons:loading'
                    : 'material-symbols:description'
                }
              />
              As DOCX
            </MenuItem>
          </MenuList>
        </CustomPopover>
      )}

      {/* Dialogs */}
      <MoveResourceDialog
        open={moveDialog.value}
        onClose={moveDialog.onFalse}
        resource={resource}
      />
      <RemoveResourceDialog
        isSessionCard={isSessionCard}
        open={deleteDialog.value}
        onClose={deleteDialog.onFalse}
        resource={resource}
        onItemDeleted={onItemDeleted}
      />
      <QuickViewDialog
        open={quickViewDialog.value}
        onClose={quickViewDialog.onFalse}
        resource={resource}
      />
      <RenameResourceDialog
        open={renameDialog.value}
        onClose={renameDialog.onFalse}
        resource={resource}
      />
    </>
  );
};

export default ResourceActions;
