import type { Resource } from 'src/types';

import { memo, useRef, useState, useEffect, useCallback } from 'react';

import Box from '@mui/material/Box';
import Slider from '@mui/material/Slider';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';

import { fSecsToTime } from 'src/utils/format-time';

import { Iconify } from 'src/components/iconify';

interface AudioPlayerProps {
  resource: Resource;
  onLoad?: () => void;
  onError?: () => void;
}

const AudioPlayer = ({ resource, onLoad, onError }: AudioPlayerProps) => {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleLoadedMetadata = useCallback(() => {
    const audio = audioRef.current;
    if (audio) {
      setDuration(audio.duration);
      setIsLoading(false);
      onLoad?.();
    }
  }, [onLoad]);

  const handleTimeUpdate = useCallback(() => {
    const audio = audioRef.current;
    if (audio) {
      setCurrentTime(audio.currentTime);
    }
  }, []);

  const handleEnded = useCallback(() => {
    setIsPlaying(false);
  }, []);

  const handleError = useCallback(() => {
    console.error('Audio failed to load:', resource.url);
    setHasError(true);
    setIsLoading(false);
    onError?.();
  }, [resource.url, onError]);

  const togglePlayPause = useCallback(() => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
      setIsPlaying(false);
    } else {
      audio.play().then(() => {
        setIsPlaying(true);
      }).catch(handleError);
    }
  }, [isPlaying, handleError]);

  const handleSeek = useCallback((_: Event, value: number | number[]) => {
    const audio = audioRef.current;
    if (audio && typeof value === 'number') {
      audio.currentTime = value;
      setCurrentTime(value);
    }
  }, []);

  const handleVolumeChange = useCallback((_: Event, value: number | number[]) => {
    const audio = audioRef.current;
    if (audio && typeof value === 'number') {
      const newVolume = value / 100;
      audio.volume = newVolume;
      setVolume(newVolume);
    }
  }, []);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.target instanceof HTMLInputElement) return;
      
      switch (event.key) {
        case ' ':
          event.preventDefault();
          togglePlayPause();
          break;
        case 'ArrowUp':
          event.preventDefault();
          handleVolumeChange(null as any, Math.min(100, volume * 100 + 10));
          break;
        case 'ArrowDown':
          event.preventDefault();
          handleVolumeChange(null as any, Math.max(0, volume * 100 - 10));
          break;
        default:
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [togglePlayPause, handleVolumeChange, volume]);

  if (hasError) {
    return (
      <Box
        sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: 'auto',
          maxWidth: 600,
          minWidth: 480,
          p: 4,
          bgcolor: 'background.paper',
          borderRadius: 3,
          boxShadow: (theme) => theme.shadows[24],
          border: '1px dashed',
          borderColor: 'error.main',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          gap: 2,
          zIndex: 1,
        }}
      >
        <Iconify 
          icon="material-symbols:error-outline" 
          sx={{ fontSize: 48, color: 'error.main' }} 
        />
        <Typography variant="h6" color="error.main" textAlign="center">
          Audio file failed to load
        </Typography>
        <Typography variant="body2" color="text.secondary" textAlign="center">
          {resource.name}
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        width: '100%',
        aspectRatio: '16/9',
        position: 'relative',
        overflow: 'hidden',
        bgcolor: 'background.paper',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <Box
      sx={{
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: 'auto',
        maxWidth: 600,
        minWidth: 480,
        p: 4,
        bgcolor: 'background.paper',
        borderRadius: 3,
        border: '1px solid',
        borderColor: 'divider',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: 3,
        zIndex: 1,
      }}
      >
        <audio
          ref={audioRef}
          src={resource.url}
          onLoadedMetadata={handleLoadedMetadata}
          onTimeUpdate={handleTimeUpdate}
          onEnded={handleEnded}
          onError={handleError}
          style={{ display: 'none' }}
        />

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
          <Iconify 
            icon="material-symbols:music-note" 
            sx={{ fontSize: 32, color: 'primary.main' }} 
          />
          
          <Box sx={{ flex: 1, minWidth: 0 }}>
            <Typography variant="h6" noWrap sx={{ mb: 0.5 }}>
              {resource.name}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Audio File
            </Typography>
          </Box>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
          <IconButton 
            onClick={togglePlayPause}
            sx={{ 
              bgcolor: 'primary.main',
              color: 'primary.contrastText',
              '&:hover': { bgcolor: 'primary.dark' },
              width: 56,
              height: 56,
            }}
          >
            <Iconify 
              icon={isPlaying ? 'material-symbols:pause' : 'material-symbols:play-arrow'}
              sx={{ fontSize: 32 }}
            />
          </IconButton>

          <Box sx={{ flex: 1, display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="caption" color="text.secondary" sx={{ minWidth: 40 }}>
              {fSecsToTime(currentTime)}
            </Typography>
            
            <Slider
              value={currentTime}
              onChange={handleSeek}
              min={0}
              max={duration}
              sx={{ flex: 1 }}
              disabled={!duration}
            />
            
            <Typography variant="caption" color="text.secondary" sx={{ minWidth: 40 }}>
              {fSecsToTime(duration)}
            </Typography>
          </Box>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
          <IconButton size="small">
            <Iconify 
              icon="material-symbols:volume-down" 
              sx={{ fontSize: 20 }} 
            />
          </IconButton>
          
          <Slider
            value={volume * 100}
            onChange={handleVolumeChange}
            min={0}
            max={100}
            sx={{ flex: 1 }}
          />
          
          <IconButton size="small">
            <Iconify 
              icon="material-symbols:volume-up" 
              sx={{ fontSize: 20 }} 
            />
          </IconButton>
        </Box>

        <Typography variant="caption" color="text.secondary" textAlign="center">
          Use spacebar to play/pause • Arrow keys to adjust volume
        </Typography>
      </Box>
    </Box>
  );
};

export default memo(AudioPlayer); 
