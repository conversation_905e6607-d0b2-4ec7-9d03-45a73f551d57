import type { Resource } from 'src/types';

import { memo, useRef, useMemo, useState, useCallback } from 'react';

import { Box, Fade, Typography } from '@mui/material';

import { Iconify } from 'src/components/iconify';
import { LoadingScreen } from 'src/components/loading-screen';

const DocumentErrorFallback = memo<{ resource: Resource; error?: string }>(({ resource, error }) => (
  <Box
    sx={{
      width: '100%',
      aspectRatio: '16/9',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      bgcolor: 'background.paper',
      border: '1px dashed',
      borderColor: 'error.main',
      borderRadius: 2,
      p: 3,
    }}
  >
    <Iconify 
      icon="material-symbols:error-outline" 
      sx={{ fontSize: 48, mb: 2, color: 'error.main' }} 
    />
    <Typography variant="h6" color="error" textAlign="center" sx={{ mb: 1 }}>
      {!resource.url ? 'Document preview not available' : error || 'Document preview failed to load'}
    </Typography>
    <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ mb: 2 }}>
      {resource.name}
    </Typography>
    <Typography variant="caption" color="text.secondary" textAlign="center">
      {!resource.url 
        ? 'No document URL provided for preview.' 
        : 'The document may not be publicly accessible or the file format may not be supported by Google Docs Viewer.'
      }
    </Typography>
  </Box>
));

DocumentErrorFallback.displayName = 'DocumentErrorFallback';

interface DocumentViewerProps {
  resource: Resource;
  onLoad?: () => void;
  onError?: () => void;
}

const DocumentViewer = memo<DocumentViewerProps>(({ resource, onLoad, onError }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [documentError, setDocumentError] = useState<string | null>(null);

  const handleIframeLoad = useCallback(() => {
    setIsLoading(false);
    setDocumentError(null);
    onLoad?.();
  }, [onLoad]);

  const handleIframeError = useCallback(() => {
    console.error('Document failed to load:', resource.url);
    setIsLoading(false);
    setDocumentError('Document preview failed to load');
    onError?.();
  }, [resource.url, onError]);

  const getGoogleDocsViewerUrl = useCallback((url: string) => {
    const encodedUrl = encodeURIComponent(url);
    return `https://docs.google.com/gview?url=${encodedUrl}&embedded=true`;
  }, []);

  const iframeProps = useMemo(() => ({
    src: resource.url ? getGoogleDocsViewerUrl(resource.url) : '',
    title: `Document preview: ${resource.name}`,
    onLoad: handleIframeLoad,
    onError: handleIframeError,
    style: {
      width: '100%',
      height: '100%',
      border: 'none',
      display: 'block',
    } as React.CSSProperties,
    allow: 'clipboard-read; clipboard-write',
    sandbox: 'allow-scripts allow-same-origin allow-popups allow-forms',
  }), [resource.url, resource.name, getGoogleDocsViewerUrl, handleIframeLoad, handleIframeError]);

  if (!resource.url || documentError) {
    return <DocumentErrorFallback resource={resource} error={documentError || undefined} />;
  }

  return (
    <Box
      ref={containerRef}
      sx={{
        width: '100%',
        minHeight: '70vh',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
        bgcolor: 'background.paper',
      }}
    >
      {isLoading && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'background.paper',
            zIndex: 1,
          }}
        >
          <LoadingScreen />
        </Box>
      )}

      <Box
        sx={{
          flex: 1,
          overflow: 'hidden',
          borderRadius: 2,
          position: 'relative',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Fade in={!isLoading} timeout={300}>
          <Box 
            sx={{ 
              width: '100%', 
              height: '100%',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <iframe
              {...iframeProps}
            />
          </Box>
        </Fade>
      </Box>
    </Box>
  );
});

DocumentViewer.displayName = 'DocumentViewer';

export default memo(DocumentViewer); 
