import type { Resource } from 'src/types';

import { memo, useRef, useState, useEffect, useCallback } from 'react';

import { Box, Fade, Stack, Tooltip, Typography, IconButton } from '@mui/material';

import { Iconify } from 'src/components/iconify';
import { LoadingScreen } from 'src/components/loading-screen';

interface ImageViewerProps {
  resource: Resource;
  onLoad?: () => void;
  onError?: () => void;
}

interface PanState {
  x: number;
  y: number;
}

interface DragState {
  isDragging: boolean;
  startX: number;
  startY: number;
  startPanX: number;
  startPanY: number;
}

interface TouchState {
  isTouch: boolean;
  startDistance: number;
  startScale: number;
  startPan: PanState;
  touches: { x: number; y: number }[];
}

const ImageViewer = ({ resource, onLoad, onError }: ImageViewerProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [scale, setScale] = useState(1);
  const [pan, setPan] = useState<PanState>({ x: 0, y: 0 });
  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    startX: 0,
    startY: 0,
    startPanX: 0,
    startPanY: 0,
  });
  const [touchState, setTouchState] = useState<TouchState>({
    isTouch: false,
    startDistance: 0,
    startScale: 1,
    startPan: { x: 0, y: 0 },
    touches: [],
  });

  const containerRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);

  const MIN_SCALE = 0.1;
  const MAX_SCALE = 5;
  const ZOOM_STEP = 0.2;

  const handleImageLoad = useCallback(() => {
    setIsLoading(false);
    setHasError(false);
    onLoad?.();
  }, [onLoad]);

  const handleImageError = useCallback(() => {
    setIsLoading(false);
    setHasError(true);
    onError?.();
  }, [onError]);

  const resetView = useCallback(() => {
    setScale(1);
    setPan({ x: 0, y: 0 });
  }, []);

  const zoomIn = useCallback(() => {
    setScale((prevScale) => Math.min(prevScale + ZOOM_STEP, MAX_SCALE));
  }, []);

  const zoomOut = useCallback(() => {
    setScale((prevScale) => Math.max(prevScale - ZOOM_STEP, MIN_SCALE));
  }, []);

  const constrainPan = useCallback((newPan: PanState, currentScale: number): PanState => {
    if (!containerRef.current || !imageRef.current) return newPan;

    const container = containerRef.current.getBoundingClientRect();
    const image = imageRef.current.getBoundingClientRect();

    const scaledImageWidth = image.width * currentScale;
    const scaledImageHeight = image.height * currentScale;

    const maxPanX = Math.max(0, (scaledImageWidth - container.width) / 2);
    const maxPanY = Math.max(0, (scaledImageHeight - container.height) / 2);

    return {
      x: Math.max(-maxPanX, Math.min(maxPanX, newPan.x)),
      y: Math.max(-maxPanY, Math.min(maxPanY, newPan.y)),
    };
  }, []);

  // Touch event helpers for native events
  const getTouchDistance = useCallback((touches: Touch[]) => {
    if (touches.length < 2) return 0;
    const dx = touches[0].clientX - touches[1].clientX;
    const dy = touches[0].clientY - touches[1].clientY;
    return Math.sqrt(dx * dx + dy * dy);
  }, []);

  const getTouchCenter = useCallback((touches: Touch[]) => {
    if (touches.length === 0) return { x: 0, y: 0 };
    if (touches.length === 1) return { x: touches[0].clientX, y: touches[0].clientY };

    const x = (touches[0].clientX + touches[1].clientX) / 2;
    const y = (touches[0].clientY + touches[1].clientY) / 2;
    return { x, y };
  }, []);

  // Keyboard shortcuts and gesture support
  // Supported gestures:
  // - Keyboard: +/= (zoom in), - (zoom out), 0 (reset)
  // - Mouse: Scroll wheel (zoom), Drag (pan when zoomed)
  // - Trackpad: Pinch (zoom), Two-finger scroll (pan), Ctrl+scroll (zoom)
  // - Touch: Pinch (zoom), Single-finger drag (pan when zoomed)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.target !== document.body) return; // Only handle when modal is focused

      switch (event.key) {
        case '+':
        case '=':
          event.preventDefault();
          zoomIn();
          break;
        case '-':
          event.preventDefault();
          zoomOut();
          break;
        case '0':
          event.preventDefault();
          resetView();
          break;
        default:
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [zoomIn, zoomOut, resetView]);

  // Event listeners setup - override browser defaults
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return undefined;

    // Wheel event handler with passive: false to override browser zoom
    const wheelHandler = (event: WheelEvent) => {
      event.preventDefault();
      event.stopPropagation();

      // Handle pinch-to-zoom on trackpad (Ctrl + wheel)
      if (event.ctrlKey) {
        const delta = event.deltaY > 0 ? -ZOOM_STEP * 0.5 : ZOOM_STEP * 0.5;
        const newScale = Math.max(MIN_SCALE, Math.min(MAX_SCALE, scale + delta));

        if (newScale !== scale) {
          setScale(newScale);
          const rect = container.getBoundingClientRect();
          const centerX = rect.width / 2;
          const centerY = rect.height / 2;
          const mouseX = event.clientX - rect.left;
          const mouseY = event.clientY - rect.top;

          const deltaX = (mouseX - centerX) * (delta / scale);
          const deltaY = (mouseY - centerY) * (delta / scale);

          const newPan = constrainPan({ x: pan.x - deltaX, y: pan.y - deltaY }, newScale);
          setPan(newPan);
        }
        return;
      }

      // Handle trackpad two-finger scroll for panning when zoomed in
      if (scale > 1 && !event.shiftKey) {
        const newPan = constrainPan(
          {
            x: pan.x - event.deltaX * 0.5,
            y: pan.y - event.deltaY * 0.5,
          },
          scale
        );
        setPan(newPan);
        return;
      }

      // Handle regular wheel scroll for zoom
      const delta = event.deltaY > 0 ? -ZOOM_STEP : ZOOM_STEP;
      const newScale = Math.max(MIN_SCALE, Math.min(MAX_SCALE, scale + delta));

      if (newScale !== scale) {
        setScale(newScale);
        const rect = container.getBoundingClientRect();
        const centerX = rect.width / 2;
        const centerY = rect.height / 2;
        const mouseX = event.clientX - rect.left;
        const mouseY = event.clientY - rect.top;

        const deltaX = (mouseX - centerX) * (delta / scale);
        const deltaY = (mouseY - centerY) * (delta / scale);

        const newPan = constrainPan({ x: pan.x - deltaX, y: pan.y - deltaY }, newScale);
        setPan(newPan);
      }
    };

    // Mouse event handlers
    const mouseDownHandler = (event: MouseEvent) => {
      if (scale <= 1) return;
      event.preventDefault();
      event.stopPropagation();

      setDragState({
        isDragging: true,
        startX: event.clientX,
        startY: event.clientY,
        startPanX: pan.x,
        startPanY: pan.y,
      });
    };

    const mouseMoveHandler = (event: MouseEvent) => {
      if (!dragState.isDragging || scale <= 1) return;
      event.preventDefault();

      const deltaX = event.clientX - dragState.startX;
      const deltaY = event.clientY - dragState.startY;

      const newPan = constrainPan(
        {
          x: dragState.startPanX + deltaX,
          y: dragState.startPanY + deltaY,
        },
        scale
      );

      setPan(newPan);
    };

    const mouseUpHandler = () => {
      setDragState((prev) => ({ ...prev, isDragging: false }));
    };

    // Touch event handlers with passive: false
    const touchStartHandler = (event: TouchEvent) => {
      event.preventDefault();
      event.stopPropagation();

      const touches = Array.from(event.touches);

      if (touches.length === 1) {
        setDragState({
          isDragging: true,
          startX: touches[0].clientX,
          startY: touches[0].clientY,
          startPanX: pan.x,
          startPanY: pan.y,
        });
      } else if (touches.length === 2) {
        const distance = getTouchDistance(touches);

        setTouchState({
          isTouch: true,
          startDistance: distance,
          startScale: scale,
          startPan: pan,
          touches: touches.map((t) => ({ x: t.clientX, y: t.clientY })),
        });

        setDragState((prev) => ({ ...prev, isDragging: false }));
      }
    };

    const touchMoveHandler = (event: TouchEvent) => {
      event.preventDefault();
      event.stopPropagation();

      const touches = Array.from(event.touches);

      if (touches.length === 1 && dragState.isDragging && scale > 1) {
        const deltaX = touches[0].clientX - dragState.startX;
        const deltaY = touches[0].clientY - dragState.startY;

        const newPan = constrainPan(
          {
            x: dragState.startPanX + deltaX,
            y: dragState.startPanY + deltaY,
          },
          scale
        );

        setPan(newPan);
      } else if (touches.length === 2 && touchState.isTouch) {
        const currentDistance = getTouchDistance(touches);

        if (touchState.startDistance > 0) {
          const scaleChange = currentDistance / touchState.startDistance;
          const newScale = Math.max(
            MIN_SCALE,
            Math.min(MAX_SCALE, touchState.startScale * scaleChange)
          );

          setScale(newScale);

          const rect = container.getBoundingClientRect();
          const centerX = rect.width / 2;
          const centerY = rect.height / 2;
          const touchCenter = getTouchCenter(touches);
          const touchX = touchCenter.x - rect.left;
          const touchY = touchCenter.y - rect.top;

          const scaleDelta = newScale - touchState.startScale;
          const deltaX = (touchX - centerX) * (scaleDelta / touchState.startScale);
          const deltaY = (touchY - centerY) * (scaleDelta / touchState.startScale);

          const newPan = constrainPan(
            {
              x: touchState.startPan.x - deltaX,
              y: touchState.startPan.y - deltaY,
            },
            newScale
          );

          setPan(newPan);
        }
      }
    };

    const touchEndHandler = (event: TouchEvent) => {
      event.preventDefault();
      event.stopPropagation();

      if (event.touches.length === 0) {
        setDragState((prev) => ({ ...prev, isDragging: false }));
        setTouchState((prev) => ({ ...prev, isTouch: false }));
      } else if (event.touches.length === 1) {
        const remainingTouch = event.touches[0];
        setDragState({
          isDragging: true,
          startX: remainingTouch.clientX,
          startY: remainingTouch.clientY,
          startPanX: pan.x,
          startPanY: pan.y,
        });
        setTouchState((prev) => ({ ...prev, isTouch: false }));
      }
    };

    // Add event listeners with { passive: false } to override browser defaults
    container.addEventListener('wheel', wheelHandler, { passive: false });
    container.addEventListener('mousedown', mouseDownHandler, { passive: false });
    container.addEventListener('mousemove', mouseMoveHandler, { passive: false });
    container.addEventListener('mouseup', mouseUpHandler);
    container.addEventListener('touchstart', touchStartHandler, { passive: false });
    container.addEventListener('touchmove', touchMoveHandler, { passive: false });
    container.addEventListener('touchend', touchEndHandler, { passive: false });

    return () => {
      container.removeEventListener('wheel', wheelHandler);
      container.removeEventListener('mousedown', mouseDownHandler);
      container.removeEventListener('mousemove', mouseMoveHandler);
      container.removeEventListener('mouseup', mouseUpHandler);
      container.removeEventListener('touchstart', touchStartHandler);
      container.removeEventListener('touchmove', touchMoveHandler);
      container.removeEventListener('touchend', touchEndHandler);
    };
  }, [scale, pan, dragState, touchState, constrainPan, getTouchDistance, getTouchCenter]);

  // Global mouse up handler for dragging outside container
  useEffect(() => {
    if (dragState.isDragging) {
      const handleGlobalMouseUp = () => setDragState((prev) => ({ ...prev, isDragging: false }));
      const handleGlobalMouseMove = (event: MouseEvent) => {
        if (!dragState.isDragging) return;

        const deltaX = event.clientX - dragState.startX;
        const deltaY = event.clientY - dragState.startY;

        const newPan = constrainPan(
          {
            x: dragState.startPanX + deltaX,
            y: dragState.startPanY + deltaY,
          },
          scale
        );

        setPan(newPan);
      };

      document.addEventListener('mouseup', handleGlobalMouseUp);
      document.addEventListener('mousemove', handleGlobalMouseMove);

      return () => {
        document.removeEventListener('mouseup', handleGlobalMouseUp);
        document.removeEventListener('mousemove', handleGlobalMouseMove);
      };
    }

    return undefined;
  }, [dragState, scale, constrainPan]);

  if (!resource.url || hasError) {
    return (
      <Box
        sx={{
          width: '100%',
          aspectRatio: '16/9',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: 'background.paper',
          border: '1px dashed',
          borderColor: hasError ? 'error.main' : 'divider',
          borderRadius: 2,
          p: 3,
        }}
      >
        <Typography
          variant="h6"
          color={hasError ? 'error' : 'text.secondary'}
          textAlign="center"
          sx={{ mb: 1 }}
        >
          {!resource.url ? 'Image preview not available' : 'Image failed to load'}
        </Typography>
        <Typography variant="body2" color="text.secondary" textAlign="center">
          {resource.name}
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        width: '100%',
        aspectRatio: '16/9',
        position: 'relative',
        overflow: 'hidden',
        bgcolor: 'black',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      {isLoading && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'background.paper',
            zIndex: 1,
          }}
        >
          <LoadingScreen />
        </Box>
      )}

      {/* Zoom Controls */}
      <Box
        sx={{
          position: 'absolute',
          top: 16,
          right: 16,
          zIndex: 2,
          bgcolor: 'rgba(0, 0, 0, 0.6)',
          borderRadius: 1,
          p: 0.5,
        }}
      >
        <Stack direction="row" spacing={0.5}>
          <Tooltip title="Zoom In (+) / Pinch Out">
            <IconButton
              size="small"
              onClick={zoomIn}
              disabled={scale >= MAX_SCALE}
              sx={{
                color: 'white',
                '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.1)' },
                '&:disabled': { color: 'rgba(255, 255, 255, 0.3)' },
              }}
            >
              <Iconify icon="material-symbols:add" />
            </IconButton>
          </Tooltip>

          <Tooltip title="Zoom Out (-) / Pinch In">
            <IconButton
              size="small"
              onClick={zoomOut}
              disabled={scale <= MIN_SCALE}
              sx={{
                color: 'white',
                '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.1)' },
                '&:disabled': { color: 'rgba(255, 255, 255, 0.3)' },
              }}
            >
              <Iconify icon="material-symbols:remove" />
            </IconButton>
          </Tooltip>

          <Tooltip title="Reset View (0)">
            <IconButton
              size="small"
              onClick={resetView}
              disabled={scale === 1 && pan.x === 0 && pan.y === 0}
              sx={{
                color: 'white',
                '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.1)' },
                '&:disabled': { color: 'rgba(255, 255, 255, 0.3)' },
              }}
            >
              <Iconify icon="material-symbols:refresh" />
            </IconButton>
          </Tooltip>
        </Stack>
      </Box>

      {/* Zoom Level Indicator */}
      {scale !== 1 && (
        <Box
          sx={{
            position: 'absolute',
            bottom: 16,
            left: 16,
            zIndex: 2,
            bgcolor: 'rgba(0, 0, 0, 0.6)',
            borderRadius: 1,
            px: 1,
            py: 0.5,
          }}
        >
          <Typography variant="caption" sx={{ color: 'white', fontWeight: 'medium' }}>
            {Math.round(scale * 100)}%
          </Typography>
        </Box>
      )}

      <Fade in={!isLoading} timeout={300}>
        <Box
          ref={containerRef}
          sx={{
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'background.paper',
            cursor: scale > 1 ? (dragState.isDragging ? 'grabbing' : 'grab') : 'default',
            overflow: 'hidden',
            // Disable OS/browser default gestures
            touchAction: 'none', // Prevents browser zoom/pan
            userSelect: 'none',
            WebkitUserSelect: 'none',
            msUserSelect: 'none',
            MozUserSelect: 'none',
            // Prevent context menu
            WebkitTouchCallout: 'none',
            WebkitTapHighlightColor: 'transparent',
          }}
        >
          <img
            ref={imageRef}
            src={resource.url}
            alt={resource.name || 'Image preview'}
            onLoad={handleImageLoad}
            onError={handleImageError}
            style={{
              maxWidth: '100%',
              maxHeight: '100%',
              objectFit: 'contain',
              display: 'block',
              borderRadius: '8px',
              transform: `scale(${scale}) translate(${pan.x}px, ${pan.y}px)`,
              transition: dragState.isDragging ? 'none' : 'transform 0.1s ease-out',
              userSelect: 'none',
              pointerEvents: 'none', // Prevent default image drag behavior
            }}
          />
        </Box>
      </Fade>
    </Box>
  );
};

export default memo(ImageViewer);
