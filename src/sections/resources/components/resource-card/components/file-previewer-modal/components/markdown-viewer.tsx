import type { Resource } from 'src/types';

import { memo, useState, useEffect, useCallback } from 'react';

import { Box, Typography } from '@mui/material';

import { Iconify } from 'src/components/iconify';
import { Markdown } from 'src/components/markdown';
import { LoadingScreen } from 'src/components/loading-screen';
import { SmallBlock } from 'src/components/settings/drawer/styles';
import { FontSizeOptions } from 'src/components/settings/drawer/font-options';

interface MarkdownViewerProps {
  resource: Resource;
  onLoad?: () => void;
  onError?: () => void;
}

const DEFAULT_FONT_SIZE = 14;
const FONT_SIZE_RANGE: [number, number] = [10, 24];

const MarkdownViewer = ({ resource, onLoad, onError }: MarkdownViewerProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [markdownContent, setMarkdownContent] = useState<string>('');
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [fontSize, setFontSize] = useState(DEFAULT_FONT_SIZE);

  const handleFontSizeChange = useCallback((newFontSize: number) => {
    setFontSize(newFontSize);
  }, []);

  const handleResetFontSize = useCallback(() => {
    setFontSize(DEFAULT_FONT_SIZE);
  }, []);

  const canResetFontSize = fontSize !== DEFAULT_FONT_SIZE;

  useEffect(() => {
    const fetchMarkdownContent = async () => {
      if (!resource.url) {
        setHasError(true);
        setErrorMessage('No URL provided for markdown file');
        setIsLoading(false);
        onError?.();
        return;
      }

      try {
        setIsLoading(true);
        setHasError(false);
        
        const response = await fetch(resource.url);
        
        if (!response.ok) {
          throw new Error(`Failed to fetch markdown content: ${response.status} ${response.statusText}`);
        }
        
        const contentType = response.headers.get('content-type');
        if (contentType && !contentType.includes('text/') && !contentType.includes('application/octet-stream')) {
          throw new Error('File does not appear to be a markdown file');
        }
        
        const text = await response.text();
        setMarkdownContent(text);
        setIsLoading(false);
        onLoad?.();
      } catch (error) {
        console.error('Failed to fetch markdown content:', error);
        setHasError(true);
        setErrorMessage(error instanceof Error ? error.message : 'Failed to load markdown content');
        setIsLoading(false);
        onError?.();
      }
    };

    fetchMarkdownContent();
  }, [resource.url]);

  if (isLoading) {
    return (
      <Box
        sx={{
          width: '100%',
          minHeight: '50vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: 'background.paper',
          borderRadius: 2,
        }}
      >
        <LoadingScreen />
      </Box>
    );
  }

  if (hasError) {
    return (
      <Box
        sx={{
          width: '100%',
          aspectRatio: '16/9',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: 'background.paper',
          border: '1px dashed',
          borderColor: 'error.main',
          p: 3,
        }}
      >
        <Iconify 
          icon="material-symbols:error-outline" 
          sx={{ fontSize: 48, mb: 2, color: 'error.main' }} 
        />
        <Typography variant="h6" color="error" textAlign="center" sx={{ mb: 1 }}>
          Markdown file failed to load
        </Typography>
        <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ mb: 2 }}>
          {resource.name}
        </Typography>
        <Typography variant="caption" color="text.secondary" textAlign="center">
          {errorMessage}
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        width: '100%',
        minHeight: '70vh',
        position: 'relative',
        overflow: 'auto',
        bgcolor: 'background.paper',
      }}
    >
      {/* Font Size Controls */}
      <Box
        sx={{
          position: 'sticky',
          top: 0,
          right: 0,
          zIndex: 10,
          display: 'flex',
          justifyContent: 'flex-end',
          mb: 2,
          bgcolor: 'background.paper',
          borderBottom: '1px solid',
          borderColor: 'divider',
          p: 2,
        }}
      >
        <Box sx={{ minWidth: 200 }}>
          <SmallBlock
            label="Font Size"
            canReset={canResetFontSize}
            onReset={handleResetFontSize}
          >
            <FontSizeOptions
              options={FONT_SIZE_RANGE}
              value={fontSize}
              onChangeOption={handleFontSizeChange}
            />
          </SmallBlock>
        </Box>
      </Box>

      {markdownContent ? (
        <Box sx={{
          width: '100%',
          maxWidth: '1200px',
          margin: '0 auto',
          fontSize: `${fontSize}px`,
          '& *': {
            fontSize: 'inherit !important',
            lineHeight: 1.6,
          },
          '& h1': { fontSize: `${fontSize * 2}px !important`, fontWeight: 700 },
          '& h2': { fontSize: `${fontSize * 1.75}px !important`, fontWeight: 600 },
          '& h3': { fontSize: `${fontSize * 1.5}px !important`, fontWeight: 600 },
          '& h4': { fontSize: `${fontSize * 1.25}px !important`, fontWeight: 500 },
          '& h5': { fontSize: `${fontSize * 1.1}px !important`, fontWeight: 500 },
          '& h6': { fontSize: `${fontSize}px !important`, fontWeight: 500 },
          '& code': { 
            fontSize: `${fontSize * 0.9}px !important`,
            fontFamily: 'Monaco, Consolas, "Courier New", monospace',
          },
          '& pre code': { 
            fontSize: `${fontSize * 0.85}px !important`,
          },
        }}>
          <Markdown>{markdownContent}</Markdown>
        </Box>
      ) : (
        <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ py: 4 }}>
          Markdown file appears to be empty
        </Typography>
      )}
    </Box>
  );
};

export default memo(MarkdownViewer); 
