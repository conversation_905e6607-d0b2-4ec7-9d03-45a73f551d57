import type { Resource } from 'src/types';

import { useResizeObserver } from 'usehooks-ts';
import { Page, pdfjs, Document } from 'react-pdf';
import { memo, useRef, useMemo, useState, useEffect, useCallback } from 'react';

import { Box, Fade, Stack, Tooltip, Typography, IconButton } from '@mui/material';

import { Iconify } from 'src/components/iconify';
import { LoadingScreen } from 'src/components/loading-screen';

pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  'pdfjs-dist/build/pdf.worker.min.mjs',
  import.meta.url
).toString();

const options = {
  cMapUrl: '/cmaps/',
  standardFontDataUrl: '/standard_fonts/',
};

const PdfErrorFallback = memo<{ resource: Resource; error?: string }>(({ resource, error }) => (
  <Box
    sx={{
      width: '100%',
      aspectRatio: '16/9',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      bgcolor: 'background.paper',
      border: '1px dashed',
      borderColor: 'error.main',
      borderRadius: 2,
      p: 3,
    }}
  >
    <Typography variant="h6" color="error" textAlign="center" sx={{ mb: 1 }}>
      {!resource.url ? 'PDF preview not available' : error || 'PDF preview failed to load'}
    </Typography>
    <Typography variant="body2" color="text.secondary" textAlign="center">
      {resource.name}
    </Typography>
  </Box>
));

PdfErrorFallback.displayName = 'PdfErrorFallback';

interface PdfViewerProps {
  resource: Resource;
  onLoad?: () => void;
  onError?: () => void;
}

const PdfViewer = memo<PdfViewerProps>(({ resource, onLoad, onError }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = useState<number>();
  const [isLoading, setIsLoading] = useState(true);
  const [pdfError, setPdfError] = useState<string | null>(null);
  const [numPages, setNumPages] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [scale, setScale] = useState<number>(0.75);

  const zoomIn = useCallback(() => {
    setScale(prevScale => Math.min(prevScale + 0.25, 3.0));
  }, []);

  const zoomOut = useCallback(() => {
    setScale(prevScale => Math.max(prevScale - 0.25, 0.5));
  }, []);

  const resetZoom = useCallback(() => {
    setScale(0.75);
  }, []);

  const goToPage = useCallback((pageNumber: number) => {
    if (pageNumber < 1 || pageNumber > numPages) return;
    
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    const targetPage = scrollContainer.querySelector(`[data-page-number="${pageNumber}"]`);
    if (targetPage) {
      targetPage.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, [numPages]);

  const goToNextPage = useCallback(() => {
    if (currentPage < numPages) {
      goToPage(currentPage + 1);
    }
  }, [currentPage, numPages, goToPage]);

  const goToPreviousPage = useCallback(() => {
    if (currentPage > 1) {
      goToPage(currentPage - 1);
    }
  }, [currentPage, goToPage]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!numPages) return;
      
      switch (event.key) {
        case 'ArrowUp':
        case 'PageUp':
          event.preventDefault();
          goToPreviousPage();
          break;
        case 'ArrowDown':
        case 'PageDown':
          event.preventDefault();
          goToNextPage();
          break;
        case '+':
        case '=':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault();
            zoomIn();
          }
          break;
        case '-':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault();
            zoomOut();
          }
          break;
        case '0':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault();
            resetZoom();
          }
          break;
        default:
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [numPages, goToPreviousPage, goToNextPage, zoomIn, zoomOut, resetZoom]);

  useResizeObserver({
    ref: containerRef,
    onResize: (size) => setContainerWidth(size.width),
  });

  // Track current page based on scroll position
  const handleScroll = useCallback(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer || numPages === 0) return;

    const pageElements = scrollContainer.querySelectorAll('[data-page-number]');
    if (pageElements.length === 0) return;

    const containerRect = scrollContainer.getBoundingClientRect();
    const viewportCenter = containerRect.top + containerRect.height / 2;

    let closestPage = 1;
    let minDistance = Infinity;

    pageElements.forEach((element) => {
      const pageNumber = parseInt(element.getAttribute('data-page-number') || '1', 10);
      const elementRect = element.getBoundingClientRect();
      const elementCenter = elementRect.top + elementRect.height / 2;
      
      // Calculate distance from viewport center to element center
      const distance = Math.abs(viewportCenter - elementCenter);
      
      // If this page is closer to viewport center, it's the current page
      if (distance < minDistance) {
        minDistance = distance;
        closestPage = pageNumber;
      }
    });

    if (closestPage !== currentPage) {
      setCurrentPage(closestPage);
    }
  }, [numPages, currentPage]);

  // Add scroll listener
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return undefined;

    scrollContainer.addEventListener('scroll', handleScroll);
    // Initial check
    handleScroll();

    return () => {
      scrollContainer.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll, numPages]);

  // Trigger scroll detection when PDF loads and pages are rendered
  useEffect(() => {
    if (!isLoading && numPages > 0) {
      // Small delay to ensure pages are fully rendered
      const timer = setTimeout(() => {
        handleScroll();
      }, 100);
      
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [isLoading, numPages, handleScroll]);

  // Memoize event handlers
  const handleLoadSuccess = useCallback((pdf: any) => {
    setIsLoading(false);
    setPdfError(null);
    setNumPages(pdf.numPages);
    onLoad?.();
  }, [onLoad]);

  const handleLoadError = useCallback(
    (error: any) => {
      console.error('PDF failed to load:', resource.url, error);
      setIsLoading(false);
      setPdfError('PDF preview failed to load');
      onError?.();
    },
    [resource.url, onError]
  );

  // Memoize document options
  const documentProps = useMemo(
    () => ({
      loading: null,
      file: resource.url,
      onLoadSuccess: handleLoadSuccess,
      onLoadError: handleLoadError,
      options,
    }),
    [resource.url, handleLoadSuccess, handleLoadError]
  );

  // Calculate page width for responsive design with zoom
  const pageWidth = useMemo(() => {
    if (!containerWidth) return undefined;
    const baseWidth = Math.min(containerWidth - 32, 800);
    return baseWidth * scale;
  }, [containerWidth, scale]);

  // Render all pages
  const renderPages = useMemo(() => {
    if (numPages === 0) return null;

    return Array.from({ length: numPages }, (_, index) => (
      <Box
        key={index + 1}
        data-page-number={index + 1}
        sx={{
          mb: 2,
          '&:last-child': { mb: 0 },
          '& .react-pdf__Page': {
            display: 'flex',
            justifyContent: 'center',
            '& canvas': {
              maxWidth: '100%',
              height: 'auto !important',
              width: 'auto !important',
              boxShadow: (theme) => theme.shadows[4],
              borderRadius: 1,
            },
          },
        }}
      >
        <Page
          pageNumber={index + 1}
          width={pageWidth}
          renderTextLayer={false}
          renderAnnotationLayer={false}
          loading={null}
        />
      </Box>
    ));
  }, [numPages, pageWidth]);

  // Early return for no URL or error
  if (!resource.url || pdfError) {
    return <PdfErrorFallback resource={resource} error={pdfError || undefined} />;
  }

  return (
    <Box
      ref={containerRef}
      sx={{
        width: '100%',
        minHeight: '70vh',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
        bgcolor: 'background.paper',
      }}
    >
      {isLoading && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'background.paper',
            zIndex: 1,
          }}
        >
          <LoadingScreen />
        </Box>
      )}

      <Box
        ref={scrollContainerRef}
        sx={{
          flex: 1,
          overflow: 'auto',
          p: 2,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Fade in={!isLoading} timeout={300}>
          <Stack spacing={2} sx={{ width: '100%', alignItems: 'center' }}>
            <Document {...documentProps}>
              {renderPages}
            </Document>
          </Stack>
        </Fade>
      </Box>

      {/* Enhanced pagination and zoom controls */}
      {numPages > 1 && (
        <Box
          sx={{
            position: 'absolute',
            bottom: 16,
            left: '50%',
            transform: 'translateX(-50%)',
            zIndex: 10,
            bgcolor: 'rgba(0, 0, 0, 0.8)',
            borderRadius: 2,
            px: 2,
            py: 1,
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            minWidth: '280px',
            justifyContent: 'center',
          }}
        >
          {/* Page navigation controls */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Tooltip title="Previous page">
              <IconButton
                size="small"
                onClick={goToPreviousPage}
                disabled={currentPage <= 1}
                sx={{
                  color: 'white',
                  '&:disabled': { color: 'rgba(255, 255, 255, 0.3)' },
                  '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.1)' },
                }}
              >
                <Iconify icon="material-symbols:chevron-left" width={20} />
              </IconButton>
            </Tooltip>

            <Typography 
              variant="body2" 
              sx={{ 
                color: 'white', 
                minWidth: '80px', 
                textAlign: 'center',
                fontSize: '0.875rem',
              }}
            >
              {currentPage} / {numPages}
            </Typography>

            <Tooltip title="Next page">
              <IconButton
                size="small"
                onClick={goToNextPage}
                disabled={currentPage >= numPages}
                sx={{
                  color: 'white',
                  '&:disabled': { color: 'rgba(255, 255, 255, 0.3)' },
                  '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.1)' },
                }}
              >
                <Iconify icon="material-symbols:chevron-right" width={20} />
              </IconButton>
            </Tooltip>
          </Box>

          {/* Divider */}
          <Box sx={{ 
            width: '1px', 
            height: '24px', 
            bgcolor: 'rgba(255, 255, 255, 0.3)',
            mx: 1,
          }} />

          {/* Zoom controls */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Tooltip title="Zoom out">
              <IconButton
                size="small"
                onClick={zoomOut}
                disabled={scale <= 0.5}
                sx={{
                  color: 'white',
                  '&:disabled': { color: 'rgba(255, 255, 255, 0.3)' },
                  '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.1)' },
                }}
              >
                <Iconify icon="material-symbols:zoom-out" width={18} />
              </IconButton>
            </Tooltip>

            <Tooltip title="Reset zoom">
              <Typography 
                variant="body2" 
                onClick={resetZoom}
                sx={{ 
                  color: 'white',
                  minWidth: '50px',
                  textAlign: 'center',
                  cursor: 'pointer',
                  fontSize: '0.75rem',
                  '&:hover': { 
                    bgcolor: 'rgba(255, 255, 255, 0.1)',
                    borderRadius: 1,
                  },
                  px: 0.5,
                  py: 0.25,
                  transition: 'background-color 0.2s',
                }}
              >
                {Math.round(scale * 100)}%
              </Typography>
            </Tooltip>

            <Tooltip title="Zoom in">
              <IconButton
                size="small"
                onClick={zoomIn}
                disabled={scale >= 3.0}
                sx={{
                  color: 'white',
                  '&:disabled': { color: 'rgba(255, 255, 255, 0.3)' },
                  '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.1)' },
                }}
              >
                <Iconify icon="material-symbols:zoom-in" width={18} />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
      )}
    </Box>
  );
});

PdfViewer.displayName = 'PdfViewer';

export default memo(PdfViewer); 
