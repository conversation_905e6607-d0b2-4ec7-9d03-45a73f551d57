import type { Resource } from 'src/types';

import { memo, useState, useEffect, useCallback } from 'react';

import { Box, Paper, Typography } from '@mui/material';

import { Iconify } from 'src/components/iconify';
import { LoadingScreen } from 'src/components/loading-screen';

interface TextViewerProps {
  resource: Resource;
  onLoad?: () => void;
  onError?: () => void;
}

const TextViewer = ({ resource, onLoad, onError }: TextViewerProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [textContent, setTextContent] = useState<string>('');
  const [errorMessage, setErrorMessage] = useState<string>('');

  const fetchTextContent = useCallback(async () => {
    if (!resource.url) {
      setHasError(true);
      setErrorMessage('No URL provided for text file');
      setIsLoading(false);
      onError?.();
      return;
    }

    try {
      setIsLoading(true);
      setHasError(false);
      const response = await fetch(resource.url);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch text content: ${response.status} ${response.statusText}`);
      }
      
      const contentType = response.headers.get('content-type');
      if (contentType && !contentType.includes('text/') && !contentType.includes('application/json')) {
        throw new Error('File does not appear to be a text file');
      }
      
      const text = await response.text();
      setTextContent(text);
      setIsLoading(false);
      onLoad?.();
    } catch (error) {
      console.error('Failed to fetch text content:', error);
      setHasError(true);
      setErrorMessage(error instanceof Error ? error.message : 'Failed to load text content');
      setIsLoading(false);
      onError?.();
    }
  }, [resource.url, onLoad, onError]);

  useEffect(() => {
    fetchTextContent();
  }, [fetchTextContent]);

  if (isLoading) {
    return (
      <Box
        sx={{
          width: '100%',
          minHeight: '50vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: 'background.paper',
          borderRadius: 2,
        }}
      >
        <LoadingScreen />
      </Box>
    );
  }

  if (hasError) {
    return (
      <Box
        sx={{
          width: '100%',
          aspectRatio: '16/9',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: 'background.paper',
          border: '1px dashed',
          borderColor: 'error.main',
          borderRadius: 2,
          p: 3,
        }}
      >
        <Iconify 
          icon="material-symbols:error-outline" 
          sx={{ fontSize: 48, mb: 2, color: 'error.main' }} 
        />
        <Typography variant="h6" color="error" textAlign="center" sx={{ mb: 1 }}>
          Text file failed to load
        </Typography>
        <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ mb: 2 }}>
          {resource.name}
        </Typography>
        <Typography variant="caption" color="text.secondary" textAlign="center">
          {errorMessage}
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        width: '100%',
        minHeight: '70vh',
        position: 'relative',
        borderRadius: 2,
        overflow: 'hidden',
        bgcolor: 'background.paper',
      }}
    >
      {/* Header with file info */}
      <Box
        sx={{
          p: 2,
          borderBottom: '1px solid',
          borderColor: 'divider',
          bgcolor: 'background.neutral',
          display: 'flex',
          alignItems: 'center',
          gap: 2,
        }}
      >
        <Iconify 
          icon="material-symbols:description" 
          sx={{ fontSize: 24, color: 'primary.main' }} 
        />
        <Box sx={{ flex: 1 }}>
          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
            {resource.name}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            Text Document • {textContent.length} characters
          </Typography>
        </Box>
      </Box>

      {/* Text content */}
      <Paper
        variant="outlined"
        sx={{
          m: 2,
          maxHeight: 'calc(70vh - 100px)',
          overflow: 'auto',
          bgcolor: 'background.paper',
        }}
      >
        <Box
          component="pre"
          sx={{
            p: 3,
            margin: 0,
            fontFamily: 'monospace',
            fontSize: '0.875rem',
            lineHeight: 1.6,
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-word',
            color: 'text.primary',
            bgcolor: 'transparent',
            border: 'none',
            outline: 'none',
            userSelect: 'text',
            '&::-webkit-scrollbar': {
              width: 8,
            },
            '&::-webkit-scrollbar-track': {
              backgroundColor: 'transparent',
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: 'divider',
              borderRadius: 4,
            },
            '&::-webkit-scrollbar-thumb:hover': {
              backgroundColor: 'action.hover',
            },
          }}
        >
          {textContent || 'File appears to be empty'}
        </Box>
      </Paper>

      {/* File statistics */}
      <Box
        sx={{
          position: 'absolute',
          bottom: 16,
          right: 16,
          bgcolor: 'rgba(0, 0, 0, 0.8)',
          borderRadius: 1,
          p: 1.5,
          display: 'flex',
          alignItems: 'center',
          gap: 1,
        }}
      >
        <Typography variant="caption" sx={{ color: 'white' }}>
          {textContent.split('\n').length} lines • {textContent.length} chars
        </Typography>
      </Box>
    </Box>
  );
};

export default memo(TextViewer); 
