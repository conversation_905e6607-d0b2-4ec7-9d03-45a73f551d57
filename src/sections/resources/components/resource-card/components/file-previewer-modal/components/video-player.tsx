import type { Resource } from 'src/types';

import { memo } from 'react';

import { VideoPlayer } from 'src/components/video-player';

interface VideoPlayerProps {
  resource: Resource;
  onLoad?: () => void;
  onError?: () => void;
}

const VideoPlayerViewer = ({ resource, onLoad, onError,  }: VideoPlayerProps) => <VideoPlayer
  src={resource.url}
  poster={resource.thumbnailUrl}
  resource={resource}
  mode="full"
  showControls
  enableThumbnailCapture
  title={resource.name}
  onLoad={onLoad}
  onError={onError}
/>;

export default memo(VideoPlayerViewer);
