import type { Resource } from 'src/types';

import { lazy, useRef, Suspense, useState, useEffect, useCallback } from 'react';

import Box from '@mui/material/Box';
import Dialog from '@mui/material/Dialog';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import DialogContent from '@mui/material/DialogContent';

import { Iconify } from 'src/components/iconify';
import { fileFormat } from 'src/components/file-thumbnail';
import { LoadingScreen } from 'src/components/loading-screen';

import { useFilePreviewer } from './file-previewer-provider';

const VideoPlayer = lazy(() => import('./components/video-player'));
const AudioPlayer = lazy(() => import('./components/audio-player'));
const ImageViewer = lazy(() => import('./components/image-viewer'));
const PdfViewer = lazy(() => import('./components/pdf-viewer'));
const DocumentViewer = lazy(() => import('./components/document-viewer'));
const TextViewer = lazy(() => import('./components/text-viewer'));
const MarkdownViewer = lazy(() => import('./components/markdown-viewer'));

const PlaceholderViewer = ({ resource }: { resource: Resource }) => (
  <Box
    sx={{
      width: '100%',
      aspectRatio: '16/9',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      bgcolor: 'background.paper',
      borderRadius: 2,
      p: 3,
    }}
  >
    <Box sx={{ textAlign: 'center' }}>
      <Iconify icon="material-symbols:preview" sx={{ fontSize: 48, mb: 2, opacity: 0.5 }} />
      <Box sx={{ typography: 'h6', mb: 1 }}>Preview Coming Soon</Box>
      <Box sx={{ typography: 'body2', color: 'text.secondary' }}>
        {resource.name} ({fileFormat(resource.fileName || resource.name || '')})
      </Box>
    </Box>
  </Box>
);

const PreviewComponentsMap: Record<string, React.ComponentType<{ resource: Resource; onLoad?: () => void; onError?: () => void }>> = {
  video: VideoPlayer,
  audio: AudioPlayer,
  image: ImageViewer,
  pdf: PdfViewer,
  document: DocumentViewer,
  word: DocumentViewer,
  powerpoint: DocumentViewer,
  txt: TextViewer,
  markdown: MarkdownViewer,
};

export const FilePreviewerModal = () => {
  const { 
    currentResource, 
    isOpen, 
    closePreview, 
    goToNext, 
    goToPrevious, 
    hasNext, 
    hasPrevious,
  } = useFilePreviewer();
  
  // Controls visibility management - using stable useState
  const [showControls, setShowControls] = useState(true);
  const hideControlsTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // Local state for loading/error management - using stable useState
  const [isError, setIsError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Reset states when resource changes
  useEffect(() => {
    if (currentResource) {
      setIsError(false);
      setIsLoading(true);
      setShowControls(true); // Show controls immediately when resource opens
      
      // Clear any existing timeout
      if (hideControlsTimeoutRef.current) {
        clearTimeout(hideControlsTimeoutRef.current);
        hideControlsTimeoutRef.current = null;
      }
    }
  }, [currentResource]);

  // Auto-hide controls after inactivity - now stable with no dependencies
  const scheduleHideControls = useCallback(() => {
    if (hideControlsTimeoutRef.current) {
      clearTimeout(hideControlsTimeoutRef.current);
    }
    
    hideControlsTimeoutRef.current = setTimeout(() => {
      setShowControls(false);
    }, 3000); // Hide after 3 seconds of inactivity
  }, []);

  // Show controls and schedule auto-hide - now stable
  const handleMouseActivity = useCallback(() => {
    setShowControls(true);
    scheduleHideControls();
  }, [scheduleHideControls]);

  // Clear timeout on unmount
  useEffect(() => () => {
      if (hideControlsTimeoutRef.current) {
        clearTimeout(hideControlsTimeoutRef.current);
      }
    }, []);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return;
      
      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault();
          if (hasPrevious) goToPrevious();
          break;
        case 'ArrowRight':
          event.preventDefault();
          if (hasNext) goToNext();
          break;
        case 'Escape':
          event.preventDefault();
          closePreview();
          break;
        default:
          break;
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, hasNext, hasPrevious, goToNext, goToPrevious, closePreview]);

  // Stable callbacks with minimal dependencies
  const handleError = useCallback(() => {
    if (currentResource) {
      console.error('Resource failed to load:', currentResource.url);
      setIsError(true);
      setIsLoading(false);
    }
  }, [currentResource]);

  const handleLoad = useCallback(() => {
    setIsError(false);
    setIsLoading(false);
    // Start auto-hide timer when resource is ready
    scheduleHideControls();
  }, [scheduleHideControls]);

  // Don't render if no resource is selected
  if (!currentResource) {
    return null;
  }

  // Determine the file format to select the appropriate preview component
  const format = fileFormat(currentResource.fileName || currentResource.name || '');
  const PreviewComponent = PreviewComponentsMap[format] || PlaceholderViewer;

  return (
    <Dialog
      open={isOpen}
      onClose={closePreview}
      fullScreen
      fullWidth
    >
      <DialogContent sx={{ p: 0, position: 'relative', display: 'flex', flexDirection: 'column', height: '100%' }}>
        {/* Header with file name and navigation controls */}
        <Box
          sx={{
            position: 'relative',
            zIndex: 20,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            px: 3,
            py: 2,
            bgcolor: (theme) => 
              theme.palette.mode === 'dark' 
                ? 'rgba(0, 0, 0, 0.6)' 
                : 'rgba(255, 255, 255, 0.9)',
            backdropFilter: 'blur(4px)',
            boxShadow: (theme) => theme.palette.mode === 'dark' ? theme.shadows[24] : theme.shadows[8],
            transition: (theme) => theme.transitions.create('opacity', {
              duration: theme.transitions.duration.short,
            }),
          }}
        >
          {/* Center section: File name and counter */}
          <Box sx={{ 
            display: 'flex', 
            flexDirection: 'column', 
            alignItems: 'center',
            flex: 1,
            mx: 2,
          }}>
            <Typography
              variant="h6"
              sx={{
                color: (theme) => 
                  theme.palette.mode === 'dark' 
                    ? theme.palette.common.white 
                    : theme.palette.text.primary,
                fontWeight: 500,
                fontSize: '1rem',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                maxWidth: '100%',
                textAlign: 'center',
              }}
            >
              {currentResource.fileName || currentResource.name || 'Untitled'}
            </Typography>
            
          </Box>

          {/* Right section: Close button */}
          <Box sx={{ display: 'flex', alignItems: 'center', minWidth: '60px', justifyContent: 'flex-end' }}>
            <IconButton
              onClick={closePreview}
              sx={{
                color: (theme) => 
                  theme.palette.mode === 'dark' 
                    ? theme.palette.common.white 
                    : theme.palette.text.primary,
                '&:hover': {
                  bgcolor: (theme) => 
                    theme.palette.mode === 'dark' 
                      ? 'rgba(255, 255, 255, 0.1)' 
                      : 'rgba(0, 0, 0, 0.04)',
                },
              }}
            >
              <Iconify icon="material-symbols:close" />
            </IconButton>
          </Box>
        </Box>

        {/* Preview container */}
        <Box
          sx={{
            width: '100%',
            position: 'relative',
            bgcolor: 'black',
            overflow: 'hidden',
            minHeight: isError || isLoading ? '200px' : 'auto',
            cursor: showControls ? 'default' : 'none',
            display: 'flex',
            justifyContent: 'center',
            flexGrow: 1,
          }}
          onMouseMove={handleMouseActivity}
          onMouseEnter={handleMouseActivity}
          onClick={handleMouseActivity}
        >
          {/* Previous navigation button overlay */}
          {hasPrevious && (
            <IconButton
              onClick={goToPrevious}
              sx={{
                position: 'absolute',
                left: 20,
                top: '50%',
                transform: 'translateY(-50%)',
                zIndex: 30,
                color: 'white',
                bgcolor: 'rgba(0, 0, 0, 0.5)',
                backdropFilter: 'blur(4px)',
                opacity: showControls ? 0.9 : 0,
                transition: (theme) => theme.transitions.create(['opacity', 'background-color'], {
                  duration: theme.transitions.duration.short,
                }),
                '&:hover': {
                  bgcolor: 'rgba(0, 0, 0, 0.7)',
                  opacity: 1,
                },
                width: 56,
                height: 56,
              }}
            >
              <Iconify icon="material-symbols:chevron-left" sx={{ fontSize: 32 }} />
            </IconButton>
          )}

          {/* Next navigation button overlay */}
          {hasNext && (
            <IconButton
              onClick={goToNext}
              sx={{
                position: 'absolute',
                right: 20,
                top: '50%',
                transform: 'translateY(-50%)',
                zIndex: 30,
                color: 'white',
                bgcolor: 'rgba(0, 0, 0, 0.5)',
                backdropFilter: 'blur(4px)',
                opacity: showControls ? 0.9 : 0,
                transition: (theme) => theme.transitions.create(['opacity', 'background-color'], {
                  duration: theme.transitions.duration.short,
                }),
                '&:hover': {
                  bgcolor: 'rgba(0, 0, 0, 0.7)',
                  opacity: 1,
                },
                width: 56,
                height: 56,
              }}
            >
              <Iconify icon="material-symbols:chevron-right" sx={{ fontSize: 32 }} />
            </IconButton>
          )}
          
          <Suspense fallback={<LoadingScreen />}>
            <PreviewComponent
              resource={currentResource}
              onLoad={handleLoad}
              onError={handleError}
            />
          </Suspense>
        </Box>
      </DialogContent>
    </Dialog>
  );
};
