import type { ReactNode } from 'react';
import type { Resource } from 'src/types';

import { useMemo, useState, useContext, useCallback, createContext } from 'react';

interface FilePreviewerState {
  currentResource: Resource | null;
  currentIndex: number;
  isOpen: boolean;
}

interface FilePreviewerContextType extends FilePreviewerState {
  openPreview: (resource: Resource, resources?: Resource[]) => void;
  closePreview: () => void;
  goToNext: () => void;
  goToPrevious: () => void;
  hasNext: boolean;
  hasPrevious: boolean;
  resources: Resource[];
}

const FilePreviewerContext = createContext<FilePreviewerContextType | undefined>(undefined);

interface FilePreviewerProviderProps {
  children: ReactNode;
  resources: Resource[];
}

export const FilePreviewerProvider = ({ children, resources }: FilePreviewerProviderProps) => {
  const [state, setState] = useState<FilePreviewerState>({
    currentResource: null,
    currentIndex: -1,
    isOpen: false,
  });

  const openPreview = useCallback((resource: Resource) => {
    const resourceList = resources.length > 0 ? resources : [resource];
    const currentIndex = resourceList.findIndex((r) => r.id === resource.id);
    
    setState({
      currentResource: resource,
      currentIndex: Math.max(0, currentIndex),
      isOpen: true,
    });
  }, [resources]);

  const closePreview = useCallback(() => {
    setState({
      currentResource: null,
      currentIndex: -1,
      isOpen: false,
    });
  }, []);

  const goToNext = useCallback(() => {
    setState((prevState) => {
      if (prevState.currentIndex < resources.length - 1) {
        const nextIndex = prevState.currentIndex + 1;
        return {
          ...prevState,
          currentResource: resources[nextIndex],
          currentIndex: nextIndex,
        };
      }
      return prevState;
    });
  }, [resources]);

  const goToPrevious = useCallback(() => {
    setState((prevState) => {
      if (prevState.currentIndex > 0) {
        const prevIndex = prevState.currentIndex - 1;
        return {
          ...prevState,
          currentResource: resources[prevIndex],
          currentIndex: prevIndex,
        };
      }
      return prevState;
    });
  }, [resources]);

  const hasNext = state.currentIndex < resources.length - 1;
  const hasPrevious = state.currentIndex > 0;

  // Memoize context value to prevent unnecessary re-renders
  const contextValue: FilePreviewerContextType = useMemo(() => ({
    ...state,
    openPreview,
    closePreview,
    goToNext,
    goToPrevious,
    hasNext,
    hasPrevious,
    resources,
  }), [
    state,
    openPreview,
    closePreview,
    goToNext,
    goToPrevious,
    hasNext,
    hasPrevious,
    resources,
  ]);

  return (
    <FilePreviewerContext.Provider value={contextValue}>
      {children}
    </FilePreviewerContext.Provider>
  );
};

export const useFilePreviewer = (): FilePreviewerContextType => {
  const context = useContext(FilePreviewerContext);
  if (context === undefined) {
    throw new Error('useFilePreviewer must be used within a FilePreviewerProvider');
  }
  return context;
};
