import type { Note } from 'src/store/api/notes/types';
import type { ProjectRole } from 'src/store/api/projects';
import type { NoteCreatedEvent, NoteDeletedEvent, NoteUpdatedEvent} from 'src/types';

import { toast } from 'sonner';
import { useDispatch } from 'react-redux';
import { useEffect, useCallback } from 'react';

import LoadingButton from '@mui/lab/LoadingButton';

import { useEventListener } from 'src/hooks/use-event-listener';

import { SSEEventType } from 'src/types';
import { useAppSelector } from 'src/store';
import { selectSelectedNote } from 'src/store/slices/notes/selectors';
import { addNote, deleteNote, selectNote, storeNotes } from 'src/store/slices/notes/slice';
import {
  useCreateNoteMutation,
  useDeleteNoteMutation,
  useGetNotesByResourceQuery,
} from 'src/store/api/notes';

import { NoteDialog } from 'src/components/notes';
import { Scrollbar } from 'src/components/scrollbar';
import { NoteList } from 'src/components/notes/note-list';
import { LoadingScreen } from 'src/components/loading-screen';

interface NotesSectionProps {
  resourceId: string;
  isResourceHasNotes: boolean;
  canComment?: boolean;
  onClose?: () => void;
  onMaximize?: () => void;
  role?: ProjectRole | string;
  isOwner?: boolean;
  currentUserId?: string;
}

export function NotesSection({
  resourceId,
  isResourceHasNotes,
  canComment = false,
  onClose,
  onMaximize,
  role,
  isOwner = false,
  currentUserId = '',
}: NotesSectionProps) {
  const dispatch = useDispatch();
  const selectedNote = useAppSelector(selectSelectedNote);

  const [createNote, { isLoading: isCreating }] = useCreateNoteMutation();
  const [deleteNoteMutation, { isLoading: isDeleting }] = useDeleteNoteMutation();
  const {
    data: apiNotes = [],
    isLoading: isApiLoading,
    refetch,
  } = useGetNotesByResourceQuery(
    { resourceId },
    {
      refetchOnMountOrArgChange: true,
    }
  );

  useEffect(() => {
    if (!isApiLoading && apiNotes) {
      dispatch(storeNotes(apiNotes));
    }
  }, [dispatch, isApiLoading, apiNotes]);

  const handleSelectNote = (note: Note) => {
    dispatch(selectNote(note));
  };

  const handleCloseDialog = () => {
    refetch();
    dispatch(selectNote(null));
  };

  const handleAddNewNote = useCallback(() => {
    createNote({
      payload: {
        content: '',
        title: 'New Note222',
        resourceId,
      },
    })
      .then((res) => {
        console.log('Note created', res);
        if (res.data) {
          handleSelectNote(res.data);
          dispatch(addNote(res.data));
        }
      })
      .catch((err) => {
        toast.error('Failed to add new note');
      });
  }, [createNote, resourceId, handleSelectNote]);

  const handleDelete = async (noteId: string) => {
    try {
      await deleteNoteMutation({ id: noteId });
      dispatch(deleteNote(noteId));
      await refetch();
      toast.success('Note deleted successfully');
    } catch (error) {
      toast.error('Failed to delete note');
    }
  };

  useEventListener(SSEEventType.NOTE_CREATED, async (event: NoteCreatedEvent) => {
    // check if the note is not created by the current user
    if (event.data.createdById !== currentUserId) {
      await refetch();
      toast.success('New note created', {
        description: `New note created by ${event.data.createdBy}`,
      });
    }
  });

  useEventListener(SSEEventType.NOTE_UPDATED,  async (event: NoteUpdatedEvent) => {
    // check if the note is not updated by the current user
    if (event.data.updatedById !== currentUserId) {
      await refetch();
      toast.success('Note updated', {
        description: `Note updated by ${event.data.updatedBy}`,
      });
    }
  });

  useEventListener(SSEEventType.NOTE_DELETED, async (event: NoteDeletedEvent) => {
    // check if the note is not deleted by the current user
    if (event.data.deletedById !== currentUserId) {
      await refetch();
      toast.success('Note deleted', {
        description: `Note deleted by ${event.data.deletedBy}`,
      });
    }
  });

  if (isApiLoading) {
    return <LoadingScreen />;
  }

  return (
    <Scrollbar sx={{ height: 300, mt: 1, p: 2 }}>
      <NoteList
        notes={apiNotes}
        onSelectNote={handleSelectNote}
        onDelete={handleDelete}
        isCanDeleteResource={isResourceHasNotes}
        role={role}
        isOwner={isOwner}
        currentUserId={currentUserId}
      />
      {canComment && (
        <LoadingButton
          sx={{ mt: 2 }}
          variant="contained"
          color="primary"
          onClick={handleAddNewNote}
          loading={isCreating}
        >
          Add Note
        </LoadingButton>
      )}

      <NoteDialog
        open={Boolean(selectedNote)}
        onClose={handleCloseDialog}
        isCanEdit={canComment}
        role={role}
        isOwner={isOwner}
        currentUserId={currentUserId}
      />
    </Scrollbar>
  );
}
