import type { Resource } from 'src/types';

import { useState, useRef, useEffect } from 'react';

import { Box, Stack, Typography } from '@mui/material';

interface ResourceAudioPreviewerProps {
  data: Resource;
  seekTime?: number;
  shouldAutoPlay?: boolean;
}

const ResourceAudioPreviewer: React.FC<ResourceAudioPreviewerProps> = ({ data, seekTime, shouldAutoPlay }) => {
  const [audioError, setAudioError] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);

  // Handle seek time updates when audio is already loaded
  useEffect(() => {
    if (seekTime !== undefined && audioRef.current) {
      audioRef.current.currentTime = seekTime;
      if (shouldAutoPlay) {
        audioRef.current.play().catch(() => {
          // If autoplay fails, that's okay - user can manually play
        });
      }
    }
  }, [seekTime, shouldAutoPlay]);

  const handleAudioError = () => {
    console.error('Audio failed to load:', data.url);
    setAudioError(true);
  };

  // If no URL or audio failed to load, show error message
  if (!data.url || audioError) {
    return (
      <Box
        sx={{
          height: 100,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          p: 2,
        }}
      >
        <Typography variant="body2" color="error" textAlign="center">
          {!data.url
            ? 'Audio URL not available. The file may still be processing.'
            : 'Failed to load audio file. The file may not be ready yet or the URL is invalid.'}
        </Typography>
        <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
          Please try refreshing the page or check back later.
        </Typography>
      </Box>
    );
  }

  return (
    <Stack direction="row" justifyContent="center" alignItems="center" sx={{ height: 100 }}>
      <audio 
        ref={audioRef}
        controls 
        crossOrigin="anonymous" 
        style={{ width: '100%' }} 
        onError={handleAudioError}
        onLoadedMetadata={() => {
          if (seekTime !== undefined && audioRef.current) {
            audioRef.current.currentTime = seekTime;
            if (shouldAutoPlay) {
              audioRef.current.play().catch(() => {
                // If autoplay fails, that's okay - user can manually play
              });
            }
          }
        }}
      >
        <source src={data.url} type="audio/mp3" />
      </audio>
    </Stack>
  );
};

export default ResourceAudioPreviewer;
