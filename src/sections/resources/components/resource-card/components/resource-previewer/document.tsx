import { useCallback } from 'react';

import { Box, Button, Typography } from '@mui/material';

import { Image } from 'src/components/image';
import { Iconify } from 'src/components/iconify';
import { fileThumb, fileFormat } from 'src/components/file-thumbnail';

import { useFilePreviewer } from '../file-previewer-modal/file-previewer-provider';

import type { ResourceItem } from '../../../resources-list';

const ResourceDocumentPreviewer: React.FC<{ data: ResourceItem }> = ({ data }) => {
  const format = fileFormat(data.fileName || data.name || '');
  const iconSrc = fileThumb(format);
  const fileName = data.fileName || data.name || '';

  const { openPreview } = useFilePreviewer();

  // Get file extension for display
  const fileExtension = fileName.split('.').pop()?.toUpperCase() || format.toUpperCase();

  const handleDownload = () => {
    if (data.url) {
      const link = document.createElement('a');
      link.href = data.url;
      link.download = fileName;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // Handle click to open preview
  const handleClick = useCallback(() => {
    if (data.url) {
      // Convert ResourceItem to Resource format expected by the previewer
      const resource = {
        ...data,
        fileName: data.fileName || data.name || '',
      };
      openPreview(resource);
    }
  }, [data, openPreview]);

  const getDocumentTypeName = (formatType: string) => {
    switch (formatType) {
      case 'word':
        return 'Microsoft Word Document';
      case 'powerpoint':
        return 'Microsoft PowerPoint Presentation';
      default:
        return 'Document';
    }
  };

  return (
    <Box
      onClick={handleClick}
      sx={{
        width: '100%',
        height: 400,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        bgcolor: 'background.paper',
        border: '1px dashed',
        borderColor: 'divider',
        borderRadius: 1,
        p: 4,
        textAlign: 'center',
        cursor: data.url ? 'pointer' : 'default',
        transition: (theme) =>
          theme.transitions.create(['transform', 'box-shadow', 'border-color'], {
            duration: theme.transitions.duration.short,
          }),
        '&:hover': data.url
          ? {
              transform: 'scale(1.02)',
              boxShadow: (theme) => theme.shadows[4],
              borderColor: 'primary.main',
            }
          : {},
      }}
    >
      <Image
        visibleByDefault
        src={iconSrc}
        alt={`${format} file`}
        sx={{
          width: 120,
          height: 120,
          mb: 2,
          '& .minimal__image__img': { objectFit: 'contain' },
        }}
      />

      <Typography
        variant="caption"
        sx={{
          fontWeight: 'bold',
          backgroundColor: format === 'word' ? '#2B579A' : '#D24726',
          color: 'white',
          px: 1.5,
          py: 0.5,
          borderRadius: 1,
          fontSize: '0.75rem',
          mb: 2,
        }}
      >
        {fileExtension}
      </Typography>

      <Typography variant="h6" color="text.primary" sx={{ mb: 1 }}>
        {getDocumentTypeName(format)}
      </Typography>

      <Typography variant="body2" color="text.secondary" sx={{ mb: 3, maxWidth: 300 }}>
        Click to preview or download the file to view its contents.
      </Typography>

      {data.url && (
        <Button
          variant="contained"
          onClick={(e) => {
            e.stopPropagation(); // Prevent triggering the parent click
            handleDownload();
          }}
          startIcon={<Iconify icon="material-symbols:download" />}
          sx={{
            backgroundColor: format === 'word' ? '#2B579A' : '#D24726',
            '&:hover': {
              backgroundColor: format === 'word' ? '#1f4378' : '#b5381f',
            },
          }}
        >
          Download {fileExtension}
        </Button>
      )}
    </Box>
  );
};

export default ResourceDocumentPreviewer;
