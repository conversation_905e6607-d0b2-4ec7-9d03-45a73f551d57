import { memo, lazy, useRef, useMemo, Suspense, useState, useEffect } from 'react';

import { Box, Skeleton } from '@mui/material';

import { CONFIG } from 'src/global-config';

import { fileFormat } from 'src/components/file-thumbnail';
import { EmptyContent } from 'src/components/empty-content';

import type { ResourceItem } from '../../../resources-list';

// Lazy load heavy components
const ResourcePdfPreviewer = lazy(() => import('./pdf'));
const ResourceTextPreviewer = lazy(() => import('./text'));
const ResourceVideoPreviewer = lazy(() => import('./video'));
const ResourceAudioPreviewer = lazy(() => import('./audio'));
const ResourceImagePreviewer = lazy(() => import('./image'));
const ResourceDocumentPreviewer = lazy(() => import('./document'));

// Define which formats support seek time and auto play
const formatsWithSeekSupport = new Set(['video', 'audio']);

const PreviewerComponentsMap: Record<
  string,
  React.LazyExoticComponent<React.FC<{ data: ResourceItem }>>
> = {
  video: ResourceVideoPreviewer,
  audio: ResourceAudioPreviewer,
  pdf: ResourcePdfPreviewer,
  txt: ResourceTextPreviewer,
  image: ResourceImagePreviewer,
  word: ResourceDocumentPreviewer,
  powerpoint: ResourceDocumentPreviewer,
};

// Loading skeleton for previewer
const PreviewerSkeleton = memo(() => (
  <Box
    sx={{
      width: '100%',
      height: 400,
      display: 'flex',
      flexDirection: 'column',
      gap: 2,
    }}
  >
    <Skeleton variant="rectangular" width="100%" height={300} sx={{ borderRadius: 1 }} />
    <Skeleton variant="text" width="60%" />
    <Skeleton variant="text" width="40%" />
  </Box>
));

PreviewerSkeleton.displayName = 'PreviewerSkeleton';

interface ResourcePreviewerProps {
  data: ResourceItem;
  enableLazyLoading?: boolean;
  seekTime?: number;
  shouldAutoPlay?: boolean;
}

const ResourcePreviewer = memo<ResourcePreviewerProps>(({ data, enableLazyLoading = true, seekTime, shouldAutoPlay }) => {
  const [isVisible, setIsVisible] = useState(!enableLazyLoading);
  const [hasIntersected, setHasIntersected] = useState(!enableLazyLoading);
  const observerRef = useRef<HTMLDivElement>(null);

  // Memoize file format calculation
  const format = useMemo(
    () => fileFormat(data.fileName || data.name || ''),
    [data.fileName, data.name]
  );

  // Intersection observer for lazy loading
  useEffect(() => {
    if (!enableLazyLoading || hasIntersected) return undefined;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          setHasIntersected(true);
          observer.disconnect();
        }
      },
      {
        rootMargin: '50px', // Load 50px before coming into view
        threshold: 0.1,
      }
    );

    if (observerRef.current) {
      observer.observe(observerRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [enableLazyLoading, hasIntersected]);

  const PreviewerComponent = PreviewerComponentsMap[format];

  // Show placeholder if not visible yet
  if (enableLazyLoading && !isVisible) {
    return (
      <Box ref={observerRef}>
        <PreviewerSkeleton />
      </Box>
    );
  }

  // Show error state for unsupported formats
  if (!PreviewerComponent) {
    return (
      <Box ref={observerRef}>
        <EmptyContent
          title="Preview is unavailable for this file type"
          description="Please download the file to view it"
          imgUrl={`${CONFIG.assetsDir}/assets/icons/empty/ic-content.svg`}
        />
      </Box>
    );
  }

  // Render with Suspense for lazy-loaded components
  return (
    <Box ref={observerRef}>
      <Suspense fallback={<PreviewerSkeleton />}>
        {/* Only pass seekTime and shouldAutoPlay to video and audio components */}
        {formatsWithSeekSupport.has(format) ? (
          <PreviewerComponent 
            data={data} 
            seekTime={seekTime} 
            shouldAutoPlay={shouldAutoPlay} 
            {...({} as any)}
          />
        ) : (
          <PreviewerComponent data={data} />
        )}
      </Suspense>
    </Box>
  );
});

ResourcePreviewer.displayName = 'ResourcePreviewer';

export default ResourcePreviewer;
