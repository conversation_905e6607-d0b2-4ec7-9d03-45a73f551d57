import type { ResourceItem } from 'src/sections/resources/components/resources-list';

import { memo, useState, useCallback, useEffect } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import IconButton from '@mui/material/IconButton';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';

import VidstackPlayer from './vidstack-player/vidstack-player';

interface ResourceVideoPreviewerProps {
  data: ResourceItem;
  seekTime?: number;
  shouldAutoPlay?: boolean;
}

const ResourceVideoPreviewer = ({ data, seekTime, shouldAutoPlay }: ResourceVideoPreviewerProps) => {
  const [shouldLoadVideo, setShouldLoadVideo] = useState(false);

  // Auto-load video if seekTime is provided (from citation click)
  useEffect(() => {
    if (seekTime !== undefined && !shouldLoadVideo) {
      setShouldLoadVideo(true);
    }
  }, [seekTime, shouldLoadVideo]);

  const handlePlayClick = useCallback(() => {
    setShouldLoadVideo(true);
  }, []);

  return (
    <Card
      sx={{
        width: '100%',
        aspectRatio: '16/9',
        position: 'relative',
        borderRadius: 1,
        overflow: 'hidden',
        cursor: 'pointer',
      }}
    >
      {!shouldLoadVideo ? (
        // Show only thumbnail with play button overlay
        <Box
          onClick={handlePlayClick}
          sx={{
            width: '100%',
            height: '100%',
            position: 'relative',
            backgroundImage: `url(${data.thumbnailUrl})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer',
            '&:hover .play-button': {
              transform: 'scale(1.1)',
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
            },
          }}
        >
          {/* Dark overlay for better contrast */}
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.3)',
              transition: (theme) =>
                theme.transitions.create(['background-color'], {
                  duration: theme.transitions.duration.short,
                }),
            }}
          />

          {/* Play button */}
          <IconButton
            className="play-button"
            aria-label="Play video"
            sx={{
              position: 'relative',
              zIndex: 1,
              width: (theme) => theme.spacing(8), // 64px
              height: (theme) => theme.spacing(8),
              backgroundColor: 'rgba(255, 255, 255, 0.8)',
              color: 'primary.main',
              transition: (theme) =>
                theme.transitions.create(['all'], {
                  duration: theme.transitions.duration.short,
                }),
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
              },
            }}
          >
            <PlayArrowIcon sx={{ fontSize: (theme) => theme.spacing(4) }} />
          </IconButton>
        </Box>
      ) : (
        <Box
          sx={{
            width: '100%',
            height: '100%',
            position: 'relative',
          }}
        >
          <VidstackPlayer data={data} seekTime={seekTime} shouldAutoPlay={shouldAutoPlay} />
        </Box>
      )}
    </Card>
  );
};

export default memo(ResourceVideoPreviewer); 
