import buttonStyles from '../styles/button.module.css';
import tooltipStyles from '../styles/tooltip.module.css';

import { forwardRef } from 'react';
import {
  Tooltip,
  MuteButton,
  PlayButton,
  useMediaState,
  FullscreenButton,
  type TooltipPlacement,
} from '@vidstack/react';

import { Iconify } from 'src/components/iconify';

export interface MediaButtonProps {
  tooltipPlacement: TooltipPlacement;
}

export function Play({ tooltipPlacement }: MediaButtonProps) {
  const isPaused = useMediaState('paused');
  return (
    <Tooltip.Root>
      <Tooltip.Trigger asChild>
        <PlayButton className={`play-button ${buttonStyles.button}`}>
          <Iconify 
            icon={isPaused ? 'material-symbols:play-arrow' : 'material-symbols:pause'} 
            sx={{
              width: "18px !important",
              height: "18px !important",
            }}
          />
        </PlayButton>
      </Tooltip.Trigger>
      <Tooltip.Content className={tooltipStyles.tooltip} placement={tooltipPlacement}>
        {isPaused ? 'Play' : 'Pause'}
      </Tooltip.Content>
    </Tooltip.Root>
  );
}

export function Mute({ tooltipPlacement }: MediaButtonProps) {
  const volume = useMediaState('volume'),
    isMuted = useMediaState('muted');
  
  const getVolumeIcon = () => {
    if (isMuted || volume === 0) {
      return 'material-symbols:volume-off';
    }
    if (volume < 0.5) {
      return 'material-symbols:volume-down';
    }
    return 'material-symbols:volume-up';
  };

  return (
    <Tooltip.Root>
      <Tooltip.Trigger asChild>
        <MuteButton className={`mute-button ${buttonStyles.button}`}>
          <Iconify 
            icon={getVolumeIcon()} 
            sx={{
              width: "18px !important",
              height: "18px !important",
            }}
          />
        </MuteButton>
      </Tooltip.Trigger>
      <Tooltip.Content className={tooltipStyles.tooltip} placement={tooltipPlacement}>
        {isMuted ? 'Unmute' : 'Mute'}
      </Tooltip.Content>
    </Tooltip.Root>
  );
}

export function Fullscreen({ tooltipPlacement }: MediaButtonProps) {
  const isActive = useMediaState('fullscreen');
  return (
    <Tooltip.Root>
      <Tooltip.Trigger asChild>
        <FullscreenButton className={`fullscreen-button ${buttonStyles.button}`}>
          <Iconify 
            icon={isActive ? 'material-symbols:fullscreen-exit' : 'material-symbols:fullscreen'} 
            sx={{
              width: "18px !important",
              height: "18px !important",
            }}
          />
        </FullscreenButton>
      </Tooltip.Trigger>
      <Tooltip.Content className={tooltipStyles.tooltip} placement={tooltipPlacement}>
        {isActive ? 'Exit Fullscreen' : 'Enter Fullscreen'}
      </Tooltip.Content>
    </Tooltip.Root>
  );
}

export interface SettingsButtonProps extends MediaButtonProps {
  onSettingsClick?: () => void;
}

export const Settings = forwardRef<HTMLButtonElement, SettingsButtonProps>(
  ({ tooltipPlacement, onSettingsClick }, ref) => {
    const handleClick = (event: React.MouseEvent) => {
      event.preventDefault();
      event.stopPropagation();
      onSettingsClick?.();
    };

    return (
      <Tooltip.Root>
        <Tooltip.Trigger asChild>
          <button 
            ref={ref}
            className={`settings-button ${buttonStyles.button}`}
            onClick={handleClick}
            onPointerUp={handleClick}
            type="button"
            aria-label="Video Quality Settings"
          >
            <Iconify 
              icon="material-symbols:settings" 
              sx={{
                width: "18px !important",
                height: "18px !important",
              }}
            />
          </button>
        </Tooltip.Trigger>
        <Tooltip.Content className={tooltipStyles.tooltip} placement={tooltipPlacement}>
          Video Quality Settings
        </Tooltip.Content>
      </Tooltip.Root>
    );
  }
); 
