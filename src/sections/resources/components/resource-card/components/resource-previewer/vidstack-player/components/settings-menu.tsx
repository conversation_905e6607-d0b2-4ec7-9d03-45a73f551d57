import type { ResourceItem } from 'src/sections/resources/components/resources-list';

import {
  Box,
  List,
  Popover,
  Divider,
  ListItem,
  Typography,
  ListItemText,
  ListItemButton,
} from '@mui/material';

import { Iconify } from 'src/components/iconify';

export interface VideoQualityOption {
  label: string;
  url: string;
  isTranscoded?: boolean;
}

interface SettingsMenuProps {
  resource: ResourceItem;
  isOpen: boolean;
  onClose: () => void;
  anchorEl: HTMLElement | null;
  onQualityChange: (option: VideoQualityOption) => void;
  currentQuality: VideoQualityOption | null;
}

export function SettingsMenu({ 
  resource, 
  isOpen, 
  onClose, 
  anchorEl, 
  onQualityChange,
  currentQuality 
}: SettingsMenuProps) {
  // Available quality options
  const qualityOptions: VideoQualityOption[] = [
    {
      label: 'Original',
      url: resource.url,
      isTranscoded: false,
    },
    ...(resource.transcodedUrl ? [{
      label: 'Optimized',
      url: resource.transcodedUrl,
      isTranscoded: true,
    }] : []),
  ];

  const handleQualitySelect = (option: VideoQualityOption) => {
    onQualityChange(option);
    onClose();
  };

  // Show menu even with single option, but with different content
  if (qualityOptions.length <= 1) {
    return (
      <Popover
        open={isOpen}
        anchorEl={anchorEl}
        onClose={onClose}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        slotProps={{
          paper: {
            sx: {
              width: 140,
              maxHeight: 80,
              border: '1px solid',
              borderColor: 'divider',
            }
          }
        }}
      >
        <Box sx={{ p: 1.5, textAlign: 'center' }}>
          <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.65rem' }}>
            Only one quality available
          </Typography>
          <Typography variant="caption" color="text.disabled" sx={{ mt: 0.25, display: 'block', fontSize: '0.6rem' }}>
            {qualityOptions[0]?.label || 'Original Quality'}
          </Typography>
        </Box>
      </Popover>
    );
  }

  return (
    <Popover
      open={isOpen}
      anchorEl={anchorEl}
      onClose={onClose}
      anchorOrigin={{
        vertical: 'top',
        horizontal: 'center',
      }}
      transformOrigin={{
        vertical: 'bottom',
        horizontal: 'center',
      }}
      slotProps={{
        paper: {
          sx: {
            width: 140,
            maxHeight: 140,
            border: '1px solid',
            borderColor: 'divider',
          }
        }
      }}
    >
      <Box sx={{ p: 0.75 }}>
        <Typography variant="caption" sx={{ px: 0.75, py: 0.25, color: 'text.secondary', fontSize: '0.7rem' }}>
          Quality
        </Typography>
        <Divider sx={{ my: 0.25 }} />
        
        <List dense sx={{ p: 0 }}>
          {qualityOptions.map((option) => (
            <ListItem key={option.url} disablePadding>
              <ListItemButton
                onClick={() => handleQualitySelect(option)}
                selected={currentQuality?.url === option.url}
                sx={{
                  borderRadius: 0.5,
                  mx: 0.25,
                  minHeight: 28,
                  py: 0.25,
                }}
              >
                <ListItemText 
                  primary={option.label}
                  primaryTypographyProps={{
                    variant: 'caption',
                    sx: { 
                      fontWeight: 'normal',
                      fontSize: '0.7rem'
                    }
                  }}
                />
                {currentQuality?.url === option.url && (
                  <Iconify 
                    icon="material-symbols:check" 
                    sx={{ 
                      width: 12, 
                      height: 12, 
                      color: 'primary.main' 
                    }} 
                  />
                )}
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Box>
    </Popover>
  );
} 
