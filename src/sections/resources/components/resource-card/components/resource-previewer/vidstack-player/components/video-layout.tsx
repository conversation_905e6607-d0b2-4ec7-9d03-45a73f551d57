import styles from '../styles/player.module.css';

import type { ResourceItem } from 'src/sections/resources/components/resources-list';

import { useRef, useMemo, useState } from 'react';
import { Gesture, Controls } from '@vidstack/react';

import * as Buttons from './buttons';
import * as Sliders from './sliders';
import { TimeGroup } from './time-group';
import { SettingsMenu, type VideoQualityOption } from './settings-menu';

export interface VideoLayoutProps {
  thumbnails?: string;
  resource?: ResourceItem;
  onQualityChange?: (option: VideoQualityOption) => void;
}

export function VideoLayout({ thumbnails, resource, onQualityChange }: VideoLayoutProps) {
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [currentQuality, setCurrentQuality] = useState<VideoQualityOption | null>(
    resource ? { label: 'Original Quality', url: resource.url, isTranscoded: false } : null
  );
  const settingsButtonRef = useRef<HTMLButtonElement>(null);

  const handleSettingsClick = () => {
    setIsSettingsOpen(!isSettingsOpen);
  };

  const handleQualityChange = (option: VideoQualityOption) => {
    setCurrentQuality(option);
    onQualityChange?.(option);
    setIsSettingsOpen(false);
  };

  const isMultiQuality = useMemo(() => {
    if (!resource) return false;

    const transcodedCount = resource.transcodedUrl ? 1 : 0;
    const originalCount = resource.url ? 1 : 0;

    return transcodedCount + originalCount > 1;
  }, [resource]);

  return (
    <>
      <Gestures />
      <Controls.Root className={styles.controls}>
        <div className={styles.spacer} />
        <Controls.Group className={styles.controlsGroup}>
          <Sliders.Time thumbnails={thumbnails} />
        </Controls.Group>
        <Controls.Group className={styles.controlsGroup}>
          <Buttons.Play tooltipPlacement="top start" />
          <Buttons.Mute tooltipPlacement="top" />
          <Sliders.Volume />
          <TimeGroup />
          <div className={styles.spacer} />
          {resource && isMultiQuality && (
            <Buttons.Settings
              ref={settingsButtonRef}
              tooltipPlacement="top"
              onSettingsClick={handleSettingsClick}
            />
          )}
          <Buttons.Fullscreen tooltipPlacement="top end" />
        </Controls.Group>
      </Controls.Root>

      {/* Settings Menu */}
      {resource && isMultiQuality && (
        <SettingsMenu
          resource={resource}
          isOpen={isSettingsOpen}
          onClose={() => setIsSettingsOpen(false)}
          anchorEl={settingsButtonRef.current}
          onQualityChange={handleQualityChange}
          currentQuality={currentQuality}
        />
      )}
    </>
  );
}

function Gestures() {
  return (
    <>
      <Gesture
        className={styles.gesture}
        event="pointerup"
        action="toggle:paused"
      />
      <Gesture
        className={styles.gesture}
        event="dblpointerup"
        action="toggle:fullscreen"
      />
      <Gesture
        className={styles.gesture}
        event="pointerup"
        action="toggle:controls"
      />
      <Gesture
        className={styles.gesture}
        event="dblpointerup"
        action="seek:-10"
      />
      <Gesture
        className={styles.gesture}
        event="dblpointerup"
        action="seek:10"
      />
    </>
  );
} 
