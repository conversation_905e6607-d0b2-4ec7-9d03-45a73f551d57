.button {
  display: inline-flex;
  position: relative;
  justify-content: center;
  align-items: center;
  width: 28px;
  height: 28px;
  border-radius: 8px;
  cursor: pointer;
  /* Resets. */
  padding: 0;
  user-select: none;
  appearance: none;
  background: none;
  outline: none;
  border: none;
  touch-action: manipulation;
  -webkit-user-select: none;
  -webkit-tap-highlight-color: transparent;
}

/* Theme-aware icon colors */
.button {
  /* Light mode: primary color */
  color: var(--palette-primary-main);
}

/* Dark mode: white color */
@media (prefers-color-scheme: dark) {
  .button {
    color: var(--palette-common-white);
  }
}

/* For CSS variables theme switching */
[data-color-scheme="light"] .button {
  color: var(--palette-primary-main);
}

[data-color-scheme="dark"] .button {
  color: var(--palette-common-white);
}

/* Easy way to hide all buttons that are not supported in current env (e.g. fullscreen/pip). */
.button[aria-hidden='true'] {
  display: none;
}

.button > svg {
  width: 18px;
  height: 18px;
  border-radius: 2px;
}

.button[data-focus] > svg {
  box-shadow: var(--focus-ring);
}

@media (hover: hover) and (pointer: fine) {
  .button:hover {
    background-color: var(--palette-action-hover);
  }
} 
