.player {
  --media-brand: var(--palette-primary-main);
  --media-focus-ring-color: var(--palette-primary-main);
  --media-focus-ring: 0 0 0 3px var(--media-focus-ring-color);

  --media-tooltip-y-offset: 30px;
  --media-menu-y-offset: 30px;

  aspect-ratio: 16 /9;
  background-color: var(--palette-background-neutral);
  border-radius: var(--media-border-radius);
  color: var(--palette-text-primary);
  contain: layout;
  font-family: sans-serif;
  overflow: hidden;
}

.player[data-focus]:not([data-playing]) {
  box-shadow: var(--media-focus-ring);
}

.player video,
.poster {
  border-radius: var(--media-border-radius);
}

.poster {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  width: 100%;
  height: 100%;
}

.poster[data-visible] {
  opacity: 1;
}

.poster img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/*************************************************************************************************
 * Controls
 *************************************************************************************************/

.controls {
  display: flex;
  flex-direction: column;
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.2s ease-out;
}

.controls[data-visible] {
  opacity: 1;
  background-image: linear-gradient(
    to top,
    var(--palette-background-paper),
    10%,
    transparent,
    95%,
    var(--palette-background-paper)
  );
}

/* Use proper theme colors for controls */
.controls .button {
  background-color: var(--palette-background-paper);
  border: 1px solid var(--palette-divider);
  border-radius: 6px;
  backdrop-filter: blur(8px);
}

/* Theme-aware colors for control buttons */
.controls .button {
  /* Light mode: primary color */
  color: var(--palette-primary-main);
}

/* Dark mode: white color */
@media (prefers-color-scheme: dark) {
  .controls .button {
    color: var(--palette-common-white);
  }
}

/* For CSS variables theme switching */
[data-color-scheme="light"] .controls .button {
  color: var(--palette-primary-main);
}

[data-color-scheme="dark"] .controls .button {
  color: var(--palette-common-white);
}

.controls .button:hover {
  background-color: var(--palette-action-hover);
}

.controls .button > svg {
  /* Icons inherit color from parent */
}

.controlsGroup {
  display: flex;
  align-items: center;
  width: 100%;
}

.controlsGroup {
  padding-inline: 8px;
}

.controlsGroup:last-child {
  margin-top: -4px;
  padding-bottom: 8px;
}

.spacer {
  flex: 1 1 0%;
  pointer-events: none;
}

.controls :global(.mute-button) {
  margin-left: -2.5px;
  margin-right: -5px !important;
}

.controls :global(.fullscreen-button) {
  margin-right: 0 !important;
}

.controls :global(.volume-slider) {
  --media-slider-preview-offset: 30px;
}

/*************************************************************************************************
 * Gestures
 *************************************************************************************************/

.gesture {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.gesture[action='seek:-10'],
.gesture[action='seek:10'] {
  width: 20%;
  z-index: 1;
}

.gesture[action='seek:10'] {
  left: unset;
  right: 0;
}

/* Remove toggle to pause on touch. */
@media (pointer: coarse) {
  .gesture[action='toggle:paused'] {
    display: none;
  }
}

/* Remove toggle controls on mouse. */
@media not (pointer: coarse) {
  .gesture[action='toggle:controls'] {
    display: none;
  }
} 
