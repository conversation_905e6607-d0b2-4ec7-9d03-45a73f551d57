.group {
  display: flex;
  align-items: center;
  margin-left: 8px;
  padding: 2px 6px;
}

.time {
  display: inline-block;
  contain: content;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 0.025em;
}

/* Theme-aware colors for time display */
.time {
  /* Light mode: primary color */
  color: var(--palette-primary-main);
}

/* Dark mode: white color */
@media (prefers-color-scheme: dark) {
  .time {
    color: var(--palette-common-white);
  }
}

/* For CSS variables theme switching */
[data-color-scheme="light"] .time {
  color: var(--palette-primary-main);
}

[data-color-scheme="dark"] .time {
  color: var(--palette-common-white);
}

.divider {
  margin: 0 2.5px;
}

/* Theme-aware colors for divider */
.divider {
  /* Light mode: primary color with opacity */
  color: var(--palette-primary-main);
  opacity: 0.7;
}

/* Dark mode: white with opacity */
@media (prefers-color-scheme: dark) {
  .divider {
    color: var(--palette-common-white);
    opacity: 0.7;
  }
}

/* For CSS variables theme switching */
[data-color-scheme="light"] .divider {
  color: var(--palette-primary-main);
  opacity: 0.7;
}

[data-color-scheme="dark"] .divider {
  color: var(--palette-common-white);
  opacity: 0.7;
} 
