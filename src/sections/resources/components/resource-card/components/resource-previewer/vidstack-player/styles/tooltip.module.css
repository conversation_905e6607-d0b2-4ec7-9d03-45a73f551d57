.tooltip {
  --enter-transform: translateY(0px) scale(1);
  --exit-transform: translateY(12px) scale(0.8);

  display: inline-block;
  color: var(--palette-text-primary);
  background-color: var(--palette-background-paper);
  border: 1px solid var(--palette-divider);
  font-size: 13px;
  font-weight: 500;
  opacity: 0;
  pointer-events: none;
  white-space: nowrap;
  z-index: 10;
  border-radius: 4px;
  padding: 4px 8px;
  will-change: transform, opacity;
  backdrop-filter: blur(8px);
  box-shadow: 0 2px 8px var(--palette-action-selected);
}

.tooltip[data-visible] {
  animation: media-tooltip-enter 0.2s ease-in;
  animation-fill-mode: forwards;
}

.tooltip:not([data-visible]) {
  animation: media-tooltip-exit 0.2s ease-out;
}

.tooltip[data-placement~='bottom'] {
  --exit-transform: translateY(-12px) scale(0.8);
}

@keyframes media-tooltip-enter {
  from {
    opacity: 0;
    transform: var(--exit-transform);
  }
  to {
    opacity: 1;
    transform: var(--enter-transform);
  }
}

@keyframes media-tooltip-exit {
  from {
    opacity: 1;
    transform: var(--enter-transform);
  }
  to {
    opacity: 0;
    transform: var(--exit-transform);
  }
}

:global(.menu[data-open]) .tooltip {
  display: none !important;
} 
