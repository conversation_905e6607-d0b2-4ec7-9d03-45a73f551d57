import styles from './styles/player.module.css';

import '@vidstack/react/player/styles/base.css';

import type { ResourceItem } from 'src/sections/resources/components/resources-list';

import { toast } from 'sonner';
import { memo, useRef, useState, useEffect, useCallback } from 'react';
import {
  Track,
  Poster,
  MediaPlayer,
  isHLSProvider,
  MediaProvider,
  type MediaCanPlayEvent,
  type MediaCanPlayDetail,
  type MediaPlayerInstance,
  type MediaProviderAdapter,
  type MediaProviderChangeEvent,
} from '@vidstack/react';

import { Tooltip } from '@mui/material';
import LoadingButton from '@mui/lab/LoadingButton';

import { useUploadResourceThumbnailMutation } from 'src/store/api/resources';

import { Iconify } from 'src/components/iconify';

import { useFilePreviewer } from 'src/sections/resources/components/resource-card/components/file-previewer-modal/file-previewer-provider';

import { VideoLayout } from './components/video-layout';

import type { VideoQualityOption } from './components/settings-menu';

interface VidstackPlayerProps {
  data: ResourceItem;
  seekTime?: number;
  shouldAutoPlay?: boolean;
}

const VidstackPlayer = ({ data, seekTime, shouldAutoPlay }: VidstackPlayerProps) => {
  const playerRef = useRef<MediaPlayerInstance>(null);
  const [uploadResourceThumbnail, { isLoading: isUploading }] = useUploadResourceThumbnailMutation();
  const [currentVideoUrl, setCurrentVideoUrl] = useState(data.url);
  const [preservedTime, setPreservedTime] = useState<number>(0);
  const [shouldResumePlayback, setShouldResumePlayback] = useState<boolean>(false);

  const { openPreview } = useFilePreviewer();

  useEffect(() => {
    if (!playerRef.current) return undefined;

    // Subscribe to state updates
    return playerRef.current.subscribe(({ paused, viewType }) => {
      // Can listen to player state changes here
    });
  }, []);

  // Reusable function to handle seeking and auto-play
  const handleSeekAndPlay = useCallback((time: number, shouldPlay?: boolean, delay: number = 100) => {
    setTimeout(() => {
      if (playerRef.current) {
        playerRef.current.currentTime = time;
        
        if (shouldPlay) {
          playerRef.current.play().catch(() => {
            // If autoplay fails, that's okay - user can manually play
          });
        }
      }
    }, delay);
  }, []);

  // Handle seek time updates when player is already loaded
  useEffect(() => {
    if (seekTime !== undefined && playerRef.current && !preservedTime) {
      handleSeekAndPlay(seekTime, shouldAutoPlay);
    }
  }, [seekTime, shouldAutoPlay, preservedTime, handleSeekAndPlay]);

  const onProviderChange = useCallback((
    provider: MediaProviderAdapter | null,
    nativeEvent: MediaProviderChangeEvent,
  ) => {
    // Configure provider if needed
    if (isHLSProvider(provider)) {
      provider.config = {};
    }
  }, []);

  const onCanPlay = useCallback((detail: MediaCanPlayDetail, nativeEvent: MediaCanPlayEvent) => {
    // Player is ready - restore preserved time if switching quality
    if (preservedTime > 0 && playerRef.current) {
      // Small delay to ensure video is fully loaded before seeking
      setTimeout(() => {
        if (playerRef.current) {
          playerRef.current.currentTime = preservedTime;
          setPreservedTime(0);
          
          // Resume playback if needed
          if (shouldResumePlayback) {
            playerRef.current.play().catch(() => {
              // If autoplay fails, that's okay - user can manually play
            });
            setShouldResumePlayback(false);
          }
        }
      }, 100);
    }
    
    // Handle external seek time and auto play
    if (seekTime !== undefined && playerRef.current && !preservedTime) {
      handleSeekAndPlay(seekTime, shouldAutoPlay, 200);
    }
  }, [preservedTime, shouldResumePlayback, seekTime, shouldAutoPlay, handleSeekAndPlay]);

  const handleQualityChange = useCallback((option: VideoQualityOption) => {
    if (!playerRef.current || option.url === currentVideoUrl) return;

    // Preserve current playback time and pause state
    const currentTime = playerRef.current.currentTime;
    const wasPaused = playerRef.current.paused;
    
    setPreservedTime(currentTime);
    setShouldResumePlayback(!wasPaused); // Will resume if video was playing
    
    // Pause video during switch to prevent issues
    if (!wasPaused) {
      playerRef.current.pause();
    }
    
    // Change video URL
    setCurrentVideoUrl(option.url);
    
    toast.success(`Switched to ${option.label}`, {
      description: 'Video quality changed successfully',
    });
  }, [currentVideoUrl]);

  const generateThumbnail = useCallback(() => {
    if (!playerRef.current) return null;

    const video = playerRef.current.el?.querySelector('video');
    if (!video) return null;

    // Pause video for consistent frame capture
    playerRef.current.pause();

    const canvas = document.createElement('canvas');
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    const ctx = canvas.getContext('2d');
    if (!ctx) return null;

    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    const imageUrl = canvas.toDataURL('image/png');

    return imageUrl;
  }, []);

  const handleCaptureFrame = useCallback(async () => {
    const imageUrl = generateThumbnail();
    if (!imageUrl) return;

    try {
      await uploadResourceThumbnail({ id: data.id, payload: { imageUrl } }).unwrap();
      toast.success('Thumbnail uploaded successfully');
    } catch {
      toast.error('Failed to upload thumbnail');
    }
  }, [data.id, generateThumbnail, uploadResourceThumbnail]);

  const handleFullscreenClick = useCallback(() => {
    if (playerRef.current) {
      playerRef.current.pause();
    }
    openPreview(data);
  }, [data, openPreview]);

  return (
    <>
      <MediaPlayer
        className={`${styles.player} player`}
        title={data.name}
        src={currentVideoUrl}
        crossOrigin
        playsInline
        onProviderChange={onProviderChange}
        onCanPlay={onCanPlay}
        ref={playerRef}
      >
        <MediaProvider>
          <Poster
            className={styles.poster}
            src={data.thumbnailUrl}
            alt={`${data.name} thumbnail`}
          />
          {data.transcriptionSrc && (
            <Track
              kind="captions"
              src={data.transcriptionSrc}
              language="en"
              label="English"
              default
            />
          )}
        </MediaProvider>

        <VideoLayout
          resource={data}
          onQualityChange={handleQualityChange}
        />
      </MediaPlayer>

      {/* Frame capture button overlay */}
      <Tooltip arrow title="Capture current frame as thumbnail">
        <LoadingButton
          loading={isUploading}
          variant="contained"
          size="small"
          color="primary"
          className="thumbnail-capture-button"
          sx={{
            position: 'absolute',
            top: 8,
            left: 8,
            zIndex: 20,
            minWidth: 'auto',
            padding: 1,
            transition: (theme) =>
              theme.transitions.create('opacity', {
                duration: theme.transitions.duration.short,
              }),
          }}
          onClick={handleCaptureFrame}
        >
          <Iconify
            icon="material-symbols:photo-camera-outline"
            sx={{
              fontSize: 24,
              color: 'inherit',
            }}
          />
        </LoadingButton>
      </Tooltip>

      {/* External fullscreen button overlay */}
      <Tooltip arrow title="Open in fullscreen modal">
        <LoadingButton
          variant="contained"
          size="small"
          color="secondary"
          sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            zIndex: 20,
            minWidth: 'auto',
            padding: 1,
            transition: (theme) =>
              theme.transitions.create('opacity', {
                duration: theme.transitions.duration.short,
              }),
          }}
          onClick={handleFullscreenClick}
        >
          <Iconify
            icon="material-symbols:open-in-new"
            sx={{
              fontSize: 24,
              color: 'inherit',
            }}
          />
        </LoadingButton>
      </Tooltip>
    </>
  );
};

export default memo(VidstackPlayer); 
