import { Box, Typography } from '@mui/material';

import { Image } from 'src/components/image';
import { fileThumb, fileFormat } from 'src/components/file-thumbnail';

import { THUMBNAIL_HEIGHT } from '.';
import { useFilePreviewer } from '../file-previewer-modal/file-previewer-provider';

import type { ResourceItem } from '../../../resources-list';

const ResourceDocumentThumbnail: React.FC<{ data: ResourceItem }> = ({ data }) => {
  const format = fileFormat(data.fileName || data.name || '');
  const iconSrc = fileThumb(format);
  const fileName = data.fileName || data.name || '';
  
  const { openPreview } = useFilePreviewer();

  // Get file extension for display
  const fileExtension = fileName.split('.').pop()?.toUpperCase() || format.toUpperCase();

  const handleClick = () => {
    openPreview(data);
  };

  return (
    <Box
      onClick={handleClick}
      sx={{
        width: '100%',
        height: THUMBNAIL_HEIGHT,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        bgcolor: 'background.paper',
        border: '1px solid',
        borderColor: 'divider',
        borderRadius: 1,
        p: 2,
        cursor: 'pointer',
        transition: (theme) => theme.transitions.create(['box-shadow', 'transform'], {
          duration: theme.transitions.duration.short,
        }),
        '&:hover': {
          boxShadow: (theme) => theme.shadows[4],
          transform: 'translateY(-1px)',
          borderColor: 'primary.main',
        },
        '&:active': {
          transform: 'translateY(0)',
        },
      }}
    >
      <Image
        visibleByDefault
        src={iconSrc}
        alt={`${format} file`}
        sx={{
          width: 80,
          height: 80,
          mb: 1,
          '& .minimal__image__img': { objectFit: 'contain' },
        }}
      />
      <Typography
        variant="caption"
        color="text.secondary"
        sx={{
          fontWeight: 'bold',
          textAlign: 'center',
          backgroundColor: format === 'word' ? '#2B579A' : '#D24726',
          color: 'white',
          px: 1,
          py: 0.5,
          borderRadius: 0.5,
          fontSize: '0.75rem',
        }}
      >
        {fileExtension}
      </Typography>
    </Box>
  );
};

export default ResourceDocumentThumbnail;
