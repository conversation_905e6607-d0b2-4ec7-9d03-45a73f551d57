import { useState } from 'react';

import { Box, Fade, Typography } from '@mui/material';

import { LoadingScreen } from 'src/components/loading-screen';

import { THUMBNAIL_HEIGHT } from '.';
import { useFilePreviewer } from '../file-previewer-modal/file-previewer-provider';

import type { ResourceItem } from '../../../resources-list';

const ResourceImageThumbnail: React.FC<{ data: ResourceItem }> = ({ data }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  
  const { openPreview } = useFilePreviewer();

  const handleImageLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  const handleImageError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  const handleClick = () => {
    // Only open preview if image has loaded successfully and has a URL
    if (!isLoading && !hasError && data.url) {
      openPreview(data);
    }
  };

  // If no URL or image failed to load, show fallback
  if (!data.url || hasError) {
    return (
      <Box
        sx={{
          width: '100%',
          height: THUMBNAIL_HEIGHT,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: 'background.paper',
          border: '1px dashed',
          borderColor: hasError ? 'error.main' : 'divider',
          borderRadius: 1,
        }}
      >
        <Typography
          variant="body2"
          color={hasError ? 'error' : 'text.secondary'}
          textAlign="center"
        >
          {!data.url ? 'Image preview not available yet' : 'Image preview failed to load'}
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      onClick={handleClick}
      sx={{
        width: '100%',
        height: THUMBNAIL_HEIGHT,
        position: 'relative',
        borderRadius: 1,
        overflow: 'hidden',
        bgcolor: 'background.paper',
        cursor: !isLoading && !hasError ? 'pointer' : 'default',
        transition: (theme) => theme.transitions.create(['transform', 'box-shadow'], {
          duration: theme.transitions.duration.short,
        }),
        '&:hover': {
          boxShadow: !isLoading && !hasError ? (theme) => theme.shadows[4] : 'none',
        },
      }}
    >
      {isLoading && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'background.paper',
            zIndex: 1,
          }}
        >
          <LoadingScreen />
        </Box>
      )}

      <Fade in={!isLoading} timeout={300}>
        <Box
          sx={{
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <img
            src={data.url}
            alt={data.name || 'Image thumbnail'}
            onLoad={handleImageLoad}
            onError={handleImageError}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              display: 'block',
            }}
          />
        </Box>
      </Fade>
    </Box>
  );
};

export default ResourceImageThumbnail;
