import type { BoxProps } from '@mui/material';

import { memo, lazy, useRef, useMemo, Suspense, useState, useEffect } from 'react';

import { Box, Skeleton } from '@mui/material';

import { Image } from 'src/components/image';
import { EmptyContent } from 'src/components/empty-content';
import { fileThumb, fileFormat } from 'src/components/file-thumbnail';

import type { ResourceItem } from '../../../resources-list';

// Lazy load heavy components
const ResourcePdfThumbnail = lazy(() => import('./pdf'));
const ResourceVideoThumbnail = lazy(() => import('./video'));
const ResourceImageThumbnail = lazy(() => import('./image'));
const ResourceDocumentThumbnail = lazy(() => import('./document'));
const ResourceTextThumbnail = lazy(() => import('./text'));

const ThumbnailComponentsMap: Record<
  string,
  React.LazyExoticComponent<React.FC<{ data: ResourceItem }>>
> = {
  video: ResourceVideoThumbnail,
  pdf: ResourcePdfThumbnail,
  image: ResourceImageThumbnail,
  word: ResourceDocumentThumbnail,
  powerpoint: ResourceDocumentThumbnail,
  txt: ResourceTextThumbnail,
  markdown: ResourceTextThumbnail,
};

export const THUMBNAIL_HEIGHT = 230;

// Loading skeleton for thumbnail
const ThumbnailSkeleton = memo(() => (
  <Skeleton variant="rectangular" width="100%" height={THUMBNAIL_HEIGHT} sx={{ borderRadius: 1 }} />
));

ThumbnailSkeleton.displayName = 'ThumbnailSkeleton';

interface ResourceThumbnailProps extends BoxProps {
  data: ResourceItem;
  enableLazyLoading?: boolean;
}

const ResourceThumbnail = memo<ResourceThumbnailProps>(
  ({ data, enableLazyLoading = true, ...props }) => {
    const [isVisible, setIsVisible] = useState(!enableLazyLoading);
    const [hasIntersected, setHasIntersected] = useState(!enableLazyLoading);
    const observerRef = useRef<HTMLDivElement>(null);

    // Memoize file format calculation
    const format = useMemo(
      () => fileFormat(data.fileName || data.name || ''),
      [data.fileName, data.name]
    );

    // Intersection observer for lazy loading
    useEffect(() => {
      if (!enableLazyLoading || hasIntersected) return undefined;

      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            setIsVisible(true);
            setHasIntersected(true);
            observer.disconnect();
          }
        },
        {
          rootMargin: '100px', // Load 100px before coming into view
          threshold: 0.1,
        }
      );

      if (observerRef.current) {
        observer.observe(observerRef.current);
      }

      return () => {
        observer.disconnect();
      };
    }, [enableLazyLoading, hasIntersected]);

    console.log('ResourceThumbnail data', data, format);

    const ThumbnailComponent = ThumbnailComponentsMap[format];

    // For ongoing session; showing placeholder
    if (data.cardType === 'session') {
      return (
        <Box ref={observerRef} sx={{ height: THUMBNAIL_HEIGHT - 5 }} {...props}>
          <EmptyContent
            title="Recording session..."
            description={data.url}
            sx={{ p: 0 }}
            slotProps={{
              img: { sx: { maxWidth: 120 } },
              description: { sx: { whiteSpace: 'nowrap' } },
            }}
          />
        </Box>
      );
    }

    // Show placeholder if not visible yet
    if (enableLazyLoading && !isVisible) {
      return (
        <Box
          ref={observerRef}
          sx={{
            height: THUMBNAIL_HEIGHT,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            ...props.sx,
          }}
          {...props}
        >
          <ThumbnailSkeleton />
        </Box>
      );
    }

    // Default file icon thumbnail
    if (!ThumbnailComponent) {
      return (
        <Box
          ref={observerRef}
          sx={{
            height: THUMBNAIL_HEIGHT,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            ...props.sx,
          }}
          {...props}
        >
          <Image
            visibleByDefault
            src={fileThumb(format)}
            alt={data.name}
            sx={{
              height: '50%',
              borderRadius: 1,
              '& .minimal__image__img': { objectFit: 'contain' },
            }}
          />
        </Box>
      );
    }

    // Render with Suspense for lazy-loaded components
    return (
      <Box ref={observerRef} {...props}>
        <Suspense fallback={<ThumbnailSkeleton />}>
          <ThumbnailComponent data={data} />
        </Suspense>
      </Box>
    );
  }
);

ResourceThumbnail.displayName = 'ResourceThumbnail';

export default ResourceThumbnail;
