import { useResizeObserver } from 'usehooks-ts';
import { Page, pdfjs, Document } from 'react-pdf';
import { memo, useRef, useMemo, useState, useCallback } from 'react';

import { Box, Fade, Typography } from '@mui/material';

import { LoadingScreen } from 'src/components/loading-screen';

import { THUMBNAIL_HEIGHT } from '.';
import { useFilePreviewer } from '../file-previewer-modal/file-previewer-provider';

import type { ResourceItem } from '../../../resources-list';

pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  'pdfjs-dist/build/pdf.worker.min.mjs',
  import.meta.url
).toString();

const options = {
  cMapUrl: '/cmaps/',
  standardFontDataUrl: '/standard_fonts/',
};

// Error boundary fallback component
const PdfErrorFallback = memo<{ data: ResourceItem; error?: string }>(({ data, error }) => (
  <Box
    sx={{
      width: '100%',
      height: THUMBNAIL_HEIGHT,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      bgcolor: 'background.paper',
      border: '1px dashed',
      borderColor: 'divider',
      borderRadius: 1,
    }}
  >
    <Typography variant="body2" color="text.secondary" textAlign="center">
      {!data.url ? 'PDF preview not available yet' : error || 'PDF preview failed to load'}
    </Typography>
  </Box>
));

PdfErrorFallback.displayName = 'PdfErrorFallback';

const ResourcePdfThumbnail = memo<{ data: ResourceItem }>(({ data }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = useState<number>();
  const [isLoading, setIsLoading] = useState(true);
  const [pdfError, setPdfError] = useState<string | null>(null);
  
  const { openPreview } = useFilePreviewer();

  useResizeObserver({
    ref: containerRef,
    onResize: (size) => setContainerWidth(size.width),
  });

  // Memoize event handlers
  const handleLoadSuccess = useCallback(() => {
    setIsLoading(false);
    setPdfError(null);
  }, []);

  const handleLoadError = useCallback(
    (error: any) => {
      console.error('PDF thumbnail failed to load:', data.url, error);
      setIsLoading(false);
      setPdfError('PDF preview failed to load');
    },
    [data.url]
  );

  // Handle click to open preview
  const handleClick = useCallback(() => {
    if (data.url && !pdfError) {
      // Convert ResourceItem to Resource format expected by the previewer
      const resource = {
        ...data,
        fileName: data.fileName || data.name || '',
      };
      openPreview(resource);
    }
  }, [data, pdfError, openPreview]);

  // Memoize document options
  const documentProps = useMemo(
    () => ({
      loading: null,
      file: data.url,
      onLoadSuccess: handleLoadSuccess,
      onLoadError: handleLoadError,
      options,
    }),
    [data.url, handleLoadSuccess, handleLoadError]
  );

  // Memoize page props
  const pageProps = useMemo(
    () => ({
      pageNumber: 1,
      width: containerWidth,
      renderTextLayer: false,
      renderAnnotationLayer: false,
      loading: null,
    }),
    [containerWidth]
  );

  // Early return for no URL or error
  if (!data.url || pdfError) {
    return <PdfErrorFallback data={data} error={pdfError || undefined} />;
  }

  return (
    <Box
      ref={containerRef}
      onClick={handleClick}
      sx={{
        width: '100%',
        height: THUMBNAIL_HEIGHT,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative',
        cursor: data.url && !pdfError ? 'pointer' : 'default',
        transition: (theme) => theme.transitions.create(['transform', 'box-shadow'], {
          duration: theme.transitions.duration.short,
        }),
        '&:hover': data.url && !pdfError ? {
          transform: 'scale(1.02)',
          boxShadow: (theme) => theme.shadows[4],
        } : {},
        '& .react-pdf__Page': {
          display: 'flex',
          justifyContent: 'center',
          '& canvas': {
            maxWidth: '100%',
            maxHeight: THUMBNAIL_HEIGHT,
            height: 'auto !important',
            width: 'auto !important',
          },
        },
      }}
    >
      {isLoading && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'background.paper',
            zIndex: 1,
          }}
        >
          <LoadingScreen />
        </Box>
      )}
      <Fade in={!isLoading} timeout={300}>
        <Box>
          <Document {...documentProps}>
            <Page {...pageProps} />
          </Document>
        </Box>
      </Fade>
    </Box>
  );
});

ResourcePdfThumbnail.displayName = 'ResourcePdfThumbnail';

export default ResourcePdfThumbnail;
