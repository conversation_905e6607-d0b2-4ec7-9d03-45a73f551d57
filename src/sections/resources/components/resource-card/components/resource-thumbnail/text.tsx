import { Box } from '@mui/material';

import { Image } from 'src/components/image';
import { fileThumb, fileFormat } from 'src/components/file-thumbnail';

import { THUMBNAIL_HEIGHT } from '.';
import { useFilePreviewer } from '../file-previewer-modal/file-previewer-provider';

import type { ResourceItem } from '../../../resources-list';

const ResourceTextThumbnail: React.FC<{ data: ResourceItem }> = ({ data }) => {
  const format = fileFormat(data.fileName || data.name || '');
  
  const { openPreview } = useFilePreviewer();
  const handleClick = () => {
    openPreview(data);
  };

  return (
    <Box
      onClick={handleClick}
      sx={{
        width: '100%',
        height: THUMBNAIL_HEIGHT,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        bgcolor: 'background.paper',
        border: '1px solid',
        borderColor: 'divider',
        borderRadius: 1,
        p: 2,
        cursor: 'pointer',
        transition: (theme) => theme.transitions.create(['box-shadow', 'transform'], {
          duration: theme.transitions.duration.short,
        }),
        '&:hover': {
          boxShadow: (theme) => theme.shadows[4],
          transform: 'translateY(-1px)',
          borderColor: 'primary.main',
        },
        '&:active': {
          transform: 'translateY(0)',
        },
      }}
    >
      <Image
            visibleByDefault
            src={fileThumb(format)}
            alt={data.name}
            sx={{
              height: '50%',
              borderRadius: 1,
              '& .minimal__image__img': { objectFit: 'contain' },
            }}
          />
    </Box>
  );
};

export default ResourceTextThumbnail;
