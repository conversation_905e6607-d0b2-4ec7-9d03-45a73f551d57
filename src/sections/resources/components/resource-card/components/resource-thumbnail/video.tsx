import { Box } from '@mui/material';

import { VideoPlayer } from 'src/components/video-player';

import { THUMBNAIL_HEIGHT } from '.';

import type { ResourceItem } from '../../../resources-list';

const ResourceVideoThumbnail: React.FC<{ data: ResourceItem }> = ({ data }) => (
    <Box
        sx={{
        width: '100%',
        position: 'relative',
        borderRadius: 1,
        overflow: 'hidden',
        cursor: 'pointer',
        transition: (theme) => theme.transitions.create(['box-shadow'], {
          duration: theme.transitions.duration.short,
        }),
        '&:hover': {
          boxShadow: (theme) => theme.shadows[4],
        },
      }}
    >
      <VideoPlayer
        src={data.url}
        captionVtts={data.transcriptionSrc ? [data.transcriptionSrc] : []}
        poster={data.thumbnailUrl}
        mode="thumbnail"
        title={data.name}
        height={THUMBNAIL_HEIGHT}
        muted
        loop
      />
    </Box>
  );

export default ResourceVideoThumbnail;
