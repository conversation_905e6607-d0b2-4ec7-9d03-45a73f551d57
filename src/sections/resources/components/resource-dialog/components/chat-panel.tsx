import type { Resource } from 'src/types';
import type { SxProps } from '@mui/material';

import { useRef, useEffect } from 'react';

import { Stack, Typography } from '@mui/material';

import { ChatBox } from 'src/sections/chat';

import useChat from '../../../../chat/hooks/use-chat';

interface ChatPanelProps {
  resource: Resource;
  sx?: SxProps;
}

const ChatPanel: React.FC<ChatPanelProps> = ({ resource, sx }) => {
  // Use the new chat hook with single resource
  const {
    messages,
    isLoading,
    isStreaming,
    sendMessage,
    stopStreaming,
    resetChat,
    allowSendMessage,
    conversationId,
  } = useChat({
    resources: resource.id ? [resource.id] : [],
    projectId: resource.projectId,
  });

  const prevResourceIdRef = useRef<string>(resource.id);

  // Reset conversation when resource changes
  useEffect(() => {
    if (prevResourceIdRef.current !== resource.id) {
      // Reset chat when switching resources
      resetChat();
      prevResourceIdRef.current = resource.id;
    }
  }, [resource.id, resetChat]);

  return (
    <Stack direction="column" sx={{ gap: 2, height: '100%', ...sx }}>
      <Typography variant="subtitle1" sx={{ p: 1, pt: 1.5 }}>
        Aida Chat
      </Typography>
      <ChatBox
        containerSx={{
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
        }}
        messageListSx={{
          height: '65vh',
          pt: 2,
          px: 0,
          pr: 1,
        }}
        messages={messages}
        loading={isLoading}
        isReplying={isStreaming}
        disabled={!allowSendMessage}
        sendMessage={sendMessage}
        stopConversation={stopStreaming}
        conversationId={conversationId}
        onResetChat={resetChat}
      />
    </Stack>
  );
};

export default ChatPanel;
