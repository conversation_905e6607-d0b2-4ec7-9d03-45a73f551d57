import type { Note } from 'src/store/api/notes/types';
import type { NoteCreatedEvent, NoteDeletedEvent, NoteUpdatedEvent} from 'src/types';

import { toast } from 'sonner';
import { useDispatch } from 'react-redux';
import { useEffect, useCallback } from 'react';

import AddRoundedIcon from '@mui/icons-material/AddRounded';
import { Stack, Divider, IconButton, Typography, CircularProgress } from '@mui/material';

import { useEventListener } from 'src/hooks/use-event-listener';
import useResourcePermissions from 'src/hooks/use-resource-permissions';

import { AUTH } from 'src/lib/firebase';
import { SSEEventType } from 'src/types';
import { useAppSelector } from 'src/store';
import { selectSelectedNote } from 'src/store/slices/notes/selectors';
import { useGetProjectMembershipQuery } from 'src/store/api/projects';
import { addNote, deleteNote, selectNote, storeNotes } from 'src/store/slices/notes/slice';
import {
  useCreateNoteMutation,
  useDeleteNoteMutation,
  useGetNotesByResourceQuery,
} from 'src/store/api/notes';

import { NoteDialog } from 'src/components/notes';
import { Scrollbar } from 'src/components/scrollbar';
import { NoteList } from 'src/components/notes/note-list';
import { LoadingScreen } from 'src/components/loading-screen';

interface NotesSectionProps {
  resourceId: string;
  resource?: any;
}

const NotesPanel: React.FC<NotesSectionProps> = ({ resourceId, resource }) => {
  const dispatch = useDispatch();
  const selectedNote = useAppSelector(selectSelectedNote);
  const { canComment } = useResourcePermissions({ resource });
  const currentUserId = AUTH.currentUser?.uid ?? '';

  const { data: projectMembership } = useGetProjectMembershipQuery(
    { id: resource?.projectId },
    {
      skip: !resource?.projectId,
    }
  );

  const isOwner = resource?.createdById === currentUserId;

  const [createNote, { isLoading: isCreating }] = useCreateNoteMutation();
  const [deleteNoteMutation] = useDeleteNoteMutation();
  const {
    data: apiNotes = [],
    isLoading: isApiLoading,
    refetch,
  } = useGetNotesByResourceQuery(
    { resourceId },
    {
      refetchOnMountOrArgChange: true,
    }
  );

  useEffect(() => {
    if (!isApiLoading && apiNotes) {
      dispatch(storeNotes(apiNotes));
    }
  }, [dispatch, isApiLoading, apiNotes]);

  const handleSelectNote = (note: Note) => {
    dispatch(selectNote(note));
  };

  const handleCloseDialog = () => {
    refetch();
    dispatch(selectNote(null));
  };

  const handleAddNewNote = useCallback(() => {
    createNote({
      payload: {
        content: '',
        title: 'New Note222',
        resourceId,
      },
    })
      .then((res) => {
        if (res.data) {
          handleSelectNote(res.data);
          dispatch(addNote(res.data));
        }
      })
      .catch((err) => {
        toast.error('Failed to add new note');
      });
  }, [createNote, resourceId, handleSelectNote]);

  const handleDelete = async (noteId: string) => {
    try {
      await deleteNoteMutation({ id: noteId });
      dispatch(deleteNote(noteId));
      await refetch();
      toast.success('Note deleted successfully');
    } catch (error) {
      toast.error('Failed to delete note');
    }
  };

  useEventListener(SSEEventType.NOTE_CREATED,  async (event: NoteCreatedEvent) => {
    // check if the note is not created by the current user
    if (event.data.createdById !== currentUserId) {
      await refetch();
      toast.success('New note created', {
        description: `New note created by ${event.data.createdBy}`,
      });
    }
  });

  useEventListener(SSEEventType.NOTE_UPDATED,  async (event: NoteUpdatedEvent) => {
    // check if the note is not updated by the current user
    if (event.data.updatedById !== currentUserId) {
      await refetch();
      toast.success('Note updated', {
        description: `Note updated by ${event.data.updatedBy}`,
      });
    }
  });

  useEventListener(SSEEventType.NOTE_DELETED, async (event: NoteDeletedEvent) => {
    // check if the note is not deleted by the current user
    if (event.data.deletedById !== currentUserId) {
      await refetch();
      toast.success('Note deleted', {
        description: `Note deleted by ${event.data.deletedBy}`,
      });
    }
  });

  if (isApiLoading) {
    return <LoadingScreen />;
  }

  return (
    <>
      <Stack direction="row" justifyContent="space-between" alignItems="center">
        <Typography variant="subtitle1" color="textPrimary" sx={{ fontSize: 16 }}>
          Notes
        </Typography>
        {canComment && (
          <IconButton disabled={isCreating} onClick={handleAddNewNote} sx={{ p: 0 }}>
            {isCreating ? <CircularProgress size={20} /> : <AddRoundedIcon />}
          </IconButton>
        )}
      </Stack>
      <Divider sx={{ my: 2 }} />
      <Scrollbar sx={{ height: '100%', mt: 1 }}>
        <NoteList
          notes={apiNotes}
          onSelectNote={handleSelectNote}
          onDelete={handleDelete}
          onAddNote={canComment ? handleAddNewNote : undefined}
          isCanDeleteResource={canComment}
          role={projectMembership?.role}
          isOwner={isOwner}
          currentUserId={currentUserId}
        />
      </Scrollbar>

      {/* Note Dialog */}
      <NoteDialog
        open={Boolean(selectedNote)}
        onClose={handleCloseDialog}
        isCanEdit={canComment}
        role={projectMembership?.role}
        isOwner={isOwner}
        currentUserId={currentUserId}
      />
    </>
  );
};

export default NotesPanel;
