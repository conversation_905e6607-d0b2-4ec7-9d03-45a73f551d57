import type { Resource } from 'src/types';
import type { TransitionProps } from '@mui/material/transitions';

import React, { useState, forwardRef } from 'react';

import Dialog from '@mui/material/Dialog';
import CloseIcon from '@mui/icons-material/Close';
import {
  Box,
  Slide,
  Stack,
  AppBar,
  Toolbar,
  IconButton,
  BottomNavigation,
  BottomNavigationAction,
} from '@mui/material';

import { getDisplayFileName } from 'src/utils/file-utils';

import { Iconify } from 'src/components/iconify';
import { fileFormat } from 'src/components/file-thumbnail';
import { LoadingScreen } from 'src/components/loading-screen';
import TruncateTypography from 'src/components/truncate-typography';

import { MainPanel } from '.';
import ChatPanel from './components/chat-panel';
import NotesPanel from './components/notes-panel';
import ResourceActions from '../resource-actions';
import PreviewPanel from './components/preview-panel';
import SummaryPanel from './components/summary-panel';
import TranscriptPanel from './components/transcript-panel';
import ResourceLocation from '../resource-card/components/resource-location';
import { FilePreviewerProvider } from '../resource-card/components/file-previewer-modal/file-previewer-provider';

const Transition = forwardRef(
  (props: TransitionProps & { children: React.ReactElement }, ref: React.Ref<unknown>) => (
    <Slide direction="up" ref={ref} {...props} />
  )
);

const ResourceDialogMobile: React.FC<{
  loading: boolean;
  resource?: Resource;
  onClose: () => void;
}> = ({ loading, resource, onClose }) => {
  const [activePanel, setActivePanel] = useState<string>('summary');

  const fFormat = resource ? fileFormat(resource.fileName) : '';
  const isMediaFile = fFormat === 'audio' || fFormat === 'video';

  const navigationItems = [
    { label: 'Summary', value: 'summary', icon: <Iconify icon="material-symbols:description" /> },
    ...(isMediaFile
      ? [
          {
            label: 'Transcript',
            value: MainPanel.Transcript,
            icon: <Iconify icon="material-symbols:speech-to-text-rounded" />,
          },
        ]
      : [
          {
            label: 'Preview',
            value: MainPanel.Preview,
            icon: <Iconify icon="material-symbols:preview" />,
          },
        ]),
    {
      label: 'Notes',
      value: MainPanel.Notes,
      icon: <Iconify icon="material-symbols:notes-rounded" />,
    },
    { label: 'Chat', value: 'chat', icon: <Iconify icon="material-symbols:chat" /> },
  ];

  const renderPanel = () => {
    if (!resource) return null;

    switch (activePanel) {
      case 'summary':
        return <SummaryPanel resource={resource} />;
      case MainPanel.Notes:
        return <NotesPanel resourceId={resource.id} resource={resource} />;
      case MainPanel.Transcript:
        return (
          <TranscriptPanel
            status={resource.transcriptionJobStatus}
            transcriptions={resource.transcription}
          />
        );
      case MainPanel.Preview:
        return <PreviewPanel resource={resource} />;
      case 'chat':
        return <ChatPanel resource={resource} />;
      default:
        return null;
    }
  };

  return (
    <Dialog
      fullScreen
      open
      onClose={onClose}
      TransitionComponent={Transition}
      PaperProps={{
        sx: {
          height: '100%',
        },
      }}
    >
      {loading || !resource ? (
        <LoadingScreen />
      ) : (
        <FilePreviewerProvider resources={resource ? [resource] : []}>
          <>
            <AppBar position="relative" color="default">
              <Toolbar sx={{ justifyContent: 'space-between', alignItems: 'center' }}>
                <Stack direction="row" gap={2} alignItems="center">
                  <TruncateTypography
                    variant="subtitle1"
                    sx={{ fontSize: 16 }}
                    text={getDisplayFileName(resource.name)}
                    maxLength={15}
                  />
                  <ResourceLocation resource={resource} />
                </Stack>

                <Stack direction="row" gap={2} alignItems="center">
                  <ResourceActions
                    resource={resource}
                    sx={{ maxHeight: 20 }}
                    hiddenActions={['view', 'select']}
                  />
                  <IconButton color="default" edge="start" onClick={onClose} sx={{ p: 0 }}>
                    <CloseIcon />
                  </IconButton>
                </Stack>
              </Toolbar>
            </AppBar>

            <Box
              sx={{
                flex: 1,
                overflow: 'auto',
                px: 2,
                pb: (theme) => theme.spacing(12),
                mb: (theme) => theme.spacing(8),
                overflowY: 'hidden',
              }}
            >
              {renderPanel()}
            </Box>

            <BottomNavigation
              value={activePanel}
              onChange={(_, newValue) => setActivePanel(newValue)}
              showLabels
              sx={(theme) => ({
                position: 'fixed',
                bottom: theme.spacing(2),
                left: theme.spacing(2),
                right: theme.spacing(2),
                height: 56,
                borderRadius: 4,
                backgroundColor: theme.palette.background.neutral,
                zIndex: theme.zIndex.appBar,
                '& .MuiBottomNavigationAction-root': {
                  minWidth: 'auto',
                  padding: theme.spacing(0.75, 1.5),
                  color: theme.palette.text.secondary,
                  '&.Mui-selected': {
                    color: theme.palette.primary.lighter,
                    '& .MuiBottomNavigationAction-label': {
                      fontSize: '0.75rem',
                      fontWeight: 600,
                    },
                  },
                },
              })}
            >
              {navigationItems.map((item) => (
                <BottomNavigationAction
                  key={item.value}
                  label={item.label}
                  value={item.value}
                  icon={item.icon}
                />
              ))}
            </BottomNavigation>
          </>
        </FilePreviewerProvider>
      )}
    </Dialog>
  );
};

export default ResourceDialogMobile;
