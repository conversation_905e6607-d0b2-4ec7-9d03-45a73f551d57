import { useSelector } from 'react-redux';
import { varAlpha } from 'minimal-shared/utils';
import { useBoolean } from 'minimal-shared/hooks';

import { Box, Stack, Typography } from '@mui/material';

import { selectPendingResourceUploads } from 'src/store/slices/resources/selectors';

import { Iconify } from 'src/components/iconify';

import UploadResourceDialog from '../upload-resource-dialog';

interface ResourceListEmptyViewProps {
  showUploadButton?: boolean;
  uploadConfig?: {
    projectId: string;
    folderId?: string;
  };
}

const ResourceListEmptyView: React.FC<ResourceListEmptyViewProps> = ({
  showUploadButton = true,
  uploadConfig,
}) => {
  const uploadDialog = useBoolean();
  const pendingUploads = useSelector(selectPendingResourceUploads);
  const isUploading = pendingUploads.length > 0;

  return (
    <>
      <Box
        onClick={() => {
          if (showUploadButton) {
            uploadDialog.onTrue();
          }
        }}
        sx={[
          (theme) => ({
            width: 1,
            height: 320,
            borderRadius: 2,
            border: `dashed 1px ${theme.vars.palette.divider}`,
            bgcolor: varAlpha(theme.vars.palette.grey['500Channel'], 0.04),
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer',
            pointerEvents: isUploading ? 'none' : 'auto',
          }),
        ]}
      >
        {showUploadButton ? (
          <Stack spacing={0.5} sx={{ alignItems: 'center' }}>
            <Iconify
              icon="material-symbols:cloud-upload"
              width={40}
              sx={{ color: 'primary.light' }}
            />
            <Typography variant="h6">
              {isUploading ? 'Uploading resources...' : 'Click here to upload'}
            </Typography>
          </Stack>
        ) : (
          <Stack spacing={0.5} sx={{ alignItems: 'center' }}>
            <Typography variant="h6">No resources found</Typography>
          </Stack>
        )}
      </Box>
      {uploadDialog.value && (
        <UploadResourceDialog
          open={uploadDialog.value}
          onClose={uploadDialog.onFalse}
          projectId={uploadConfig?.projectId}
        />
      )}
    </>
  );
};

export default ResourceListEmptyView;
