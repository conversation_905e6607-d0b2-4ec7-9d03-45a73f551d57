import { useSelector } from 'react-redux';
import React, { memo, useMemo, useState, useEffect, useCallback } from 'react';

import { Stack, ToggleButton, ToggleButtonGroup } from '@mui/material';

import { type Resource } from 'src/types';
import { selectFocusedResource } from 'src/store/slices/resources/selectors';

import { Iconify } from 'src/components/iconify';
import { EmptyContent } from 'src/components/empty-content';

import { ResourceSort } from '../../types';
import ResourcesGridView from './grid-view';
import ResourcesSort from '../resources-sort';
import ResourcesTableView from './table-view';
import ResourceListEmptyView from './empty-view';
import ResourcesSearch from '../resources-search';

export interface ResourceItem extends Resource {
  cardType?: 'resource' | 'session';
  title?: string | React.ReactNode;
  /**
   * Applicable to session card only
   */
  meetingUrl?: string;
}

const ResourcesList: React.FC<{
  items: ResourceItem[];
  showUploadButton?: boolean;
  uploadConfig?: {
    projectId: string;
    folderId?: string;
  };
}> = ({ items, showUploadButton = true, uploadConfig }) => {
  const [sortBy, setSortBy] = useState(ResourceSort.Latest);
  const [searchQuery, setSearchQuery] = useState('');
  const [displayMode, setDisplayMode] = useState('list');

  const focusedResource = useSelector(selectFocusedResource);

  const resourceItems: ResourceItem[] = useMemo(() => {
    let output = [...items];

    // Search
    if (searchQuery) {
      output = output.filter((item) => item.name.toLowerCase().includes(searchQuery.toLowerCase()));
    }

    // Sort
    switch (sortBy) {
      case ResourceSort.Latest:
        return output.sort(
          (a, b) => new Date(b.fileLastModified).getTime() - new Date(a.fileLastModified).getTime()
        );
      case ResourceSort.Oldest:
        return output.sort(
          (a, b) => new Date(a.fileLastModified).getTime() - new Date(b.fileLastModified).getTime()
        );
      case ResourceSort.TitleAsc:
        return output.sort((a, b) => a.name.localeCompare(b.name));
      case ResourceSort.TitleDesc:
        return output.sort((a, b) => b.name.localeCompare(a.name));
      default:
        return output;
    }
  }, [items, sortBy, searchQuery]);

  const handleChangeView = useCallback(
    (event: React.MouseEvent<HTMLElement>, newView: string | null) => {
      if (newView !== null) {
        setDisplayMode(newView);
      }
    },
    []
  );

  // Scroll to the top when the focused resource changed
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, [focusedResource]);

  if (!items.length) {
    return (
      <ResourceListEmptyView showUploadButton={showUploadButton} uploadConfig={uploadConfig} />
    );
  }

  // List or Grid view
  return (
    <Stack direction="column" gap={3} sx={{ width: '100%' }}>
      <Stack
        direction="row"
        gap={1}
        alignItems="center"
        sx={{ width: '100%', justifyContent: 'space-between' }}
      >
        <ResourcesSearch searchQuery={searchQuery} onSearch={setSearchQuery} />
        <Stack direction="row" gap={1}>
          <ResourcesSort sort={sortBy} onSort={setSortBy} />
          <ToggleButtonGroup size="small" value={displayMode} exclusive onChange={handleChangeView}>
            <ToggleButton value="list">
              <Iconify icon="material-symbols:list" />
            </ToggleButton>

            <ToggleButton value="grid">
              <Iconify icon="mingcute:dot-grid-fill" />
            </ToggleButton>
          </ToggleButtonGroup>
        </Stack>
      </Stack>
      {resourceItems.length > 0 ? (
        displayMode === 'grid' ? (
          <ResourcesGridView items={resourceItems} />
        ) : (
          <ResourcesTableView items={resourceItems} />
        )
      ) : (
        <EmptyContent filled sx={{ py: 5 }} />
      )}
    </Stack>
  );
};

export default memo(ResourcesList);
