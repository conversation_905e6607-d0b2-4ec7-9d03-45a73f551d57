import type { Theme, SxProps } from '@mui/material/styles';

import { varAlpha } from 'minimal-shared/utils';
import { useBoolean, useDoubleClick } from 'minimal-shared/hooks';

import Box from '@mui/material/Box';
import Checkbox from '@mui/material/Checkbox';
import { useTheme } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import ListItemText from '@mui/material/ListItemText';
import { Link, Stack, Tooltip, IconButton } from '@mui/material';
import TableRow, { tableRowClasses } from '@mui/material/TableRow';
import TableCell, { tableCellClasses } from '@mui/material/TableCell';

import { fData } from 'src/utils/format-number';
import { fDate, fTime } from 'src/utils/format-time';
import { getDisplayFileName } from 'src/utils/file-utils';

import { TranscriptStatus } from 'src/types';

import { Iconify } from 'src/components/iconify';
import { fileFormat, FileThumbnail } from 'src/components/file-thumbnail';

import useCheckResourceStatus from 'src/sections/resources/hooks/check-resource-status';

import ResourceActions from '../../resource-actions';
import ResourceLocation from '../../resource-card/components/resource-location';

import type { ResourceItem } from '..';

// ----------------------------------------------------------------------

type Props = {
  row: ResourceItem;
  selected: boolean;
  onSelectRow: () => void;
  onView: () => void;
};

const FileTableRow = ({ row, selected, onSelectRow, onView }: Props) => {
  const theme = useTheme();
  const detailsDrawer = useBoolean();

  const isSessionRow = row.cardType === 'session';
  const skipChecking = isSessionRow || row.transcriptionJobStatus === TranscriptStatus.Completed;

  useCheckResourceStatus(skipChecking ? undefined : row.id);

  const handleClick = useDoubleClick({
    click: () => {
      onView();
    },
    doubleClick: () => {
      onSelectRow();
    },
  });

  const defaultStyles: SxProps<Theme> = {
    borderTop: `solid 1px ${varAlpha(theme.vars.palette.grey['500Channel'], 0.16)}`,
    borderBottom: `solid 1px ${varAlpha(theme.vars.palette.grey['500Channel'], 0.16)}`,
    '&:first-of-type': {
      borderTopLeftRadius: 16,
      borderBottomLeftRadius: 16,
      borderLeft: `solid 1px ${varAlpha(theme.vars.palette.grey['500Channel'], 0.16)}`,
    },
    '&:last-of-type': {
      borderTopRightRadius: 16,
      borderBottomRightRadius: 16,
      borderRight: `solid 1px ${varAlpha(theme.vars.palette.grey['500Channel'], 0.16)}`,
    },
  };

  return (
    <TableRow
      selected={selected}
      sx={{
        cursor: 'pointer',
        borderRadius: 2,
        [`&.${tableRowClasses.selected}, &:hover`]: {
          backgroundColor: 'background.paper',
          boxShadow: theme.vars.customShadows.z20,
          transition: theme.transitions.create(['background-color', 'box-shadow'], {
            duration: theme.transitions.duration.shortest,
          }),
          '&:hover': {
            backgroundColor: 'background.paper',
            boxShadow: theme.vars.customShadows.z20,
          },
        },
        [`& .${tableCellClasses.root}`]: {
          ...defaultStyles,
        },
        ...(detailsDrawer.value && {
          [`& .${tableCellClasses.root}`]: {
            ...defaultStyles,
          },
        }),
      }}
    >
      <TableCell padding="checkbox">
        <Checkbox
          checked={selected}
          onClick={onSelectRow}
          onDoubleClick={() => console.info('ON DOUBLE CLICK')}
          inputProps={{
            id: `${row.id}-checkbox`,
            'aria-label': `${row.id} checkbox`,
          }}
        />
      </TableCell>

      <TableCell onClick={handleClick}>
        <Box sx={{ gap: 2, display: 'flex', alignItems: 'center' }}>
          <FileThumbnail file={row.fileName} />

          <Stack direction="column" spacing={0.5}>
            <Typography
              noWrap
              variant="inherit"
              sx={{
                maxWidth: 360,
                cursor: 'pointer',
                ...(detailsDrawer.value && { fontWeight: 'fontWeightBold' }),
              }}
            >
              {getDisplayFileName(row.name)}
            </Typography>
            {row.title && (
              <Typography variant="caption" color="textSecondary">
                {row.title}
              </Typography>
            )}
          </Stack>
        </Box>
      </TableCell>

      <TableCell onClick={handleClick} sx={{ whiteSpace: 'nowrap' }}>
        <ResourceLocation resource={row} />
      </TableCell>
      <TableCell onClick={handleClick} sx={{ whiteSpace: 'nowrap' }}>
        {isSessionRow ? '-' : fData(row.fileSize)}
      </TableCell>

      <TableCell onClick={handleClick} sx={{ whiteSpace: 'nowrap' }}>
        {isSessionRow ? 'Recording' : fileFormat(row.fileName || row.name || '')}
      </TableCell>

      <TableCell onClick={handleClick} sx={{ whiteSpace: 'nowrap' }}>
        <ListItemText
          primary={fDate(row.fileLastModified)}
          secondary={fTime(row.fileLastModified)}
          primaryTypographyProps={{ typography: 'body2' }}
          secondaryTypographyProps={{ mt: 0.5, component: 'span', typography: 'caption' }}
        />
      </TableCell>

      <TableCell align="right" sx={{ px: 1, whiteSpace: 'nowrap' }}>
        <ResourceActions
          resource={row}
          hiddenActions={['select']}
          appendActions={
            isSessionRow && (
              <Tooltip title="Join meeting" placement="top" arrow>
                <Link href={row.meetingUrl} target="_blank" rel="noopener noreferrer">
                  <IconButton disableRipple sx={{ py: 0 }}>
                    <Iconify icon="material-symbols:open-in-new" />
                  </IconButton>
                </Link>
              </Tooltip>
            )
          }
        />
      </TableCell>
    </TableRow>
  );
};

export default FileTableRow;
