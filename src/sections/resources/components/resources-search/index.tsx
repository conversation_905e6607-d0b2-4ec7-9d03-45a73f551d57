import { useState, useEffect } from 'react';
import { useDebounce } from 'minimal-shared/hooks';

import { TextField, InputAdornment } from '@mui/material';

import { Iconify } from 'src/components/iconify';

const ResourcesSearch: React.FC<{
  searchQuery: string;
  onSearch: (query: string) => void;
}> = ({ searchQuery, onSearch }) => {
  const [value, setValue] = useState(searchQuery);

  const debouncedValue = useDebounce(value, 500);

  useEffect(() => {
    onSearch(debouncedValue);
  }, [debouncedValue, onSearch]);

  return (
    <TextField
      size="small"
      value={value}
      onChange={(event) => setValue(event.target.value)}
      placeholder="Search by title"
      slotProps={{
        input: {
          startAdornment: (
            <InputAdornment position="start">
              <Iconify icon="material-symbols:search" sx={{ color: 'text.disabled' }} />
            </InputAdornment>
          ),
        },
      }}
    />
  );
};

export default ResourcesSearch;
