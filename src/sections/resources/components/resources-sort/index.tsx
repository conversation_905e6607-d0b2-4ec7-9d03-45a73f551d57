import { usePopover } from 'minimal-shared/hooks';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import MenuList from '@mui/material/MenuList';
import MenuItem from '@mui/material/MenuItem';
import SwapVertIcon from '@mui/icons-material/SwapVert';

import { Iconify } from 'src/components/iconify';
import { CustomPopover } from 'src/components/custom-popover';

import { ResourceSort } from 'src/sections/resources/types';

type Props = {
  sort: ResourceSort;
  onSort: (newValue: ResourceSort) => void;
};

export const SORT_OPTIONS: { value: ResourceSort; label: string }[] = [
  { value: ResourceSort.Latest, label: 'Latest' },
  { value: ResourceSort.Oldest, label: 'Oldest' },
  { value: ResourceSort.TitleAsc, label: 'Title (A-Z)' },
  { value: ResourceSort.TitleDesc, label: 'Title (Z-A)' },
];

const ResourcesSort = ({ sort, onSort }: Props) => {
  const menuActions = usePopover();

  const renderMenuActions = () => (
    <CustomPopover
      open={menuActions.open}
      anchorEl={menuActions.anchorEl}
      onClose={menuActions.onClose}
    >
      <MenuList>
        {SORT_OPTIONS.map((option) => (
          <MenuItem
            key={option.value}
            selected={sort === option.value}
            onClick={() => {
              menuActions.onClose();
              onSort(option.value);
            }}
          >
            {option.label}
          </MenuItem>
        ))}
      </MenuList>
    </CustomPopover>
  );

  const selectedOption = SORT_OPTIONS.find((option) => option.value === sort);

  return (
    <>
      <Button
        variant="outlined"
        disableRipple
        color="inherit"
        onClick={menuActions.onOpen}
        endIcon={
          <Iconify
            icon={
              menuActions.open
                ? 'material-symbols:keyboard-arrow-up'
                : 'material-symbols:keyboard-arrow-down'
            }
          />
        }
        sx={{
          fontWeight: 'fontWeightSemiBold',
          textTransform: 'capitalize',
          flexShrink: 0,
          height: 40,
        }}
      >
        <SwapVertIcon />
        <Box component="span" sx={{ ml: 0.5, fontWeight: 'fontWeightBold' }}>
          {selectedOption?.label}
        </Box>
      </Button>

      {renderMenuActions()}
    </>
  );
};

export default ResourcesSort;
