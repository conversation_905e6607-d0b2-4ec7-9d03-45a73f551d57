import { m } from 'framer-motion';
import { useSelector } from 'react-redux';
import { usePopover } from 'minimal-shared/hooks';

import { Badge, Tooltip, IconButton } from '@mui/material';

import { selectPendingResourceUploads } from 'src/store/slices/resources/selectors';

import { Iconify } from 'src/components/iconify';
import { varTap, varHover, transitionTap } from 'src/components/animate';

import UploadingPopover from './uploading-popover';

const UploadResourceButton: React.FC = () => {
  const { open, anchorEl, onClose, onOpen } = usePopover();
  const pendingUploads = useSelector(selectPendingResourceUploads);

  return (
    <>
      <Tooltip title="Uploads" arrow>
        <IconButton
          component={m.button}
          whileTap={varTap(0.96)}
          whileHover={varHover(1.04)}
          transition={transitionTap()}
          aria-label="Upload button"
          onClick={onOpen}
        >
          <Badge badgeContent={pendingUploads.length} color="error">
            <Iconify width={24} icon="material-symbols:cloud-upload" />
          </Badge>
        </IconButton>
      </Tooltip>

      <UploadingPopover open={open} anchorEl={anchorEl} onClose={onClose} />
    </>
  );
};

export default UploadResourceButton;
