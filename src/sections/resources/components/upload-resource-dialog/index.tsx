import type { DialogProps } from '@mui/material/Dialog';

import { toast } from 'sonner';
import { useMemo, useState, useEffect } from 'react';

import Dialog from '@mui/material/Dialog';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import { Box, Button, Typography } from '@mui/material';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';

import { useUploadFile } from 'src/utils/resource';

import { Upload } from 'src/components/upload';
import { Iconify } from 'src/components/iconify';
import { fileFormat } from 'src/components/file-thumbnail';

import ProjectDropdown from 'src/sections/projects/components/project-dropdown';

import { TranscodingMode } from 'src/types/resource';

import { RadioOption } from './radio-option';

// ----------------------------------------------------------------------

type Props = DialogProps & {
  open: boolean;
  title?: string;
  onClose: () => void;
  projectId?: string;
};

const MAX_FILE_SIZE = 5120 * 1024 * 1024; // 5GB

// Updated to support DOCX, PPTX, and image files
const ACCEPTED_FILE_FORMATS = ['audio', 'video', 'pdf', 'txt', 'md', 'word', 'powerpoint', 'image'];
const UNSUPPORTED_VIDEO_TYPES = ['video/x-matroska'];

const UploadResourceDialog: React.FC<Props> = ({
  open,
  onClose,
  title = 'Upload files',
  projectId,
  ...other
}) => {
  const uploadFile = useUploadFile();

  const [files, setFiles] = useState<File[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedProjectId, setSelectedProjectId] = useState<string>(projectId || '');
  const [cardOption, setCardOption] = useState<TranscodingMode>(TranscodingMode.KEEP_BOTH);

  useEffect(() => {
    if (!open) {
      setFiles([]);
    }
  }, [open]);

  const isWarningUnsupportedVideo = useMemo(
    () => files.some((file) => UNSUPPORTED_VIDEO_TYPES.includes(file.type)),
    [files]
  );

  const handleDrop = (acceptedFiles: File[]) => {
    // Only allow audio and video files
    const filteredFiles = acceptedFiles.filter((file) => {
      const format = fileFormat(file.name);
      return ACCEPTED_FILE_FORMATS.includes(format);
    });

    setFiles((prevFiles) => [...prevFiles, ...filteredFiles]);
  };

  const handleRemoveFile = (inputFile: File | string) => {
    const filtered = files.filter((file) => file !== inputFile);
    setFiles(filtered);
  };

  const handleRemoveAllFiles = () => {
    setFiles([]);
  };

  const handleUpload = async () => {
    if (!files.length) return;
    try {
      setIsSubmitting(true);
      await Promise.all(
        files.map((file) => {
          if (UNSUPPORTED_VIDEO_TYPES.includes(file.type)) {
            return uploadFile(file, selectedProjectId, undefined, {
              transcoded: true,
              mode: cardOption === TranscodingMode.KEEP_BOTH ? 'keep-both' : 'delete-original',
            });
          }
          return uploadFile(file, selectedProjectId);
        })
      );
      toast.success('Files submitted. Uploading in progress...');
      onClose();
    } catch (error: any) {
      toast.error('Something wrong! Unable to submit files', {
        description: error.message,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderUnsupportedVideoWarning = () => {
    if (!isWarningUnsupportedVideo) return null;

    return (
      <Box sx={{ width: '100%', marginTop: 2 }}>
        <Typography variant="body2">
          We&apos;ll convert all .mkv files to .mp4 for transcription.
          <br />
          Keep the original .mkv files too?
        </Typography>
        <div style={{ display: 'flex', gap: 4, flexDirection: 'column', marginTop: 4 }}>
          <RadioOption
            sx={{ width: '100%' }}
            value={TranscodingMode.KEEP_BOTH}
            label="Keep .mkv and .mp4"
            selected={cardOption === TranscodingMode.KEEP_BOTH}
            onChange={(value) => setCardOption(value as TranscodingMode)}
          />
          <RadioOption
            sx={{ width: '100%' }}
            value={TranscodingMode.DELETE_ORIGINAL}
            label="Keep only .mp4"
            selected={cardOption === TranscodingMode.DELETE_ORIGINAL}
            onChange={(value) => setCardOption(value as TranscodingMode)}
          />
        </div>
      </Box>
    );
  };

  return (
    <Dialog fullWidth maxWidth="sm" open={open} onClose={onClose} {...other}>
      <DialogTitle sx={[(theme) => ({ p: theme.spacing(3, 3, 2, 3) })]}>{title}</DialogTitle>

      <DialogContent
        dividers
        sx={{
          pt: 1,
          pb: 0,
          border: 'none',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          gap: 1,
        }}
      >
        <ProjectDropdown
          projectId={selectedProjectId}
          onChange={setSelectedProjectId}
          sx={{ mb: 2 }}
        />
        <Upload
          multiple
          value={files}
          onDrop={handleDrop}
          onRemove={handleRemoveFile}
          maxSize={MAX_FILE_SIZE}
          helperText="PDF, TXT, MD, DOCX, PPTX, JPG, JPEG, PNG, GIF, BMP, SVG, WEBP, MP3, M4A, WAV, AAC, OGG, FLAC, WMA, MP4, MOV, AVI, MPG, WEBM, WMV, FLV, MKV"
        />
        {renderUnsupportedVideoWarning()}
      </DialogContent>

      <DialogActions>
        <LoadingButton
          variant="contained"
          startIcon={<Iconify icon="material-symbols:cloud-upload" />}
          onClick={handleUpload}
          disabled={!files.length}
          loading={isSubmitting}
        >
          Upload
        </LoadingButton>
        {!!files.length && (
          <Button variant="outlined" color="inherit" onClick={handleRemoveAllFiles}>
            Remove all
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default UploadResourceDialog;
