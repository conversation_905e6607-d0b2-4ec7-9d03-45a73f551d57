import type { Session } from 'src/types';

import { toast } from 'sonner';
import { useState, useCallback } from 'react';

import { Stack, Tooltip, IconButton, Typography, CircularProgress } from '@mui/material';

import { SessionStatus } from 'src/types';
import { useRetrySessionMutation } from 'src/store/api/sessions';

import { Iconify } from 'src/components/iconify';

import { getSessionStatusColor, getSessionStatusDisplay } from 'src/sections/recordings/utils';

import type { ResourceItem } from '../components/resources-list';

const useSessionsToResourceItems = () => {
  const [retrySession] = useRetrySessionMutation();
  const [retrySessionIds, setRetrySessionIds] = useState<string[]>([]);

  const handleRetrySession = useCallback(
    async (sessionId: string) => {
      try {
        setRetrySessionIds((prev) => [...prev, sessionId]);
        toast.success('Retrying to process session...');
        await retrySession({ id: sessionId }).unwrap();
      } catch (error: unknown) {
        toast.error('Failed to process session', {
          description: error instanceof Error ? error.message : 'Unknown error',
        });
      } finally {
        setRetrySessionIds((prev) => prev.filter((id) => id !== sessionId));
      }
    },
    [retrySession]
  );

  const convertSessionsToResourceItems = useCallback(
    (sessions: Session[]) =>
      sessions.map<ResourceItem>((session) => {
        const statusDisplay = getSessionStatusDisplay(session);
        const statusColor = getSessionStatusColor(session);
        const isRetryLoading = retrySessionIds.includes(session.id);
        const allowRetry = [SessionStatus.Failed, SessionStatus.Processing].includes(
          session.status
        );

        const title = (
          <Stack key={session.id} direction="row" alignItems="center" gap={1}>
            <Typography
              variant="body2"
              color="textSecondary"
              component="div"
              sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
            >
              Status:
              <Typography variant="body2" color={statusColor} fontWeight={600}>
                {statusDisplay}
              </Typography>
            </Typography>

            {allowRetry && (
              <>
                {isRetryLoading ? (
                  <CircularProgress size={16} />
                ) : (
                  <Tooltip title="Retry">
                    <IconButton size="small" onClick={() => handleRetrySession(session.id)}>
                      <Iconify icon="material-symbols:refresh" />
                    </IconButton>
                  </Tooltip>
                )}
              </>
            )}
          </Stack>
        );

        return {
          id: session.id,
          ffprobe: null,
          duration: 0,
          thumbnailUrl: '',
          fileSize: 0,
          fileName: session.title,
          name: session.title,
          createdById: session.createdById,
          url: session.meetingUrl,
          createdAt: session.createdAt,
          fileLastModified: session.createdAt,
          cardType: 'session',
          title,
          meetingUrl: session.meetingUrl,
        };
      }),
    [retrySessionIds, handleRetrySession]
  );

  return convertSessionsToResourceItems;
};

export default useSessionsToResourceItems;
