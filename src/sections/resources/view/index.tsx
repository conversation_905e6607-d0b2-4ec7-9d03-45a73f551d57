import type { ResourceItem } from 'src/sections/resources/components/resources-list';

import { useDispatch } from 'react-redux';
import { useMemo, useEffect } from 'react';
import { useSearchParams } from 'react-router';

import { Box, Stack, Tooltip, Typography, CircularProgress } from '@mui/material';

import useUserSessions from 'src/hooks/user-sessions';
import useUserStatistics from 'src/hooks/user-statistics';

import { useAppSelector } from 'src/store';
import { DashboardContent } from 'src/layouts/dashboard';
import { viewResource } from 'src/store/slices/resources/slice';
import { selectLastViewedProjectId } from 'src/store/slices/settings/selectors';

import { Iconify } from 'src/components/iconify';

import ResourcesList from 'src/sections/resources/components/resources-list';

import useGetResources from '../hooks/get-resources';
import { RESOURCE_LIST_HEADER_HEIGHT } from '../types';
import useSessionsToResourceItems from '../hooks/sessions-to-resource-items';
import { FilePreviewerModal } from '../components/resource-card/components/file-previewer-modal/file-previewer-modal';
import { FilePreviewerProvider } from '../components/resource-card/components/file-previewer-modal/file-previewer-provider';

const ResourcesView: React.FC = () => {
  const convertSessionsToResourceItems = useSessionsToResourceItems();
  const currentProjectId = useAppSelector(selectLastViewedProjectId);

  const { resources, isLoading } = useGetResources();

  const { ongoingSessions = [] } = useUserSessions({ projectId: currentProjectId || undefined });
  const statistics = useUserStatistics();
  const [params] = useSearchParams();
  const dispatch = useDispatch();
  const resourceId = params.get('resourceId') as string;

  const resourceItems: ResourceItem[] = useMemo(
    () => [...convertSessionsToResourceItems(ongoingSessions), ...resources],
    [resources, ongoingSessions, convertSessionsToResourceItems]
  );

  useEffect(() => {
    if (!resourceId) return;

    const resource = resourceItems.find((item) => item.id === resourceId);
    if (!resource) return;

    // Only dispatch if not already viewing this resource
    dispatch(viewResource(resource));
  }, [resourceId, resourceItems, dispatch]);

  return (
    <DashboardContent maxWidth="xl" sx={{ width: '100%' }}>
      <Stack
        direction="row"
        flexWrap="wrap"
        alignItems="flex-start"
        justifyContent="space-between"
        sx={{ height: RESOURCE_LIST_HEADER_HEIGHT, gap: 1, mb: 2 }}
      >
        <Stack direction="column" gap={1}>
          <Typography variant="h4">Recent</Typography>
          {statistics && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                {statistics.totalDurationsInMins} mins | {statistics.totalTranscriptionsWordCount}{' '}
                words | {statistics.totalResourcesCount} sources
              </Typography>
              <Tooltip title="Total usage over time" arrow>
                <Iconify
                  icon="material-symbols:info"
                  sx={{ width: 12, height: 12, color: 'text.secondary' }}
                />
              </Tooltip>
            </Box>
          )}
        </Stack>
      </Stack>
      <Box
        sx={{
          display: 'flex',
          justifyContent: isLoading ? 'center' : 'flex-start',
        }}
      >
        {isLoading ? (
          <CircularProgress size="3rem" />
        ) : (
          <FilePreviewerProvider resources={resourceItems}>
            <ResourcesList items={resourceItems ?? []} />
            <FilePreviewerModal />
          </FilePreviewerProvider>
        )}
      </Box>
    </DashboardContent>
  );
};

export default ResourcesView;
