import type { ApiRequestConfig } from 'src/store/api/types';

import type {
  CreateMessageRequest,
  ConvertToResourceRequest,
  CreateConversationRequest,
} from './types';

export interface ChatApiConfigs {
  createConversation: ApiRequestConfig<{
    payload: CreateConversationRequest;
  }>;
  sendMessage: ApiRequestConfig<{
    conversationId: string;
    payload: CreateMessageRequest;
  }>;
  getConversation: ApiRequestConfig<{ id: string }>;
  deleteConversation: ApiRequestConfig<{ id: string }>;
  convertToResource: ApiRequestConfig<{ id: string }>;
}

export const chatApiConfigs: ChatApiConfigs = {
  createConversation: (args) => ({
    method: 'POST',
    uri: 'agent/conversations',
    data: args.payload,
  }),
  sendMessage: (args) => ({
    method: 'POST',
    uri: `agent/conversations/${args.conversationId}/message/stream`,
    data: args.payload,
  }),
  getConversation: (args) => ({
    method: 'GET',
    uri: `agent/conversations/${args.id}`,
  }),
  deleteConversation: (args) => ({
    method: 'DELETE',
    uri: `agent/conversations/${args.id}`,
  }),
  convertToResource: (args: { id: string; payload?: ConvertToResourceRequest }) => ({
    method: 'POST',
    uri: `agent/conversations/${args.id}/convert-to-resource`,
    data: args.payload || {},
  }),
};
