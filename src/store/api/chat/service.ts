import { apiService } from '..';
import { chatApiConfigs } from './configs';

import type {
  CreateMessageRequest,
  ConvertToResourceRequest,
  ConvertToResourceResponse,
  CreateConversationResponse,
} from './types';

export const chatService = apiService.injectEndpoints({
  endpoints: (build) => ({
    createConversation: build.mutation<CreateConversationResponse, {}>({
      query: chatApiConfigs.createConversation,
      invalidatesTags: ['Conversations'],
    }),

    sendMessage: build.mutation<
      void,
      {
        conversationId: string;
        payload: CreateMessageRequest;
      }
    >({
      query: chatApiConfigs.sendMessage,
      invalidatesTags: (result, error, { conversationId }) => [
        { type: 'Conversations', id: conversationId },
        'Messages',
      ],
    }),

    convertToResource: build.mutation<
      ConvertToResourceResponse,
      {
        id: string;
        payload?: ConvertToResourceRequest;
      }
    >({
      query: chatApiConfigs.convertToResource,
      invalidatesTags: (result, error, { id }) => [
        { type: 'Conversations', id },
        'Resources',
        { type: 'Resources', id: 'LIST' },
      ],
    }),
  }),
  overrideExisting: false,
});

export const {
  useCreateConversationMutation,
  useSendMessageMutation,
  useConvertToResourceMutation,
} = chatService;
