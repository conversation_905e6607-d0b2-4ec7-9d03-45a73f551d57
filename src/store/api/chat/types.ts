export type CreateConversationRequest = {
  projectId: string;
};

export type CreateConversationResponse = {
  data: {
    id: string;
    createdAt: string;
    createdById: string;
    projectId: string;
  };
};

export type CreateMessageRequest = {
  conversationId: string;
  message?: string;
  images?: Array<{
    id: string;
    data: string; // Base64 encoded image data
    filename: string;
    mimeType: string;
    size: number;
  }>;
  model?: string;
  resources?: string[];
  preferences?: object;
};

export type ConvertToResourceRequest = {
  format?: 'markdown' | 'docx' | 'txt';
};

export type ConvertToResourceResponse = {
  success: boolean;
  message: string;
  data: {
    id: string;
    title: string;
    fileName: string;
    fileSize: number;
    projectId: string;
    createdById: string;
    createdAt: string;
    gcsFilePath: string;
    uploadAction: string;
  };
  statusCode: number;
};
