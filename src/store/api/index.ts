import { createApi } from '@reduxjs/toolkit/query/react';

import { baseApiCall } from 'src/lib/axios';

import type { BaseQueryResult } from './types';

// @ts-expect-error Check type error later
const baseQuery: BaseQueryResult = async (args) => {
  try {
    const result = await baseApiCall(args);
    return { data: result };
  } catch (error) {
    return { error };
  }
};

export const apiService = createApi({
  reducerPath: 'api',
  baseQuery,
  keepUnusedDataFor: 5,
  tagTypes: [
    'Resources',
    'ResourceDetails',
    'Sessions',
    'Projects',
    'ProjectFolders',
    'InitialContext',
    'Notes',
    'Calendar',
    'ProjectMembers',
    'PendingInvitations',
    'PublishedShareLinks',
    'AccessRequests',
    'UserFeedback',
    'Chat',
    'Conversations',
    'Messages',
    'UrlResolver',
  ],
  endpoints: () => ({}),
});
