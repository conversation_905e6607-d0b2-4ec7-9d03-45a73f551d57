import { projectsService } from './service';

export const {
  useGetProjectsQuery,
  useCreateProjectMutation,
  useDeleteProjectMutation,
  useLazyGetProjectDetailsQuery,
  useGetProjectDetailsQuery,
  useGetProjectInfoQuery,
  useLazyGetProjectInfoQuery,
  useGetProjectResourcesQuery,
  useLazyGetProjectResourcesQuery,
  useGetProjectAllResourceIdsQuery,
  useLazyGetProjectAllResourceIdsQuery,
  useCreateProjectFolderMutation,
  useDeleteProjectFolderMutation,
  useGetProjectFolderDetailsQuery,
  useLazyGetListProjectMembersQuery,
  useLazyGetListPendingInvitationsQuery,
  useLazyGetListAccessRequestsQuery,
  useInviteUserMutation,
  useAcceptInvitationMutation,
  useResendInvitationMutation,
  useCancelInvitationMutation,
  useChangeInvitationRoleMutation,
  useChangeRoleOfProjectMemberMutation,
  usePublishShareLinkMutation,
  useApproveOrRejectAccessMutation,
  useSharedLinkVerificationMutation,
  useGetProjectMembershipQuery,
  useRemoveProjectMemberMutation,
  useLeaveProjectMutation,
  useCreateProjectDefaultMutation,
  useUpdateProjectMutation,
  useCreateProjectResourceWithTranscriptionMutation
} = projectsService;
