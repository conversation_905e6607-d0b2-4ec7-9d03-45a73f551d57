import type { Resource } from 'src/types';
import type { SortBy, Project, SortOrder, ProjectFolder, ProcessingStatus } from 'src/types/project';

export enum InvitationStatus {
  SENT = 'SENT',
  ACCEPTED = 'ACCEPTED',
  REJECTED = 'REJECTED',
}

export enum ProjectRole {
  VIEWER = 'VIEWER',
  EDITOR = 'EDITOR',
  COMMENTER = 'COMMENTER',
  OWNER = 'OWNER',
}

export enum RequestStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}

export interface CreateProjectRequest {
  name: string;
  description?: string;
}

export type UpdateProjectRequest = Partial<Project>;

export interface CreateProjectFolderRequest {
  name: string;
}

export interface GetProjectFolderDetailsResponse {
  folder: ProjectFolder;
  resources: Resource[];
}

export interface InviteUserQuery {
  force?: boolean;
}

export interface InviteUserRequest {
  email: string;
  role: ProjectRole;
}

export interface AcceptInvitationRequest {
  code: string;
}

export interface ResendInvitationRequest {
  email: string;
}

export interface ResendInvitationResponse {
  code: string;
  expiredAt: string;
}

export interface ChangeRoleOfProjectMemberRequest {
  role: ProjectRole;
}

export interface ChangeInvitationRoleRequest {
  role: ProjectRole;
}

export interface ChangeInvitationRoleResponse {
  invitationId: string;
  projectId: string;
  email: string;
  role: ProjectRole;
  status: InvitationStatus;
}

export interface ApproveOrRejectAccessRequest {
  isApproved: boolean;
}

export interface SharedLinkVerificationRequest {
  token: string;
}

export interface GetProjectMembershipRequest {
  id: string;
}

// Project Resources API query parameters
export interface GetProjectResourcesParams {
  page?: number;
  limit?: number;
  search?: string;
  fileType?: string; // comma-separated values
  processingStatus?: ProcessingStatus;
  sortBy?: SortBy;
  sortOrder?: SortOrder;
}

// Removed duplicate project resource types - now defined in src/types/project.ts
