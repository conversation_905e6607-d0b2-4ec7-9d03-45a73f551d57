import type { RootState } from 'src/store/reducers';
import type { ProjectFolder } from 'src/types/project';
import type { Draft, ThunkDispatch } from '@reduxjs/toolkit';

import { projectsService } from './service';

export const optimisticAddFolderToProject = (
  folders: Draft<ProjectFolder[]>,
  newFolder: ProjectFolder
) => {
  // Add to the beginning of the list (most recent first)
  folders.unshift(newFolder);
};

export const optimisticRemoveFolderFromProject = (
  folders: Draft<ProjectFolder[]>,
  folderId: string
) => {
  const index = folders.findIndex((folder) => folder.id === folderId);
  if (index !== -1) {
    folders.splice(index, 1);
  }
};

export const optimisticAddFolder = (
  dispatch: ThunkDispatch<any, any, any>,
  projectId: string,
  newFolder: ProjectFolder
) => {
  // Update project details to include the new folder
  dispatch(
    projectsService.util.updateQueryData('getProjectDetails', { id: projectId }, (draft) =>
      optimisticAddFolderToProject(draft.folders, newFolder)
    )
  );
};

export const optimisticRemoveFolder = (
  dispatch: ThunkDispatch<any, any, any>,
  projectId: string,
  folderId: string
) => {
  // Update project details to remove the folder
  dispatch(
    projectsService.util.updateQueryData('getProjectDetails', { id: projectId }, (draft) =>
      optimisticRemoveFolderFromProject(draft.folders, folderId)
    )
  );
};

export const optimisticUpdateResource = (
  dispatch: ThunkDispatch<any, any, any>,
  state: RootState,
  projectId: string,
  resourceId: string,
  resourceName: string,
) => {
  const queries = state.api.queries;

  Object.values(queries)
  .filter(q =>
    q?.endpointName === 'getProjectResources' &&
    (q?.originalArgs as any)?.id === projectId
  )
  .forEach(q =>
    (dispatch as any)(
      projectsService.util.updateQueryData(
        'getProjectResources',
        q?.originalArgs as any,
        draft => {
          const idx = draft.items.findIndex(i => i.id === resourceId);
          if (idx !== -1) {
            draft.items[idx].name = resourceName;
          }
        }
      )
    )
  );
};

export const optimisticRemoveResource = (
  dispatch: ThunkDispatch<any, any, any>,
  state: RootState,
  projectId: string,
  resourceId: string,
) => {
  const queries = state.api.queries;

  Object.values(queries)
    .filter(q =>
      q?.endpointName === 'getProjectResources' &&
      (q?.originalArgs as any)?.id === projectId
    )
    .forEach(q =>
      (dispatch as any)(
        projectsService.util.updateQueryData(
          'getProjectResources',
          q?.originalArgs as any,
          draft => {
            const idx = draft.items.findIndex(i => i.id === resourceId);
            if (idx !== -1) {
              draft.items.splice(idx, 1);
              draft.total = Math.max(0, draft.total - 1);
            }
          }
        )
      )
    );
};
