import type { Resource } from 'src/types';

import { apiService } from '..';
import { projectsService } from '../projects';
import { resourceApiConfigs } from './configs';
import { optimisticAddResource, optimisticUpdateResourceList } from './utils';

import type { BaseResponse } from '../types';
import type { ResourceConfigParams } from './configs';
import type { TranscodingStatusItem, GetResourceUploadUrlResponse } from './types';

export const resourcesService = apiService.injectEndpoints({
  endpoints: (build) => ({
    getResources: build.query<Resource[], {}>({
      query: resourceApiConfigs.getResources,
      transformResponse: (response: Resource[]) =>
        response.map((resource: Resource) => {
          const { transcription, ...rest } = resource;
          return {
            ...rest,
            transcription: transcription?.sort((a, b) => a.startTime - b.startTime),
          };
        }),
      providesTags: (result, error, arg) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: 'ResourceDetails' as const, id })),
              { type: 'Resources', id: 'LIST' },
              { type: 'Resources' },
            ]
          : ['Resources'],
    }),
    getResourceUploadUrl: build.mutation<
      GetResourceUploadUrlResponse,
      ResourceConfigParams<'getResourceUploadUrl'>
    >({
      query: resourceApiConfigs.getResourceUploadUrl,
    }),
    getResource: build.query<Resource, { id: string }>({
      query: resourceApiConfigs.getResource,
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        const { data } = await queryFulfilled;
        optimisticUpdateResourceList(dispatch, data);
      },
      transformResponse: (response: Resource) => ({
        ...response,
        transcription: response.transcription?.sort((a, b) => a.startTime - b.startTime),
      }),
      providesTags: (result) => [{ type: 'ResourceDetails', id: result?.id }],
    }),
    createResource: build.mutation<Resource, ResourceConfigParams<'createResource'>>({
      query: resourceApiConfigs.createResource,
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        try {
          const { data } = await queryFulfilled;
          optimisticAddResource(dispatch, data);
        } catch {
          // Error handling - the optimistic update will be automatically reverted
          // since we're not doing any manual cache updates on error
        }
      },
    }),
    updateResource: build.mutation<Resource, ResourceConfigParams<'updateResource'>>({
      query: resourceApiConfigs.updateResource,
      async onQueryStarted(arg, { dispatch, queryFulfilled, getState }) {
        try {
          const { data } = await queryFulfilled;
          optimisticUpdateResourceList(dispatch, data);
          
          // Update paginated project resources cache if resource belongs to a project
          if (data.projectId) {
            const state = getState() as any;
            const queries = state.api.queries;
            
            Object.keys(queries).forEach((queryKey) => {
              if (queryKey.startsWith('getProjectResources(')) {
                const query = queries[queryKey];
                if (query?.originalArgs?.id === data.projectId && query?.data?.items) {
                  dispatch(
                    projectsService.util.updateQueryData(
                      'getProjectResources',
                      query.originalArgs,
                      (draft) => {
                        const resourceIndex = draft.items.findIndex((item) => item.id === data.id);
                        if (resourceIndex !== -1) {
                          // Update the resource with new data
                          draft.items[resourceIndex] = {
                            ...draft.items[resourceIndex],
                            name: data.name,
                            fileName: data.fileName || data.name,
                            filename: data.fileName || data.name, // Legacy field
                            // Add any other fields that might be updated during rename
                          };
                        }
                      }
                    )
                  );
                }
              }
            });
          }
        } catch (error) {
          // Error handling - the optimistic updates will be automatically reverted
          console.error('Failed to update resource:', error);
        }
      },
    }),
    deleteResource: build.mutation<Resource, ResourceConfigParams<'deleteResource'>>({
      query: resourceApiConfigs.deleteResource,
      async onQueryStarted(arg, { dispatch, queryFulfilled, getState }) {
        // Capture project ID before deletion for potential future use
        const state = getState() as any;
        let resourceProjectId: string | null = null;
        
        // Try to find the resource's project ID from cached data
        const resourceQueries = state.api.queries;
        Object.keys(resourceQueries).forEach((queryKey) => {
          if (queryKey.startsWith('getResources(') || queryKey.startsWith('getProjectDetails(') || queryKey.startsWith('getProjectResources(')) {
            const query = resourceQueries[queryKey];
            if (query?.data) {
              // Check in resources array or items array
              const resources = query.data.resources || query.data.items || query.data;
              if (Array.isArray(resources)) {
                const resource = resources.find((r: any) => r.id === arg.id);
                if (resource?.projectId) {
                  resourceProjectId = resource.projectId;
                }
              }
            }
          }
        });

        // Perform optimistic removal from all relevant caches
        const patchResults = [];

        // Remove from global resources list
        const globalPatch = dispatch(
          resourcesService.util.updateQueryData('getResources', {}, (draft) =>
            draft.filter((item) => item.id !== arg.id)
          )
        );
        patchResults.push(globalPatch);

        // Update all project details caches that might contain this resource
        const projectDetailsQueries = state.api.queries;

        Object.keys(projectDetailsQueries).forEach((queryKey) => {
          if (queryKey.startsWith('getProjectDetails(')) {
            const query = projectDetailsQueries[queryKey];
            if (query?.data?.resources?.some((r: any) => r.id === arg.id)) {
              const projectId = query.originalArgs?.id;
              if (projectId) {
                const projectPatch = dispatch(
                  projectsService.util.updateQueryData(
                    'getProjectDetails',
                    { id: projectId },
                    (draft) => {
                      draft.resources = draft.resources.filter((item) => item.id !== arg.id);
                    }
                  )
                );
                patchResults.push(projectPatch);
              }
            }
          }
        });

        // Update all project folder details caches that might contain this resource
        Object.keys(projectDetailsQueries).forEach((queryKey) => {
          if (queryKey.startsWith('getProjectFolderDetails(')) {
            const query = projectDetailsQueries[queryKey];
            if (query?.data?.resources?.some((r: any) => r.id === arg.id)) {
              const { id: projectId, folderId } = query.originalArgs || {};
              if (projectId && folderId) {
                const folderPatch = dispatch(
                  projectsService.util.updateQueryData(
                    'getProjectFolderDetails',
                    { id: projectId, folderId },
                    (draft) => {
                      draft.resources = draft.resources.filter((item) => item.id !== arg.id);
                    }
                  )
                );
                patchResults.push(folderPatch);
              }
            }
          }
        });

        // Update all paginated project resources caches that might contain this resource
        Object.keys(projectDetailsQueries).forEach((queryKey) => {
          if (queryKey.startsWith('getProjectResources(')) {
            const query = projectDetailsQueries[queryKey];
            if (query?.data?.items?.some((r: any) => r.id === arg.id)) {
              const patchProjectResources = dispatch(
                projectsService.util.updateQueryData(
                  'getProjectResources',
                  query.originalArgs,
                  (draft) => {
                    draft.items = draft.items.filter((item) => item.id !== arg.id);
                    draft.total = Math.max(0, draft.total - 1);
                  }
                )
              );
              patchResults.push(patchProjectResources);
            }
          }
        });

        try {
          await queryFulfilled;
          // On success, keep the optimistic updates - no need to invalidate tags
        } catch {
          // Revert all optimistic updates on error
          patchResults.forEach((patch) => patch.undo());
        }
      },
    }),
    uploadResourceThumbnail: build.mutation<
      { url: string },
      ResourceConfigParams<'uploadResourceThumbnail'>
    >({
      query: resourceApiConfigs.uploadResourceThumbnail,
      async onQueryStarted(arg, { dispatch, queryFulfilled, getState }) {
        try {
          const { data } = await queryFulfilled;

          // Only update paginated project resources caches
          const state = getState() as any;
          const queries = state.api.queries;

          Object.keys(queries).forEach((queryKey) => {
            if (queryKey.startsWith('getProjectResources(')) {
              const query = queries[queryKey];
              if (query?.data?.items?.some((r: any) => r.id === arg.id)) {
                dispatch(
                  projectsService.util.updateQueryData(
                    'getProjectResources',
                    query.originalArgs,
                    (draft) => {
                      const resourceIndex = draft.items.findIndex((item) => item.id === arg.id);
                      if (resourceIndex !== -1) {
                        draft.items[resourceIndex] = {
                          ...draft.items[resourceIndex],
                          thumbnailUrl: data.url,
                        };
                      }
                    }
                  )
                );
              }
            }
          });
        } catch (error) {
          console.error('Failed to update thumbnail in cache:', error);
          // Error handling - the cache will remain in its previous state
        }
      },
    }),
    extendResourceRetention: build.mutation<
      Resource,
      ResourceConfigParams<'extendResourceRetention'>
    >({
      query: resourceApiConfigs.extendResourceRetention,
      invalidatesTags: (result) => {
        console.log('result', result);
        return ['Resources'];
      },
    }),
    transcodingStatus: build.query<
      TranscodingStatusItem[],
      ResourceConfigParams<'transcodingStatus'>
    >({
      query: resourceApiConfigs.transcodingStatus,
      transformResponse: (response: BaseResponse<TranscodingStatusItem[]>) => response.data,
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        try {
          const { data } = await queryFulfilled;
          // Update the resources in the cache
          dispatch(
            projectsService.util.updateQueryData('getProjectDetails', { id: arg.id }, (draft) => {
              data.forEach((item) => {
                const resource = draft.resources.find(
                  (r) => r.id === item.id && r.isTranscoding !== item.isTranscoding
                );
                if (!resource) return;
                resource.isTranscoding = item.isTranscoding;
              });
            })
          );
        } catch (error) {
          console.log('error', error);
          // Error handling is managed by the polling hook
        }
      },
    }),
  }),
  overrideExisting: true,
});
