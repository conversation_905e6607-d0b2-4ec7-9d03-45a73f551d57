import type { Resource, Transcription, TranscriptStatus } from 'src/types';

export enum ConvertDownloadFormat {
  Txt = 'txt',
  Docx = 'docx',
}

export interface GetResourceResponse {
  resource: Resource;
  transcription: Transcription[];
  transcriptionJobStatus: TranscriptStatus;
}

export interface GetResourceUploadUrlResponse {
  fields: {
    'x-ignore-file-name': string;
    [key: string]: string;
  };
  url: string;
}

export interface TranscodingStatusItem {
  id: string;
  isTranscoding: boolean;
}
