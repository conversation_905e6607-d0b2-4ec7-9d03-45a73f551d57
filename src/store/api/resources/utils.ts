import type { Draft } from 'immer';
import type { Resource } from 'src/types';
import type { ThunkDispatch } from '@reduxjs/toolkit';

import { FileType, ProcessingStatus } from 'src/types/project';

import { resourcesService } from './service';
import { projectsService } from '../projects';

export const optimisticUpdateResourceForList = (
  resources: Draft<Resource[]>,
  updatedResource: Resource
) => {
  const index = resources.findIndex((item) => item.id === updatedResource.id);
  if (index === -1) return;
  resources[index] = { ...resources[index], ...updatedResource };
};

export const optimisticUpdateResourceForDetails = (
  resource: Draft<Resource>,
  updatedResource: Resource
) => ({
  ...resource,
  ...updatedResource,
});

export const optimisticAddResourceToList = (
  resources: Draft<Resource[]>,
  newResource: Resource
) => {
  // Add to the beginning of the list (most recent first)
  resources.unshift(newResource);
};

export const optimisticRemoveResourceFromList = (
  resources: Draft<Resource[]>,
  resourceId: string
) => {
  const index = resources.findIndex((item) => item.id === resourceId);
  if (index !== -1) {
    resources.splice(index, 1);
  }
};

export const optimisticUpdateResourceList = (
  dispatch: ThunkDispatch<any, any, any>,
  updatedResource: Resource
) => {
  dispatch(
    resourcesService.util.updateQueryData('getResources', {}, (draft) =>
      optimisticUpdateResourceForList(draft, updatedResource)
    )
  );
  dispatch(
    resourcesService.util.updateQueryData('getResource', { id: updatedResource.id }, (draft) =>
      optimisticUpdateResourceForDetails(draft, updatedResource)
    )
  );

  if (updatedResource.projectId) {
    dispatch(
      projectsService.util.updateQueryData(
        'getProjectDetails',
        { id: updatedResource.projectId },
        (draft) => optimisticUpdateResourceForList(draft.resources, updatedResource)
      )
    );
  }
};

export const optimisticAddResource = (
  dispatch: ThunkDispatch<any, any, any>,
  newResource: Resource
) => {
  // Update global resources list
  dispatch(
    resourcesService.util.updateQueryData('getResources', {}, (draft) =>
      optimisticAddResourceToList(draft, newResource)
    )
  );

  // Update project details if resource belongs to a project
  if (newResource.projectId) {
    dispatch(
      projectsService.util.updateQueryData(
        'getProjectDetails',
        { id: newResource.projectId },
        (draft) => optimisticAddResourceToList(draft.resources, newResource)
      )
    );

    // Update paginated project resources (getProjectResources)
    // We need to update all cached queries for this project
    const state = (dispatch as any).getState();
    const queries = state.api.queries;
    
    Object.keys(queries).forEach((queryKey) => {
      if (queryKey.startsWith('getProjectResources(')) {
        const query = queries[queryKey];
        if (query?.originalArgs?.id === newResource.projectId && query?.data?.items) {
          // Convert Resource to ProjectResourceItem for paginated responses
          const projectResourceItem = {
            id: newResource.id,
            orgId: newResource.orgId || null,
            name: newResource.name,
            url: newResource.url,
            transcodedFileSize: null,
            isTranscoding: newResource.isTranscoding || false,
            fileSize: newResource.fileSize || 0,
            fileLastModified: newResource.fileLastModified?.toISOString() || new Date().toISOString(),
            sessionId: newResource.sessionId || null,
            duration: newResource.duration || 0,
            topics: newResource.topics || null,
            createdById: newResource.createdById,
            projectId: newResource.projectId!,
            folderId: newResource.folderId || null,
            originalResourceState: null,
            thumbnailResourceId: null,
            originalResourceId: null,
            type: 'file', // Default type for resources
            createdAt: newResource.createdAt.toISOString(),
            fileName: newResource.fileName || newResource.name,
            transcription: newResource.transcription || [],
            transcriptionJobStatus: newResource.transcriptionJobStatus || 'UnTranscript',
            userPermissions: newResource.userPermissions || {
              canView: true,
              canEdit: true,
              canComment: true,
            },
            isSpriteSheets: newResource.isSpriteSheets,
            vttSrc: newResource.vttSrc,
            transcodedUrl: newResource.transcodedUrl || null,
            // Legacy fields for backward compatibility
            filename: newResource.fileName || newResource.name,
            description: '', // Resource doesn't have description field
            fileType: FileType.UNKNOWN, // Resource doesn't have fileType field, will be determined by backend
            thumbnailUrl: newResource.thumbnailUrl || '',
            dateAdded: newResource.createdAt.toISOString(),
            processingStatus: newResource.isTranscoding ? ProcessingStatus.PROCESSING : ProcessingStatus.COMPLETED,
            lastModified: newResource.fileLastModified?.toISOString(),
          };

          // Update the specific paginated query
          dispatch(
            projectsService.util.updateQueryData(
              'getProjectResources',
              query.originalArgs,
              (draft) => {
                draft.items.unshift(projectResourceItem);
                draft.total += 1;
              }
            )
          );
        }
      }
    });
  }

  // Update project folder details if resource belongs to a folder
  if (newResource.projectId && newResource.folderId) {
    dispatch(
      projectsService.util.updateQueryData(
        'getProjectFolderDetails',
        { id: newResource.projectId, folderId: newResource.folderId },
        (draft) => optimisticAddResourceToList(draft.resources, newResource)
      )
    );
  }
};

export const optimisticRemoveResource = (
  dispatch: ThunkDispatch<any, any, any>,
  resourceId: string,
  projectId?: string,
  folderId?: string
) => {
  // Update global resources list
  dispatch(
    resourcesService.util.updateQueryData('getResources', {}, (draft) =>
      optimisticRemoveResourceFromList(draft, resourceId)
    )
  );

  // Update project details if resource belongs to a project
  if (projectId) {
    dispatch(
      projectsService.util.updateQueryData('getProjectDetails', { id: projectId }, (draft) =>
        optimisticRemoveResourceFromList(draft.resources, resourceId)
      )
    );
  }

  // Update project folder details if resource belongs to a folder
  if (projectId && folderId) {
    dispatch(
      projectsService.util.updateQueryData(
        'getProjectFolderDetails',
        { id: projectId, folderId },
        (draft) => optimisticRemoveResourceFromList(draft.resources, resourceId)
      )
    );
  }
};
