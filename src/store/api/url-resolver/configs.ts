import type { ApiRequestConfig } from 'src/store/api/types';

export interface UrlResolverApiConfigs {
  resolveUrl: ApiRequestConfig<{
    shortCode: string;
  }>;
  createShortlink: ApiRequestConfig<{
    payload: {
      originalUrl: string;
      urlType: string;
      entityId: string;
      expiresInDays?: number;
    };
  }>;
}

export type UrlResolverConfigParams<Config extends keyof UrlResolverApiConfigs> = Parameters<
  UrlResolverApiConfigs[Config]
>[0];

export const urlResolverApiConfigs: UrlResolverApiConfigs = {
  resolveUrl: (args) => ({
    method: 'GET',
    uri: `resolve/${args.shortCode}`,
  }),
  createShortlink: (args) => ({
    method: 'POST',
    uri: 'shortlink',
    data: args.payload,
  }),
};
