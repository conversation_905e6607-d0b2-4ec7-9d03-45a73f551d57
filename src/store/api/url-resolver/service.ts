import { apiService } from '..';
import { urlResolverApiConfigs } from './configs';

import type { UrlResolverConfigParams } from './configs';
import type { ResolveUrlResponse, CreateShortlinkResponse } from './types';

export const urlResolverService = apiService.injectEndpoints({
  endpoints: (build) => ({
    resolveUrl: build.query<ResolveUrlResponse, UrlResolverConfigParams<'resolveUrl'>>({
      query: urlResolverApiConfigs.resolveUrl,
      providesTags: ['UrlResolver'],
    }),
    createShortlink: build.mutation<CreateShortlinkResponse, UrlResolverConfigParams<'createShortlink'>>({
      query: urlResolverApiConfigs.createShortlink,
      invalidatesTags: ['UrlResolver'],
    }),
  }),
});
