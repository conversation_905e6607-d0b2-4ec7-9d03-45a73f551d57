import type { PersistConfig } from 'redux-persist';
import type { Store, Reducer } from '@reduxjs/toolkit';
import type { TypedUseSelectorHook } from 'react-redux';

import { useSelector } from 'react-redux';
import { persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';

import { apiService } from './api';
import rootReducer from './reducers';

import type { RootState, ApplicationActions } from './reducers';

export type IApplicationState = RootState;

export type AppStore = Store<IApplicationState | ApplicationActions>;

let store: AppStore | undefined = undefined;

export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

export const getStore = (): AppStore => store!;

const persistConfig: PersistConfig<IApplicationState> = {
  key: 'root',
  storage,
  // Only persist settings and agent settings
  whitelist: ['settings', 'agentSettings'],
};

const persistedReducer = persistReducer(persistConfig, rootReducer) as Reducer;

const setupStore = (preloadedState?: IApplicationState) =>
  configureStore({
    reducer: persistedReducer,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        immutableCheck: false,
        serializableCheck: false,
      }).concat(apiService.middleware),
    preloadedState,
    devTools: {
      trace: false,
    },
  });

export const configureAppStore = () => {
  store = setupStore({} as IApplicationState);
  setupListeners(store.dispatch);
};
