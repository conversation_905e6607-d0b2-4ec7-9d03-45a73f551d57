import type { AnyAction } from '@reduxjs/toolkit';

import { combineReducers } from '@reduxjs/toolkit';

import { notesSlice } from 'src/store/slices/notes/slice';
import { notesInitialState } from 'src/store/slices/notes/types';
import { uiSlice, uiInitialState } from 'src/store/slices/ui/slice';
import { settingsSlice, settingsInitialState } from 'src/store/slices/settings/slice';
import { resourcesSlice, resourcesInitialState } from 'src/store/slices/resources/slice';
import { agentSettingsSlice, agentSettingsInitialState } from 'src/store/slices/agent-settings';
import {
  liveTranscriptionSlice,
  liveTranscriptionInitialState,
} from 'src/store/slices/live-transcription/slice';

import { apiService } from '../api';

export type RootState = ReturnType<typeof reducers>;

export type ApplicationActions = AnyAction;

const reducers = combineReducers({
  ui: uiSlice.reducer,
  resources: resourcesSlice.reducer,
  notes: notesSlice.reducer,
  settings: settingsSlice.reducer,
  liveTranscription: liveTranscriptionSlice.reducer,
  agentSettings: agentSettingsSlice.reducer,
  [apiService.reducerPath]: apiService.reducer,
});

export const defaultState: RootState = {
  ui: uiInitialState,
  resources: resourcesInitialState,
  notes: notesInitialState,
  settings: settingsInitialState,
  liveTranscription: liveTranscriptionInitialState,
  agentSettings: agentSettingsInitialState,
  [apiService.reducerPath]: {} as ReturnType<typeof apiService.reducer>,
};

const rootReducer = (state: RootState = defaultState, action: ApplicationActions) =>
  reducers(state, action);

export default rootReducer;
