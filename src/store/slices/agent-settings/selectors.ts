import type { ChatPreferences } from 'src/types';
import type { IApplicationState } from 'src/store';

import { createSelector } from '@reduxjs/toolkit';

const DEFAULT_CHAT_PREFERENCES: ChatPreferences = {
  enableSearchQuery: true,
  userInstructions: '',
};

export const selectChatPreferences = (state: IApplicationState) =>
  state.agentSettings.chatPreferences;

export const selectChatPreferencesForProject = createSelector(
  [selectChatPreferences, (_, projectId: string) => projectId],
  (chatPreferences, projectId) => chatPreferences[projectId] ?? DEFAULT_CHAT_PREFERENCES
);
