import type { ChatPreferences } from 'src/types';
import type { PayloadAction } from '@reduxjs/toolkit';

import { createSlice } from '@reduxjs/toolkit';

import type { AgentSettingsSliceState } from './types';

export const agentSettingsInitialState: AgentSettingsSliceState = {
  chatPreferences: {},
};

export const agentSettingsSlice = createSlice({
  name: 'agentSettings',
  initialState: agentSettingsInitialState,
  reducers: {
    setChatPreferences: (
      state,
      action: PayloadAction<{ projectId: string; preferences: ChatPreferences }>
    ) => {
      const { projectId, preferences } = action.payload;
      state.chatPreferences[projectId] = preferences;
    },
    clearChatPreferences: (state, action: PayloadAction<string>) => {
      const projectId = action.payload;
      delete state.chatPreferences[projectId];
    },
    clearAllChatPreferences: (state) => {
      state.chatPreferences = {};
    },
  },
});

export const { setChatPreferences, clearChatPreferences, clearAllChatPreferences } =
  agentSettingsSlice.actions;

export default agentSettingsSlice.reducer;
