import type { PayloadAction } from '@reduxjs/toolkit';
import type { Note } from 'src/store/api/notes/types';

import { createSlice } from '@reduxjs/toolkit';

import { notesInitialState } from './types';

export const notesSlice = createSlice({
  name: 'notes',
  initialState: notesInitialState,
  reducers: {
    viewNote: (state, action: PayloadAction<Note | null>) => {
      state.focusedNote = action.payload;
    },
    selectNote: (state, action: PayloadAction<Note | null>) => {
      state.selectedNote = action.payload;
    },
    storeNotes: (state, action: PayloadAction<Note[]>) => {
      state.notes = action.payload;
    },
    addNote: (state, action: PayloadAction<Note>) => {
      state.notes.push(action.payload);
    },
    updateNote: (state, action: PayloadAction<Note>) => {
      const index = state.notes.findIndex((note) => note.id === action.payload.id);
      if (index !== -1) {
        state.notes[index] = action.payload;
      }
    },
    deleteNote: (state, action: PayloadAction<string>) => {
      state.notes = state.notes.filter((note) => note.id !== action.payload);
      if (state.selectedNote?.id === action.payload) {
        state.selectedNote = null;
      }
      if (state.focusedNote?.id === action.payload) {
        state.focusedNote = null;
      }
    },
  },
});

export const {
  viewNote,
  selectNote,
  storeNotes,
  addNote,
  updateNote,
  deleteNote,
} = notesSlice.actions;

export default notesSlice.reducer; 