import type { IApplicationState } from 'src/store';

import { createSelector } from '@reduxjs/toolkit';

export const selectResourceUploadQueue = (state: IApplicationState) =>
  state.resources.resourceUploadQueue;

export const selectSortedResourceUploadQueue = createSelector(
  [selectResourceUploadQueue],
  (queue) =>
    [...queue].sort((a, b) => {
      if (!a.submittedAt || !b.submittedAt) return 0;
      const firstDate = new Date(a.submittedAt).getTime();
      const secondDate = new Date(b.submittedAt).getTime();

      return secondDate - firstDate;
    })
);

export const selectPendingResourceUploads = createSelector(
  [selectSortedResourceUploadQueue],
  (queue) =>
    queue.filter(
      (item) => item.status && ['pending', 'uploading', 'processing'].includes(item.status)
    )
);

export const selectPendingResourceUploadsForProject = createSelector(
  [selectPendingResourceUploads, (_, projectId: string) => projectId],
  (queue, projectId) => queue.filter((item) => item.projectId === projectId)
);

export const selectActiveResourceUploadsForProject = createSelector(
  [selectSortedResourceUploadQueue, (_, projectId: string) => projectId],
  (queue, projectId) => {
    const filtered = queue.filter((item) => {
      if (item.projectId !== projectId) return false;

      // Show pending, uploading, and processing uploads
      if (item.status && ['pending', 'uploading', 'processing'].includes(item.status)) {
        return true;
      }

      return false;
    });

    // Sort by status priority: uploading > processing > pending
    return filtered.sort((a, b) => {
      const statusPriority = {
        processing: 3,
        uploading: 2,
        pending: 1,
      };

      const aPriority = statusPriority[a.status as keyof typeof statusPriority] || 0;
      const bPriority = statusPriority[b.status as keyof typeof statusPriority] || 0;

      // Primary sort by status priority (higher priority first)
      if (aPriority !== bPriority) {
        return bPriority - aPriority;
      }

      // Secondary sort by submission date (newer first) for same status
      if (!a.submittedAt || !b.submittedAt) return 0;
      const firstDate = new Date(a.submittedAt).getTime();
      const secondDate = new Date(b.submittedAt).getTime();
      return secondDate - firstDate;
    });
  }
);

export const selectFocusedResource = (state: IApplicationState) => state.resources.focusedResource;

export const selectSelectedResources = (state: IApplicationState) =>
  state.resources.selectedResources;

export const selectSelectedResourceIds = createSelector(
  [selectSelectedResources],
  (selectedResources) => selectedResources.map((resource) => resource.id)
);
