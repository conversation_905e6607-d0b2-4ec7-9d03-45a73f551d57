import type { PayloadAction } from '@reduxjs/toolkit';
import type { Resource, ResourceUploadQueueItem } from 'src/types';

import { createSlice } from '@reduxjs/toolkit';

export type ResourceSliceState = {
  resourceUploadQueue: ResourceUploadQueueItem[];
  focusedResource: Resource | null;
  selectedResources: Resource[];
};

export const resourcesInitialState: ResourceSliceState = {
  resourceUploadQueue: [],
  focusedResource: null,
  selectedResources: [],
};

export const resourcesSlice = createSlice({
  name: 'resources',
  initialState: resourcesInitialState,
  reducers: {
    viewResource: (state, action: PayloadAction<Resource | null>) => {
      state.focusedResource = action.payload;
    },
    addToUploadQueue: (state, action: PayloadAction<ResourceUploadQueueItem>) => {
      state.resourceUploadQueue.push({
        ...action.payload,
        status: 'pending',
        submittedAt: new Date(),
      });
    },
    loadUploadQueueFromCache: (state, action: PayloadAction<ResourceUploadQueueItem[]>) => {
      // Merge cached items with existing queue, avoiding duplicates
      const existingIds = new Set(state.resourceUploadQueue.map((item) => item.id));
      const newItems = action.payload.filter((item) => !existingIds.has(item.id));
      state.resourceUploadQueue.push(...newItems);
    },
    updateUploadQueueItem: (
      state,
      action: PayloadAction<{ id: string; data: Partial<ResourceUploadQueueItem> }>
    ) => {
      const { id, data } = action.payload;
      const index = state.resourceUploadQueue.findIndex((item) => item.id === id);
      if (index !== -1) {
        state.resourceUploadQueue[index] = {
          ...state.resourceUploadQueue[index],
          ...data,
        };
      }
    },
    removeFromUploadQueue: (state, action: PayloadAction<{ id: string }>) => {
      state.resourceUploadQueue = state.resourceUploadQueue.filter(
        (item) => item.id !== action.payload.id
      );
    },
    clearCompletedUploads: (state) => {
      state.resourceUploadQueue = state.resourceUploadQueue.filter(
        (item) => item.status !== 'completed'
      );
    },
    toggleSelectResource: (state, action: PayloadAction<Resource>) => {
      const index = state.selectedResources.findIndex((item) => item.id === action.payload.id);
      if (index !== -1) {
        state.selectedResources.splice(index, 1);
      } else {
        state.selectedResources.push(action.payload);
      }
    },
    selectAllResources: (state, action: PayloadAction<Resource[]>) => {
      state.selectedResources = action.payload;
    },
    clearSelectedResources: (state) => {
      state.selectedResources = [];
    },
  },
});

export const {
  addToUploadQueue,
  loadUploadQueueFromCache,
  removeFromUploadQueue,
  updateUploadQueueItem,
  clearCompletedUploads,
  viewResource,
  toggleSelectResource,
  clearSelectedResources,
  selectAllResources,
} = resourcesSlice.actions;

export default resourcesSlice.reducer;
