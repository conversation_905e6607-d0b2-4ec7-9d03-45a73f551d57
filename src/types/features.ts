/**
 * List of available feature flags. Configurable in PostHog.
 */
export enum AppFeatures {
  INTERNAL = 'internal', // For internal development
  PROJECT_COLLABORATION = 'project-collaboration',
  LIVE_TRANSCRIBE = 'live-transcribe',
  MICROSOFT_LOGIN = 'microsoft-login',
  CHAT_PREFERENCES = 'chat-preferences',
  NEW_PREVIEW_EDITOR_FONT_SIZE_CONTROL = 'new-preview-editor-font-size-control',
  SUPPORT_CHAT_IMAGES = 'support-chat-images',
  MULTI_MODELS = 'multi-models', // Multi-provider AI model support
}
