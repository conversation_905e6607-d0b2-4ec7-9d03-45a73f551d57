import type { Note } from 'src/store/api/notes';

import type { Resource } from './resource';
import type { SessionStatus } from './session';
import type { RecallBotStatus } from './recall';

export enum SSEEventType {
  // Connection events
  CONNECTED = 'connected',
  HEARTBEAT = 'heartbeat',

  // Resource events
  RESOURCE_CREATED = 'resource.created',
  RESOURCE_UPDATED = 'resource.updated',
  RESOURCE_DELETED = 'resource.deleted',
  RESOURCE_MOVED_OUT_OF_PROJECT = "resource.moved-out-of-project", // In case resource is moved out of a project
  RESOURCE_MOVED_TO_PROJECT = "resource.moved-to-project", // In case resource is moved to a project

  // Session events
  SESSION_CREATED = 'session.created',
  SESSION_STATUS_UPDATED = 'session.status-updated',
  SESSION_DELETED = 'session.deleted',

  // ACL events
  ACL_MEMBER_NEW_MEMBER = 'acl.member.new-member',
  ACL_MEMBER_CHANGE_ROLE = 'acl.member.change-role',
  ACL_MEMBER_REMOVED = 'acl.member.removed',
  ACL_MEMBER_LEAVE = 'acl.member.leave',
  ACL_MEMBER_ACCEPT_INVITATION = 'acl.member.accept-invitation',

  // Project events
  PROJECT_DELETED = 'project.deleted',

  // Note events
  NOTE_CREATED = 'note.created',
  NOTE_UPDATED = 'note.updated',
  NOTE_DELETED = 'note.deleted',
}

// SSE Event types for project real-time updates
export interface ProjectEventBase {
  eventId: string;
  type: string;
  projectId: string;
  timestamp: string;
  userId?: string;
}

export interface ResourceCreatedEvent extends ProjectEventBase {
  type: SSEEventType.RESOURCE_CREATED;
  data: Resource;
}

export interface ResourceUpdatedEvent extends ProjectEventBase {
  type: SSEEventType.RESOURCE_UPDATED;
  data: {
    id: string;
    name: string;
    updatedAt: string;
    updatedBy: string;
  };
}

export interface ResourceDeletedEvent extends ProjectEventBase {
  type: SSEEventType.RESOURCE_DELETED;
  data: {
    id: string;
    deletedAt: string;
    deletedBy: string;
  };
}

export interface ResourceMovedEvent extends ProjectEventBase {
  type: SSEEventType.RESOURCE_MOVED_OUT_OF_PROJECT | SSEEventType.RESOURCE_MOVED_TO_PROJECT;
  data: {
    id: string;
    name: string;
    movedById: string;
    movedBy: string;
    movedAt: string;
    movedFrom: string;
    movedTo: string;
  };
}

export interface SessionCreatedEvent extends ProjectEventBase {
  type: SSEEventType.SESSION_CREATED;
  data: {
    sessionId: string;
    title: string;
    status: 'NotStarted';
    createdBy: string;
    updatedBy: string;
    updatedAt: string;
    recallBotStatus: RecallBotStatus | null;
    meetingUrl: string;
  };
}

export interface SessionStatusUpdatedEvent extends ProjectEventBase {
  type: SSEEventType.SESSION_STATUS_UPDATED;
  data: {
    sessionId: string;
    title: string;
    status: SessionStatus;
    updatedAt: string;
    recallBotStatus: RecallBotStatus | null;
    botId?: string;
    updateSource?: 'recall_callback';
    actualEndTime?: string;
    videoUrl?: string;
    resourceId?: string;
    resourceName?: string;
    transcriptionWordCount?: number;
  };
}

export interface SessionDeletedEvent extends ProjectEventBase {
  type: SSEEventType.SESSION_DELETED;
  data: {
    sessionId: string;
    title: string;
    deletedBy: string;
    deletedAt: string;
  };
}

export interface MemberAddedEvent extends ProjectEventBase {
  type: SSEEventType.ACL_MEMBER_NEW_MEMBER;
  data: {
    userId: string;
    userName: string;
    role: string;
    addedBy: string;
    addedAt: string;
  };
}

export interface MemberChangedRoleEvent extends ProjectEventBase {
  type: SSEEventType.ACL_MEMBER_CHANGE_ROLE;
  data: {
    userId: string;
    oldRole: string;
    newRole: string;
    updatedBy: string;
    updatedAt: string;
    userName: string;
    actionUserName: string;
  };
}
export interface MemberLeaveEvent extends ProjectEventBase {
  type: SSEEventType.ACL_MEMBER_LEAVE;
  data: {
    userId: string;
    userName: string;
    leftAt: string;
  };
}
export interface MemberAcceptInvitationEvent extends ProjectEventBase {
  type: SSEEventType.ACL_MEMBER_ACCEPT_INVITATION;
  data: {
    userId: string;
    userName: string;
  };
}
export interface MemberRemovedEvent extends ProjectEventBase {
  type: SSEEventType.ACL_MEMBER_REMOVED;
  data: {
    userId: string;
    userName: string;
  };
}

export interface ProjectDeletedEvent extends ProjectEventBase {
  type: SSEEventType.PROJECT_DELETED;
  data: {
    projectId: string;
    projectDefaultId: string;
    projectName: string;
    deletedBy: string;
    deletedAt: string;
  };
}

export interface NoteCreatedEvent extends ProjectEventBase {
  type: SSEEventType.NOTE_CREATED;
  data: Note & {
    createdById: string;
    createdBy: string;
    createdAt: string;
  };
}
export interface NoteUpdatedEvent extends ProjectEventBase {
  type: SSEEventType.NOTE_UPDATED;
  data: {
    id: string;
    title: string;
    content: string;
    resourceId: string;
    projectId: string;
    updatedBy: string;
    updatedAt: string;
    updatedById: string;
  };
}

export interface NoteDeletedEvent extends ProjectEventBase {
  type: SSEEventType.NOTE_DELETED;
  data: {
    id: string;
    deletedBy: string;
    deletedById: string;
    deletedAt: string;
  };
}

// Union type for all possible project events
export type ProjectEvent =
  | ResourceCreatedEvent
  | ResourceUpdatedEvent
  | ResourceDeletedEvent
  | SessionCreatedEvent
  | SessionStatusUpdatedEvent
  | SessionDeletedEvent
  | MemberAddedEvent
  | MemberChangedRoleEvent
  | MemberLeaveEvent
  | MemberAcceptInvitationEvent
  | MemberRemovedEvent
  | ProjectDeletedEvent;

// SSE Client configuration
export interface ProjectSSEClientConfig {
  projectId: string;
  onEvent?: (event: ProjectEvent) => void;
  onError?: (error: Event) => void;
  onReconnect?: () => void;
  maxReconnectAttempts?: number;
  reconnectDelay?: number;
}

// SSE Connection state
export interface SSEConnectionState {
  isConnected: boolean;
  isReconnecting: boolean;
  reconnectAttempts: number;
  lastError?: string;
  lastEventTimestamp?: string;
}
