import type { ProjectRole } from 'src/store/api/projects';

import type { Resource, ResourcePermission } from './resource';

// Enums for project-related constants
export enum FileType {
  VIDEO = 'video',
  AUDIO = 'audio',
  DOCUMENT = 'document',
  IMAGE = 'image',
  UNKNOWN = 'unknown',
}

export enum ProcessingStatus {
  COMPLETED = 'completed',
  PROCESSING = 'processing',
  FAILED = 'failed',
  PENDING = 'pending',
}

export enum SortBy {
  DATE_ADDED = 'dateAdded',
  NAME = 'name',
  FILE_SIZE = 'fileSize',
  LAST_MODIFIED = 'lastModified',
  PROCESSING_STATUS = 'processingStatus',
}

export enum SortOrder {
  ASC = 'asc',
  DESC = 'desc',
}

export interface Project {
  id: string;
  name: string;
  description: string;
  createdAt: Date;
  updatedAt: Date;
  totalFilesSize: number;
  totalFilesCount: number;
  userPermissions?: ResourcePermission;
  createdById: string;
  isDefault?: boolean;
  createdBy: ProjectUser;
}

export interface ProjectFolder {
  id: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
  projectId: string;
  createdById: string;
}

export interface ProjectUser {
  uid: string;
  displayName: string;
  email: string;
  photoURL: string;
  providerId: string;
}

export interface ProjectDetails extends Project {
  folders: ProjectFolder[];
  resources: Resource[];
  createdById: string;
}

export interface PaginationMeta {
  total: number;
  page: number;
  limit: number;
}

export interface PaginatedProjectDetails extends ProjectDetails {
  meta?: PaginationMeta;
}

export interface ProjectMember {
  id: string;
  projectId?: string;
  userId: string;
  role: ProjectRole;
  createdAt?: Date;
  user: ProjectUser;
}

export interface PendingInvitation {
  status: 'SENT';
  code: string;
  email: string;
  projectId: string;
  role: ProjectRole;
  expiredAt: Date;
  isExpired: boolean;
  canResend: boolean;
  userId: string | null;
  id: string;
  createdAt: Date;
  user?: ProjectUser;
}

export interface PublishedShareLink {
  token: string;
  projectId: string;
  expiredAt: Date;
  maxAccessCount: number;
}

export interface SharedLinkVerification {
  isMember: boolean;
}

export interface AccessRequest {
  projectId: string;
  sharedLinkId: string;
  requestedById: string;
  status: 'PENDING';
  approvedAt: Date | null;
  rejectedAt: Date | null;
  rejectedReason: string | null;
  expiredAt: Date;
  id: string;
  createdAt: Date;
  user: ProjectUser;
}

// Project Resources API types
export interface ProjectResourceItem {
  id: string;
  orgId?: string | null;
  name: string;
  url: string;
  transcodedUrl?: string | null;
  transcodedFileSize?: number | null;
  isTranscoding: boolean;
  fileSize: number;
  fileLastModified: string;
  sessionId?: string | null;
  duration: number;
  topics?: any | null;
  createdById: string;
  projectId: string;
  folderId?: string | null;
  originalResourceState?: any | null;
  thumbnailResourceId?: string | null;
  originalResourceId?: string | null;
  type: string;
  createdAt: string;
  fileName: string;
  transcription: any[];
  transcriptionJobStatus: string;
  userPermissions: {
    canView: boolean;
    canEdit: boolean;
    canComment: boolean;
  };
  isSpriteSheets?: boolean;
  vttSrc?: string;
  // Legacy fields for backward compatibility
  filename?: string;
  description?: string;
  fileType?: FileType;
  thumbnailUrl?: string;
  dateAdded?: string;
  processingStatus?: ProcessingStatus;
  lastModified?: string;
}

export interface PaginatedProjectResourcesResponse {
  page: number;
  limit: number;
  total: number;
  items: ProjectResourceItem[];
}
