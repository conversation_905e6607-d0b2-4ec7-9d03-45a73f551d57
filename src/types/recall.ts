export enum RecallBotStatus {
  NotStarted = 'NotStarted',
  JoiningCall = 'joining_call',
  InWaitingRoom = 'in_waiting_room',
  InCallNotRecording = 'in_call_not_recording',
  RecordingPermissionAllowed = 'recording_permission_allowed',
  RecordingPermissionDenied = 'recording_permission_denied',
  InCallRecording = 'in_call_recording',
  CallEnded = 'call_ended',
  Done = 'done',
  Fatal = 'fatal',
  AnalysisDone = 'analysis_done',
  AnalysisFailed = 'analysis_failed',
  MediaExpired = 'media_expired',
  TimeoutExceededWaitingRoom = "timeout_exceeded_waiting_room",
}
