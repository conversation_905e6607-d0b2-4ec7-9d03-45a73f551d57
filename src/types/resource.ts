import type { CancelTokenSource } from 'axios';
import type { GetResourceUploadUrlResponse } from 'src/store/api/resources';

import type { Transcription, TranscriptStatus } from './transcription';

export enum TranscodingMode {
  KEEP_BOTH = 'keep',
  DELETE_ORIGINAL = 'delete',
}

// File type literals for the generic file previewer
export type ResourceFileType = 'video' | 'image' | 'pdf' | 'document' | 'text' | 'audio' | 'word' | 'powerpoint' | 'markdown';

export interface ResourceTopic {
  topic_name: string;
  score: number;
}

export interface ResourcePermission {
  canView: boolean;
  canEdit: boolean;
  canComment: boolean;
}

export interface Resource {
  id: string;
  ffprobe: any;
  duration: number;
  thumbnailUrl: string;
  fileSize: number;
  fileName: string;
  name: string;
  createdById: string;
  url: string;
  createdAt: Date;
  fileLastModified: Date;
  type?: ResourceFileType; // Add type field for file previewer
  orgId?: string;
  sessionId?: string;
  topics?: ResourceTopic[];
  transcription?: Transcription[];
  transcriptionJobStatus?: TranscriptStatus;
  projectId?: string;
  folderId?: string;
  folder?: {
    id: string;
    name: string;
  };
  project?: {
    id: string;
    name: string;
  };
  userPermissions?: ResourcePermission;
  isTranscoding?: boolean;
  isSpriteSheets?: boolean;
  vttSrc?: string;
  transcodedUrl?: string;
  transcriptionSrc?: string;
}

export interface ResourceUploadQueueItem {
  id: string;
  file: File;
  uploadUrlData: GetResourceUploadUrlResponse;
  progress?: number;
  status?: 'pending' | 'uploading' | 'processing' | 'completed' | 'failed' | 'cancelled';
  submittedAt?: Date;
  cancelToken?: CancelTokenSource;
  projectId?: string;
  folderId?: string;
  transcoded?: boolean;
  mode?: 'delete-original' | 'keep-both';
}

// APIs (will be migrated to RTKQ later)
export interface CreateResourceRequest {
  fileName: string;
  uploadedFileName: string;
  fileSize: number;
}
