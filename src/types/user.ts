import type { Project, ProjectFolder } from './project';

export interface UserStatistics {
  id: string;
  userId: string;
  totalResourcesDuration: number;
  totalResourcesCount: number;
  totalTranscriptionsWordCount: number;
  createdAt: Date;
}

export interface UserProject
  extends Pick<Project, 'id' | 'name' | 'createdAt' | 'createdById' | 'isDefault' | 'createdBy'> {
  folders: Array<ProjectFolder>;
  isOwner: boolean;
}

export interface UserInitialContext {
  projects: Array<UserProject>;
}
