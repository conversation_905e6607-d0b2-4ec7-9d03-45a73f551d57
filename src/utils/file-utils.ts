/**
 * Utility functions for file handling and display
 */

/**
 * Extracts the file extension from a filename
 * @param filename - The filename to extract extension from
 * @returns The lowercase file extension without the dot, or empty string if no extension
 * @example
 * getFileExtension('file.tar.gz') // returns 'gz'
 * getFileExtension('README') // returns ''
 */
export function getFileExtension(filename: string): string {
  if (!filename || typeof filename !== 'string') {
    return '';
  }
  const lastDotIndex = filename.lastIndexOf('.');
  return lastDotIndex !== -1 ? filename.substring(lastDotIndex + 1).toLowerCase() : '';
}

/**
 * Removes the file extension from a filename
 * @param filename - The filename to remove extension from
 * @returns The filename without extension, or original filename if no extension
 * @example
 * removeFileExtension('file.tar.gz') // returns 'file.tar'
 * removeFileExtension('README') // returns 'README'
 */
export function removeFileExtension(filename: string): string {
  if (!filename || typeof filename !== 'string') {
    return filename;
  }
  const lastDotIndex = filename.lastIndexOf('.');
  return lastDotIndex !== -1 ? filename.substring(0, lastDotIndex) : filename;
}

/**
 * Gets the display name for a file, hiding certain extensions for better UX
 * Currently hides .md extensions as they are considered implementation details
 * @param filename - The filename to get display name for
 * @returns The display name for UI presentation
 * @example
 * getDisplayFileName('notes.md') // returns 'notes'
 * getDisplayFileName('README') // returns 'README'
 */
export function getDisplayFileName(filename: string): string {
  if (!filename || typeof filename !== 'string') {
    return filename;
  }

  const extension = getFileExtension(filename);

  // Hide .md extension in display as it's an implementation detail
  if (extension === 'md') {
    return removeFileExtension(filename);
  }

  return filename;
}

/**
 * Checks if a file has a specific extension (case-insensitive)
 * @param filename - The filename to check
 * @param extension - The extension to check for (without dot)
 * @returns True if the file has the specified extension
 * @example
 * hasFileExtension('notes.MD', 'md') // returns true
 * hasFileExtension('README', 'txt') // returns false
 */
export function hasFileExtension(filename: string, extension: string): boolean {
  return getFileExtension(filename) === extension.toLowerCase();
}
