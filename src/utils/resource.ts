import { toast } from 'sonner';
import { v4 as uuidv4 } from 'uuid';

import { useUploadQueueCacheSync } from 'src/hooks/use-upload-queue-cache-sync';

import { useGetResourceUploadUrlMutation } from 'src/store/api/resources';

export const useUploadFile = () => {
  const { addToUploadQueueWithCache } = useUploadQueueCacheSync();
  const [getUploadUrl] = useGetResourceUploadUrlMutation();

  const uploadFile = async (
    file: File,
    projectId?: string,
    folderId?: string,
    optional?: {
      transcoded?: boolean;
      mode: 'delete-original' | 'keep-both';
    }
  ) => {
    if (!file) return;

    try {
      const response = await getUploadUrl({
        payload: {
          fileName: file.name,
          projectId,
        },
      }).unwrap();

      await addToUploadQueueWithCache({
        id: uuidv4(),
        file,
        uploadUrlData: response,
        projectId,
        folderId,
        transcoded: optional?.transcoded,
        mode: optional?.mode,
      });
    } catch (error: any) {
      const statusCode = error?.data?.statusCode || error?.status;

      if (statusCode === 403) {
        toast.error('Permission denied', {
          description: "You don't have enough permissions to upload files to this project",
        });
        throw new Error('Permission denied');
      }

      toast.error('Something wrong! Unable to get upload url', {
        description: error?.data?.message || error.message || 'Unknown error occurred',
      });
      throw error;
    }
  };

  return uploadFile;
};
