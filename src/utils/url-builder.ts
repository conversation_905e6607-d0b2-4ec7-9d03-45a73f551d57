import { CONFIG } from 'src/global-config';

// ----------------------------------------------------------------------

interface BuildSSEUrlParams {
  projectId: string;
  token: string;
}

// ----------------------------------------------------------------------

/**
 * Builds the SSE (Server-Sent Events) URL for project events
 * @param projectId - The ID of the project to connect to
 * @param token - The authentication token
 * @returns The complete SSE URL
 */
export function buildProjectSSEUrl({ projectId, token }: BuildSSEUrlParams): string {
  const baseUrl = CONFIG.aidaApiUrl;
  return `${baseUrl}/sse/projects/${projectId}?token=${token}`;
}
