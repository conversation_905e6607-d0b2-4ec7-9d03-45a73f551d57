import path from 'path';
import { execSync } from 'child_process';
import checker from 'vite-plugin-checker';
import { defineConfig, normalizePath } from 'vite';
import react from '@vitejs/plugin-react-swc';
import { viteStaticCopy } from 'vite-plugin-static-copy';
import { sentryVitePlugin } from '@sentry/vite-plugin';

// ----------------------------------------------------------------------

const PORT = 3000;

// Get git SHA for release naming
const getGitSha = () => {
  try {
    return execSync('git rev-parse --short HEAD', { encoding: 'utf8' }).trim();
  } catch (error) {
    console.warn('Could not get git SHA:', error);
    return 'unknown';
  }
};

const cMapsDir = normalizePath(
  path.join(path.dirname(require.resolve('pdfjs-dist/package.json')), 'cmaps')
);
const standardFontsDir = normalizePath(
  path.join(path.dirname(require.resolve('pdfjs-dist/package.json')), 'standard_fonts')
);

export default defineConfig(({ mode }) => {
  const isProduction = mode === 'production';
  const shouldUploadSourceMaps = mode === 'production' || mode === 'staging';
  const gitSha = process.env.VITE_GIT_SHA || getGitSha();

  return {
    envDir: './env',
    define: {
      'import.meta.env.VITE_GIT_SHA': JSON.stringify(gitSha),
    },
    plugins: [
      viteStaticCopy({
        targets: [
          { src: cMapsDir, dest: '' },
          { src: standardFontsDir, dest: '' },
        ],
      }),
      react(),
      checker({
        typescript: true,
        eslint: {
          useFlatConfig: true,
          lintCommand: 'eslint "./src/**/*.{js,jsx,ts,tsx}"',
          dev: { logLevel: ['error'] },
        },
        overlay: {
          position: 'tl',
          initialIsOpen: false,
        },
      }),
      // Add Sentry plugin for production and staging builds
      ...(shouldUploadSourceMaps
        ? [
            sentryVitePlugin({
              // These will be read from .sentryclirc if not provided as env vars
              org: process.env.SENTRY_ORG || 'beings',
              project: process.env.SENTRY_PROJECT || 'aida',
              authToken: process.env.SENTRY_AUTH_TOKEN, // Will fallback to .sentryclirc

              // Configuration for uploading source maps
              sourcemaps: {
                assets: './dist/**',
                ignore: ['node_modules'],
                filesToDeleteAfterUpload: './dist/**/*.map',
              },

              // Configuration for creating releases
              release: {
                name: process.env.SENTRY_RELEASE || `aida-${mode}-${gitSha}`,
                deploy: {
                  env: mode,
                },
              },

              // Don't upload source maps in development
              disable: !shouldUploadSourceMaps,

              // Suppress CLI output
              silent: false,

              // Debug mode (set to true if you want verbose output)
              debug: false,
            }),
          ]
        : []),
    ],
    resolve: {
      alias: [
        {
          find: /^~(.+)/,
          replacement: path.resolve(process.cwd(), 'node_modules/$1'),
        },
        {
          find: /^src(.+)/,
          replacement: path.resolve(process.cwd(), 'src/$1'),
        },
      ],
    },

    server: { port: PORT, host: true },
    preview: { port: PORT, host: true },
    build: {
      sourcemap: true,
    },
  };
});
